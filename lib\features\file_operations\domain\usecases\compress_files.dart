import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/file_operation.dart';
import '../repositories/file_operations_repository.dart';

@lazySingleton
class CompressFiles {
  final FileOperationsRepository repository;

  const CompressFiles(this.repository);

  ResultFuture<String> call({
    required List<String> sourcePaths,
    required String destinationPath,
    required CompressionOptions options,
  }) async {
    return await repository.compressFiles(sourcePaths, destinationPath, options);
  }
}
