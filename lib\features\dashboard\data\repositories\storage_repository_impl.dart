import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/storage_info.dart';
import '../../domain/repositories/storage_repository.dart';
import '../datasources/storage_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: StorageRepository)
class StorageRepositoryImpl implements StorageRepository {
  final StorageDataSource dataSource;

  const StorageRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, List<StorageInfo>>> getStorageInfo() async {
    try {
      final result = await dataSource.getStorageInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, StorageInfo>> getInternalStorageInfo() async {
    try {
      final result = await dataSource.getInternalStorageInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<StorageInfo>>> getExternalStorageInfo() async {
    try {
      final result = await dataSource.getExternalStorageInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getDirectorySize(String path) async {
    try {
      final result = await dataSource.getDirectorySize(path);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, int>>> getCategorizedStorageUsage() async {
    try {
      final result = await dataSource.getCategorizedStorageUsage();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
