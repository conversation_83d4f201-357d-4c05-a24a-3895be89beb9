import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/file_item.dart';
import '../repositories/file_repository.dart';

@lazySingleton
class GetDirectoryContents {
  final FileRepository repository;

  const GetDirectoryContents(this.repository);

  ResultFuture<List<FileItem>> call(String path) async {
    return await repository.getDirectoryContents(path);
  }
}
