import 'dart:convert';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';

import '../models/language.dart';

/// خدمة الترجمة
@singleton
class TranslationService {
  static const String _translationsPath = 'assets/translations';
  
  Map<String, dynamic> _translations = {};
  Language _currentLanguage = SupportedLanguages.defaultLanguage;

  /// اللغة الحالية
  Language get currentLanguage => _currentLanguage;

  /// الترجمات الحالية
  Map<String, dynamic> get translations => _translations;

  /// تحميل الترجمات للغة معينة
  Future<void> loadTranslations(Language language) async {
    try {
      final String translationFile = '$_translationsPath/${language.code}.json';
      final String jsonString = await rootBundle.loadString(translationFile);
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      
      _translations = jsonMap;
      _currentLanguage = language;
    } catch (e) {
      // في حالة فشل التحميل، استخدام الترجمات الافتراضية
      if (language.code != SupportedLanguages.defaultLanguage.code) {
        await loadTranslations(SupportedLanguages.defaultLanguage);
      } else {
        _translations = {};
      }
    }
  }

  /// الحصول على ترجمة بالمفتاح
  String translate(String key, {Map<String, dynamic>? params}) {
    final translation = _getNestedTranslation(key);
    
    if (translation == null) {
      return key; // إرجاع المفتاح إذا لم توجد الترجمة
    }

    // استبدال المعاملات إذا وجدت
    if (params != null && params.isNotEmpty) {
      return _replaceParams(translation, params);
    }

    return translation;
  }

  /// الحصول على ترجمة متداخلة
  String? _getNestedTranslation(String key) {
    final keys = key.split('.');
    dynamic current = _translations;

    for (final k in keys) {
      if (current is Map<String, dynamic> && current.containsKey(k)) {
        current = current[k];
      } else {
        return null;
      }
    }

    return current is String ? current : null;
  }

  /// استبدال المعاملات في النص
  String _replaceParams(String text, Map<String, dynamic> params) {
    String result = text;
    
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });

    return result;
  }

  /// الحصول على ترجمة مع صيغة الجمع
  String translatePlural(
    String key,
    int count, {
    Map<String, dynamic>? params,
  }) {
    final Map<String, dynamic> mergedParams = {
      'count': count,
      ...?params,
    };

    // محاولة الحصول على صيغة الجمع المناسبة
    String pluralKey;
    if (count == 0) {
      pluralKey = '${key}_zero';
    } else if (count == 1) {
      pluralKey = '${key}_one';
    } else if (count == 2) {
      pluralKey = '${key}_two';
    } else if (count >= 3 && count <= 10) {
      pluralKey = '${key}_few';
    } else {
      pluralKey = '${key}_many';
    }

    // محاولة الحصول على الترجمة المناسبة
    String? translation = _getNestedTranslation(pluralKey);
    
    // إذا لم توجد، استخدام الترجمة الأساسية
    if (translation == null) {
      translation = _getNestedTranslation(key);
    }

    if (translation == null) {
      return key;
    }

    return _replaceParams(translation, mergedParams);
  }

  /// التحقق من وجود ترجمة
  bool hasTranslation(String key) {
    return _getNestedTranslation(key) != null;
  }

  /// الحصول على جميع الترجمات لمجموعة معينة
  Map<String, dynamic>? getTranslationGroup(String groupKey) {
    final keys = groupKey.split('.');
    dynamic current = _translations;

    for (final k in keys) {
      if (current is Map<String, dynamic> && current.containsKey(k)) {
        current = current[k];
      } else {
        return null;
      }
    }

    return current is Map<String, dynamic> ? current : null;
  }

  /// الحصول على قائمة بجميع المفاتيح
  List<String> getAllKeys({String prefix = ''}) {
    final List<String> keys = [];
    _collectKeys(_translations, keys, prefix);
    return keys;
  }

  /// جمع المفاتيح بشكل تكراري
  void _collectKeys(
    Map<String, dynamic> map,
    List<String> keys,
    String prefix,
  ) {
    map.forEach((key, value) {
      final fullKey = prefix.isEmpty ? key : '$prefix.$key';
      
      if (value is Map<String, dynamic>) {
        _collectKeys(value, keys, fullKey);
      } else if (value is String) {
        keys.add(fullKey);
      }
    });
  }

  /// تحديث ترجمة معينة (للاختبار أو التخصيص)
  void updateTranslation(String key, String value) {
    final keys = key.split('.');
    Map<String, dynamic> current = _translations;

    for (int i = 0; i < keys.length - 1; i++) {
      final k = keys[i];
      if (!current.containsKey(k) || current[k] is! Map<String, dynamic>) {
        current[k] = <String, dynamic>{};
      }
      current = current[k] as Map<String, dynamic>;
    }

    current[keys.last] = value;
  }

  /// مسح جميع الترجمات
  void clearTranslations() {
    _translations.clear();
  }

  /// إعادة تحميل الترجمات
  Future<void> reloadTranslations() async {
    await loadTranslations(_currentLanguage);
  }

  /// الحصول على معلومات الترجمة
  Map<String, dynamic> getTranslationInfo() {
    return {
      'language': _currentLanguage.toJson(),
      'translationCount': getAllKeys().length,
      'groups': _getTopLevelGroups(),
      'isRTL': _currentLanguage.isRTL,
    };
  }

  /// الحصول على المجموعات الرئيسية
  List<String> _getTopLevelGroups() {
    return _translations.keys.toList();
  }

  /// تصدير الترجمات
  String exportTranslations() {
    return json.encode(_translations);
  }

  /// استيراد الترجمات
  void importTranslations(String jsonString) {
    try {
      final Map<String, dynamic> imported = json.decode(jsonString);
      _translations = imported;
    } catch (e) {
      throw Exception('Failed to import translations: $e');
    }
  }

  /// دمج الترجمات
  void mergeTranslations(Map<String, dynamic> newTranslations) {
    _mergeRecursive(_translations, newTranslations);
  }

  /// دمج تكراري للخرائط
  void _mergeRecursive(
    Map<String, dynamic> target,
    Map<String, dynamic> source,
  ) {
    source.forEach((key, value) {
      if (value is Map<String, dynamic> && target[key] is Map<String, dynamic>) {
        _mergeRecursive(target[key] as Map<String, dynamic>, value);
      } else {
        target[key] = value;
      }
    });
  }

  /// البحث في الترجمات
  List<String> searchTranslations(String query) {
    final List<String> results = [];
    final allKeys = getAllKeys();

    for (final key in allKeys) {
      final translation = translate(key);
      if (translation.toLowerCase().contains(query.toLowerCase()) ||
          key.toLowerCase().contains(query.toLowerCase())) {
        results.add(key);
      }
    }

    return results;
  }

  /// التحقق من اكتمال الترجمات
  Map<String, dynamic> validateTranslations(Language referenceLanguage) {
    // هذه الطريقة يمكن استخدامها للتحقق من اكتمال الترجمات
    // مقارنة بلغة مرجعية (عادة الإنجليزية)
    return {
      'isComplete': true, // يمكن تطوير هذا لاحقاً
      'missingKeys': <String>[],
      'extraKeys': <String>[],
    };
  }
}
