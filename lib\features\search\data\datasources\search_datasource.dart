import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

import '../models/search_result_model.dart';
import '../../domain/entities/search_filter.dart';
import '../../../file_browser/data/models/file_item_model.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/utils/file_utils.dart';
import '../../../../core/constants/app_constants.dart';

abstract class SearchDataSource {
  Future<SearchResultModel> searchFiles(SearchFilter filter);
  Stream<SearchResultModel> searchFilesStream(SearchFilter filter);
  Future<void> cancelSearch();
  
  Future<DuplicateSearchResultModel> findDuplicates(DuplicateSearchFilter filter);
  Stream<DuplicateSearchResultModel> findDuplicatesStream(DuplicateSearchFilter filter);
  Future<void> cancelDuplicateSearch();
  
  Future<List<SearchFilter>> getSearchHistory();
  Future<void> saveSearchToHistory(SearchFilter filter);
  Future<void> clearSearchHistory();
  
  Future<List<String>> getSearchSuggestions(String query);
  Future<SearchResultModel> quickSearch(String query);
}

@LazySingleton(as: SearchDataSource)
class SearchDataSourceImpl implements SearchDataSource {
  final Uuid _uuid = const Uuid();
  StreamController<SearchResultModel>? _searchController;
  StreamController<DuplicateSearchResultModel>? _duplicateController;
  bool _isSearchCancelled = false;
  bool _isDuplicateSearchCancelled = false;

  @override
  Future<SearchResultModel> searchFiles(SearchFilter filter) async {
    try {
      _isSearchCancelled = false;
      final startTime = DateTime.now();
      final results = <FileItemModel>[];
      
      final searchPaths = filter.searchPaths ?? ['/storage/emulated/0'];
      
      for (final searchPath in searchPaths) {
        if (_isSearchCancelled) break;
        
        await _searchInDirectory(
          searchPath,
          filter,
          results,
          0,
          _getMaxDepth(filter.depth),
        );
      }
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      return SearchResultModel(
        files: results,
        query: filter.query ?? '',
        filter: filter,
        totalResults: results.length,
        searchDuration: duration,
        isComplete: !_isSearchCancelled,
      );
    } catch (e) {
      throw ServerException(message: 'Search failed: $e');
    }
  }

  @override
  Stream<SearchResultModel> searchFilesStream(SearchFilter filter) async* {
    _searchController = StreamController<SearchResultModel>();
    _isSearchCancelled = false;
    
    try {
      final startTime = DateTime.now();
      final results = <FileItemModel>[];
      final searchPaths = filter.searchPaths ?? ['/storage/emulated/0'];
      
      for (final searchPath in searchPaths) {
        if (_isSearchCancelled) break;
        
        await _searchInDirectoryStream(
          searchPath,
          filter,
          results,
          0,
          _getMaxDepth(filter.depth),
          startTime,
        );
      }
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      yield SearchResultModel(
        files: results,
        query: filter.query ?? '',
        filter: filter,
        totalResults: results.length,
        searchDuration: duration,
        isComplete: !_isSearchCancelled,
      );
    } catch (e) {
      yield SearchResultModel(
        files: const [],
        query: filter.query ?? '',
        filter: filter,
        totalResults: 0,
        searchDuration: Duration.zero,
        isComplete: false,
        errorMessage: 'Search failed: $e',
      );
    } finally {
      _searchController?.close();
      _searchController = null;
    }
  }

  Future<void> _searchInDirectory(
    String dirPath,
    SearchFilter filter,
    List<FileItemModel> results,
    int currentDepth,
    int maxDepth,
  ) async {
    if (_isSearchCancelled || (maxDepth >= 0 && currentDepth > maxDepth)) {
      return;
    }

    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      final entities = await directory.list().toList();
      
      for (final entity in entities) {
        if (_isSearchCancelled) break;
        
        try {
          final fileItem = FileItemModel.fromFileSystemEntity(entity);
          
          if (_matchesFilter(fileItem, filter)) {
            results.add(fileItem);
            
            if (results.length >= AppConstants.maxSearchResults) {
              _isSearchCancelled = true;
              break;
            }
          }
          
          // Recurse into subdirectories
          if (entity is Directory && (maxDepth < 0 || currentDepth < maxDepth)) {
            await _searchInDirectory(
              entity.path,
              filter,
              results,
              currentDepth + 1,
              maxDepth,
            );
          }
        } catch (e) {
          // Skip files that can't be accessed
          continue;
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  Future<void> _searchInDirectoryStream(
    String dirPath,
    SearchFilter filter,
    List<FileItemModel> results,
    int currentDepth,
    int maxDepth,
    DateTime startTime,
  ) async {
    if (_isSearchCancelled || (maxDepth >= 0 && currentDepth > maxDepth)) {
      return;
    }

    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      final entities = await directory.list().toList();
      
      for (final entity in entities) {
        if (_isSearchCancelled) break;
        
        try {
          final fileItem = FileItemModel.fromFileSystemEntity(entity);
          
          if (_matchesFilter(fileItem, filter)) {
            results.add(fileItem);
            
            // Emit progress every 10 results
            if (results.length % 10 == 0) {
              final currentTime = DateTime.now();
              final duration = currentTime.difference(startTime);
              
              _searchController?.add(SearchResultModel(
                files: List.from(results),
                query: filter.query ?? '',
                filter: filter,
                totalResults: results.length,
                searchDuration: duration,
                isComplete: false,
              ));
            }
            
            if (results.length >= AppConstants.maxSearchResults) {
              _isSearchCancelled = true;
              break;
            }
          }
          
          // Recurse into subdirectories
          if (entity is Directory && (maxDepth < 0 || currentDepth < maxDepth)) {
            await _searchInDirectoryStream(
              entity.path,
              filter,
              results,
              currentDepth + 1,
              maxDepth,
              startTime,
            );
          }
        } catch (e) {
          // Skip files that can't be accessed
          continue;
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  bool _matchesFilter(FileItemModel fileItem, SearchFilter filter) {
    // Query matching
    if (filter.query != null && filter.query!.isNotEmpty) {
      final query = filter.caseSensitive ? filter.query! : filter.query!.toLowerCase();
      final fileName = filter.caseSensitive ? fileItem.name : fileItem.name.toLowerCase();
      
      if (filter.useRegex) {
        try {
          final regex = RegExp(query, caseSensitive: filter.caseSensitive);
          if (!regex.hasMatch(fileName)) return false;
        } catch (e) {
          // Invalid regex, fall back to contains
          if (!fileName.contains(query)) return false;
        }
      } else {
        if (!fileName.contains(query)) return false;
      }
    }
    
    // File type filtering
    if (filter.fileTypes.isNotEmpty && !filter.fileTypes.contains(SearchFileType.all)) {
      bool matchesType = false;
      for (final type in filter.fileTypes) {
        if (_matchesFileType(fileItem, type)) {
          matchesType = true;
          break;
        }
      }
      if (!matchesType) return false;
    }
    
    // Size filtering
    if (filter.minSize != null && fileItem.size < filter.minSize!) return false;
    if (filter.maxSize != null && fileItem.size > filter.maxSize!) return false;
    
    // Date filtering
    if (filter.modifiedAfter != null && fileItem.modifiedDate.isBefore(filter.modifiedAfter!)) return false;
    if (filter.modifiedBefore != null && fileItem.modifiedDate.isAfter(filter.modifiedBefore!)) return false;
    
    // Hidden files
    if (!filter.includeHidden && fileItem.isHidden) return false;
    
    // System files
    if (!filter.includeSystemFiles && FileUtils.isSystemFile(fileItem.path)) return false;
    
    // Extensions
    if (filter.extensions != null && filter.extensions!.isNotEmpty) {
      final extension = fileItem.extension?.toLowerCase();
      if (extension == null || !filter.extensions!.contains(extension)) return false;
    }
    
    return true;
  }

  bool _matchesFileType(FileItemModel fileItem, SearchFileType type) {
    switch (type) {
      case SearchFileType.all:
        return true;
      case SearchFileType.images:
        return fileItem.fileType == FileType.image;
      case SearchFileType.videos:
        return fileItem.fileType == FileType.video;
      case SearchFileType.audio:
        return fileItem.fileType == FileType.audio;
      case SearchFileType.documents:
        return fileItem.fileType == FileType.document;
      case SearchFileType.archives:
        return fileItem.fileType == FileType.archive;
      case SearchFileType.applications:
        return fileItem.fileType == FileType.apk;
      case SearchFileType.folders:
        return fileItem.isDirectory;
    }
  }

  int _getMaxDepth(SearchDepth depth) {
    switch (depth) {
      case SearchDepth.currentFolder:
        return 0;
      case SearchDepth.oneLevel:
        return 1;
      case SearchDepth.twoLevels:
        return 2;
      case SearchDepth.unlimited:
        return -1;
    }
  }

  @override
  Future<void> cancelSearch() async {
    _isSearchCancelled = true;
    _searchController?.close();
    _searchController = null;
  }

  @override
  Future<DuplicateSearchResultModel> findDuplicates(DuplicateSearchFilter filter) async {
    try {
      _isDuplicateSearchCancelled = false;
      final startTime = DateTime.now();
      
      final duplicateGroups = await _findDuplicateGroups(filter);
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      final totalDuplicates = duplicateGroups.fold<int>(
        0, 
        (sum, group) => sum + group.duplicateCount,
      );
      
      final totalWastedSpace = duplicateGroups.fold<int>(
        0, 
        (sum, group) => sum + group.wastedSpace,
      );
      
      return DuplicateSearchResultModel(
        duplicateGroups: duplicateGroups,
        totalDuplicates: totalDuplicates,
        totalWastedSpace: totalWastedSpace,
        searchDuration: duration,
        filter: filter,
        isComplete: !_isDuplicateSearchCancelled,
      );
    } catch (e) {
      throw ServerException(message: 'Duplicate search failed: $e');
    }
  }

  @override
  Stream<DuplicateSearchResultModel> findDuplicatesStream(DuplicateSearchFilter filter) async* {
    _duplicateController = StreamController<DuplicateSearchResultModel>();
    _isDuplicateSearchCancelled = false;
    
    try {
      final startTime = DateTime.now();
      final duplicateGroups = await _findDuplicateGroups(filter);
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      final totalDuplicates = duplicateGroups.fold<int>(
        0, 
        (sum, group) => sum + group.duplicateCount,
      );
      
      final totalWastedSpace = duplicateGroups.fold<int>(
        0, 
        (sum, group) => sum + group.wastedSpace,
      );
      
      yield DuplicateSearchResultModel(
        duplicateGroups: duplicateGroups,
        totalDuplicates: totalDuplicates,
        totalWastedSpace: totalWastedSpace,
        searchDuration: duration,
        filter: filter,
        isComplete: !_isDuplicateSearchCancelled,
      );
    } catch (e) {
      yield DuplicateSearchResultModel(
        duplicateGroups: const [],
        totalDuplicates: 0,
        totalWastedSpace: 0,
        searchDuration: Duration.zero,
        filter: filter,
        isComplete: false,
        errorMessage: 'Duplicate search failed: $e',
      );
    } finally {
      _duplicateController?.close();
      _duplicateController = null;
    }
  }

  Future<List<DuplicateGroupModel>> _findDuplicateGroups(DuplicateSearchFilter filter) async {
    final allFiles = <FileItemModel>[];
    
    // Collect all files from search paths
    for (final searchPath in filter.searchPaths) {
      if (_isDuplicateSearchCancelled) break;
      await _collectFilesForDuplicateSearch(searchPath, filter, allFiles);
    }
    
    // Group files by the selected method
    final groups = <String, List<FileItemModel>>{};
    
    for (final file in allFiles) {
      if (_isDuplicateSearchCancelled) break;
      
      final key = await _getDuplicateKey(file, filter.method);
      if (key != null) {
        groups.putIfAbsent(key, () => []).add(file);
      }
    }
    
    // Filter groups with duplicates and create DuplicateGroup objects
    final duplicateGroups = <DuplicateGroupModel>[];
    
    for (final entry in groups.entries) {
      if (entry.value.length > 1) {
        final totalSize = entry.value.first.size;
        duplicateGroups.add(DuplicateGroupModel(
          id: _uuid.v4(),
          duplicates: entry.value,
          totalSize: totalSize,
          hash: filter.method == DuplicateSearchMethod.contentHash ? entry.key : null,
          detectionMethod: filter.method,
        ));
      }
    }
    
    return duplicateGroups;
  }

  Future<void> _collectFilesForDuplicateSearch(
    String dirPath,
    DuplicateSearchFilter filter,
    List<FileItemModel> files,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      await for (final entity in directory.list(recursive: true)) {
        if (_isDuplicateSearchCancelled) break;
        
        if (entity is File) {
          try {
            final fileItem = FileItemModel.fromFileSystemEntity(entity);
            
            // Apply filters
            if (filter.minFileSize != null && fileItem.size < filter.minFileSize!) continue;
            if (!filter.includeHidden && fileItem.isHidden) continue;
            if (!filter.includeSystemFiles && FileUtils.isSystemFile(fileItem.path)) continue;
            
            // File type filtering
            if (filter.fileTypes.isNotEmpty && !filter.fileTypes.contains(SearchFileType.all)) {
              bool matchesType = false;
              for (final type in filter.fileTypes) {
                if (_matchesFileType(fileItem, type)) {
                  matchesType = true;
                  break;
                }
              }
              if (!matchesType) continue;
            }
            
            files.add(fileItem);
          } catch (e) {
            // Skip files that can't be accessed
            continue;
          }
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  Future<String?> _getDuplicateKey(FileItemModel file, DuplicateSearchMethod method) async {
    switch (method) {
      case DuplicateSearchMethod.nameOnly:
        return file.name.toLowerCase();
      
      case DuplicateSearchMethod.nameAndSize:
        return '${file.name.toLowerCase()}_${file.size}';
      
      case DuplicateSearchMethod.contentHash:
        return await _calculateFileHash(file.path);
      
      case DuplicateSearchMethod.fuzzyName:
        return _getFuzzyName(file.name);
    }
  }

  Future<String?> _calculateFileHash(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      return null;
    }
  }

  String _getFuzzyName(String fileName) {
    // Remove common suffixes and normalize
    return fileName
        .toLowerCase()
        .replaceAll(RegExp(r'\s+'), '')
        .replaceAll(RegExp(r'[^\w]'), '')
        .replaceAll(RegExp(r'copy|duplicate|\d+$'), '');
  }

  @override
  Future<void> cancelDuplicateSearch() async {
    _isDuplicateSearchCancelled = true;
    _duplicateController?.close();
    _duplicateController = null;
  }

  @override
  Future<List<SearchFilter>> getSearchHistory() async {
    // TODO: Implement search history persistence
    return [];
  }

  @override
  Future<void> saveSearchToHistory(SearchFilter filter) async {
    // TODO: Implement search history persistence
  }

  @override
  Future<void> clearSearchHistory() async {
    // TODO: Implement search history persistence
  }

  @override
  Future<List<String>> getSearchSuggestions(String query) async {
    // TODO: Implement search suggestions
    return [];
  }

  @override
  Future<SearchResultModel> quickSearch(String query) async {
    final filter = SearchFilter(
      query: query,
      searchPaths: ['/storage/emulated/0'],
      depth: SearchDepth.twoLevels,
    );
    
    return await searchFiles(filter);
  }
}
