import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/file_item.dart';
import '../bloc/file_browser_bloc.dart';
import '../bloc/file_browser_event.dart';
import '../../../../core/utils/file_utils.dart';
import '../../../../core/constants/app_constants.dart';

class FileGridView extends StatelessWidget {
  final List<FileItem> files;

  const FileGridView({
    super.key,
    required this.files,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.0,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
      ),
      itemCount: files.length,
      itemBuilder: (context, index) {
        final file = files[index];
        return FileGridItem(
          file: file,
          onTap: () => _handleFileTap(context, file),
          onLongPress: () => _showFileOptions(context, file),
        );
      },
    );
  }

  void _handleFileTap(BuildContext context, FileItem file) {
    if (file.isDirectory) {
      context.read<FileBrowserBloc>().add(NavigateToDirectoryEvent(file.path));
    } else {
      // Open file with appropriate app
      _openFile(context, file);
    }
  }

  void _openFile(BuildContext context, FileItem file) {
    // TODO: Implement file opening logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${file.name}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showFileOptions(BuildContext context, FileItem file) {
    showModalBottomSheet(
      context: context,
      builder: (context) => FileOptionsBottomSheet(file: file),
    );
  }
}

class FileGridItem extends StatelessWidget {
  final FileItem file;
  final VoidCallback onTap;
  final VoidCallback onLongPress;

  const FileGridItem({
    super.key,
    required this.file,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // File Icon
              _buildFileIcon(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // File Name
              Text(
                file.name,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: file.isDirectory ? FontWeight.bold : FontWeight.normal,
                  color: file.isHidden ? Colors.grey[600] : null,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // File Info
              Text(
                _getFileInfo(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              
              // Indicators
              if (file.isHidden || !file.permissions.writable)
                Padding(
                  padding: const EdgeInsets.only(top: AppConstants.smallPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (file.isHidden)
                        Icon(
                          Icons.visibility_off,
                          size: 16,
                          color: Colors.grey[500],
                        ),
                      if (file.isHidden && !file.permissions.writable)
                        const SizedBox(width: AppConstants.smallPadding),
                      if (!file.permissions.writable)
                        Icon(
                          Icons.lock,
                          size: 16,
                          color: Colors.orange[600],
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileIcon() {
    if (file.isDirectory) {
      return Icon(
        Icons.folder,
        color: Colors.blue[600],
        size: 48,
      );
    }

    // File icon based on type
    IconData iconData;
    Color iconColor;

    if (file.fileType != null) {
      switch (file.fileType!) {
        case FileType.image:
          iconData = Icons.image;
          iconColor = Colors.green[600]!;
          break;
        case FileType.video:
          iconData = Icons.video_file;
          iconColor = Colors.red[600]!;
          break;
        case FileType.audio:
          iconData = Icons.audio_file;
          iconColor = Colors.orange[600]!;
          break;
        case FileType.document:
          iconData = Icons.description;
          iconColor = Colors.blue[600]!;
          break;
        case FileType.archive:
          iconData = Icons.archive;
          iconColor = Colors.purple[600]!;
          break;
        case FileType.apk:
          iconData = Icons.android;
          iconColor = Colors.green[700]!;
          break;
        case FileType.other:
          iconData = Icons.insert_drive_file;
          iconColor = Colors.grey[600]!;
          break;
      }
    } else {
      iconData = Icons.insert_drive_file;
      iconColor = Colors.grey[600]!;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 48,
    );
  }

  String _getFileInfo() {
    if (file.isDirectory) {
      return 'Folder';
    } else {
      return FileUtils.formatFileSize(file.size);
    }
  }
}

// Reuse the FileOptionsBottomSheet from file_list_view.dart
class FileOptionsBottomSheet extends StatelessWidget {
  final FileItem file;

  const FileOptionsBottomSheet({
    super.key,
    required this.file,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File info header
          Row(
            children: [
              Icon(
                file.isDirectory ? Icons.folder : Icons.insert_drive_file,
                size: 32,
                color: file.isDirectory ? Colors.blue : Colors.grey[600],
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      file.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      file.path,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          const Divider(),
          
          // Options
          ListTile(
            leading: const Icon(Icons.open_in_new),
            title: Text(file.isDirectory ? 'Open' : 'Open with'),
            onTap: () {
              Navigator.pop(context);
              if (file.isDirectory) {
                context.read<FileBrowserBloc>().add(NavigateToDirectoryEvent(file.path));
              }
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Show rename dialog
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              context.read<FileBrowserBloc>().add(DeleteFileEvent(file.path));
            },
          ),
        ],
      ),
    );
  }
}
