import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../domain/usecases/search_files.dart';
import '../../domain/usecases/find_duplicates.dart';
import '../../domain/repositories/search_repository.dart';
import 'search_event.dart';
import 'search_state.dart';

@injectable
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final SearchFiles _searchFiles;
  final FindDuplicates _findDuplicates;
  final SearchRepository _repository;
  
  StreamSubscription? _searchSubscription;
  StreamSubscription? _duplicateSubscription;

  SearchBloc(
    this._searchFiles,
    this._findDuplicates,
    this._repository,
  ) : super(const SearchInitial()) {
    on<StartSearchEvent>(_onStartSearch);
    on<QuickSearchEvent>(_onQuickSearch);
    on<CancelSearchEvent>(_onCancelSearch);
    on<StartDuplicateSearchEvent>(_onStartDuplicateSearch);
    on<CancelDuplicateSearchEvent>(_onCancelDuplicateSearch);
    on<LoadSearchHistoryEvent>(_onLoadSearchHistory);
    on<SaveSearchToHistoryEvent>(_onSaveSearchToHistory);
    on<ClearSearchHistoryEvent>(_onClearSearchHistory);
    on<GetSearchSuggestionsEvent>(_onGetSearchSuggestions);
    on<UpdateSearchFilterEvent>(_onUpdateSearchFilter);
    on<DeleteDuplicateFilesEvent>(_onDeleteDuplicateFiles);
  }

  @override
  Future<void> close() {
    _searchSubscription?.cancel();
    _duplicateSubscription?.cancel();
    return super.close();
  }

  Future<void> _onStartSearch(
    StartSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchLoading());
    
    await _searchSubscription?.cancel();
    
    _searchSubscription = _searchFiles.stream(event.filter).listen(
      (result) {
        if (result.isComplete) {
          emit(SearchCompleted(result));
        } else {
          emit(SearchInProgress(result));
        }
      },
      onError: (error) {
        emit(SearchError('Search failed: $error'));
      },
    );
  }

  Future<void> _onQuickSearch(
    QuickSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchLoading());

    final result = await _repository.quickSearch(event.query);
    
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (searchResult) => emit(SearchCompleted(searchResult)),
    );
  }

  Future<void> _onCancelSearch(
    CancelSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    await _searchSubscription?.cancel();
    _searchSubscription = null;
    
    final result = await _repository.cancelSearch();
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (_) => emit(const SearchInitial()),
    );
  }

  Future<void> _onStartDuplicateSearch(
    StartDuplicateSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(const DuplicateSearchLoading());
    
    await _duplicateSubscription?.cancel();
    
    _duplicateSubscription = _findDuplicates.stream(event.filter).listen(
      (result) {
        if (result.isComplete) {
          emit(DuplicateSearchCompleted(result));
        } else {
          emit(DuplicateSearchInProgress(result));
        }
      },
      onError: (error) {
        emit(DuplicateSearchError('Duplicate search failed: $error'));
      },
    );
  }

  Future<void> _onCancelDuplicateSearch(
    CancelDuplicateSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    await _duplicateSubscription?.cancel();
    _duplicateSubscription = null;
    
    final result = await _repository.cancelDuplicateSearch();
    result.fold(
      (failure) => emit(DuplicateSearchError(failure.message)),
      (_) => emit(const SearchInitial()),
    );
  }

  Future<void> _onLoadSearchHistory(
    LoadSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    final result = await _repository.getSearchHistory();
    
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (history) => emit(SearchHistoryLoaded(history)),
    );
  }

  Future<void> _onSaveSearchToHistory(
    SaveSearchToHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    final result = await _repository.saveSearchToHistory(event.filter);
    
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (_) {
        // Optionally reload history
        add(const LoadSearchHistoryEvent());
      },
    );
  }

  Future<void> _onClearSearchHistory(
    ClearSearchHistoryEvent event,
    Emitter<SearchState> emit,
  ) async {
    final result = await _repository.clearSearchHistory();
    
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (_) => emit(const SearchHistoryLoaded([])),
    );
  }

  Future<void> _onGetSearchSuggestions(
    GetSearchSuggestionsEvent event,
    Emitter<SearchState> emit,
  ) async {
    final result = await _repository.getSearchSuggestions(event.query);
    
    result.fold(
      (failure) => emit(SearchError(failure.message)),
      (suggestions) => emit(SearchSuggestionsLoaded(suggestions)),
    );
  }

  void _onUpdateSearchFilter(
    UpdateSearchFilterEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(SearchFilterUpdated(event.filter));
  }

  Future<void> _onDeleteDuplicateFiles(
    DeleteDuplicateFilesEvent event,
    Emitter<SearchState> emit,
  ) async {
    try {
      // TODO: Implement file deletion through file operations
      // For now, just emit success
      emit(DuplicateFilesDeleted(
        groupId: event.groupId,
        deletedFiles: event.filesToDelete,
      ));
    } catch (e) {
      emit(SearchError('Failed to delete files: $e'));
    }
  }
}
