import 'package:equatable/equatable.dart';
import '../../domain/entities/app_info.dart';
import 'app_manager_event.dart';

abstract class AppManagerState extends Equatable {
  const AppManagerState();

  @override
  List<Object?> get props => [];
}

class AppManagerInitial extends AppManagerState {
  const AppManagerInitial();
}

class AppManagerLoading extends AppManagerState {
  const AppManagerLoading();
}

class AppsLoaded extends AppManagerState {
  final List<AppInfo> apps;
  final List<AppInfo> filteredApps;
  final Set<String> selectedApps;
  final String searchQuery;
  final AppType? filterType;
  final AppStatus? filterStatus;
  final bool? filterEnabled;
  final AppSortType sortType;
  final bool sortAscending;

  const AppsLoaded({
    required this.apps,
    required this.filteredApps,
    this.selectedApps = const {},
    this.searchQuery = '',
    this.filterType,
    this.filterStatus,
    this.filterEnabled,
    this.sortType = AppSortType.name,
    this.sortAscending = true,
  });

  AppsLoaded copyWith({
    List<AppInfo>? apps,
    List<AppInfo>? filteredApps,
    Set<String>? selectedApps,
    String? searchQuery,
    AppType? filterType,
    AppStatus? filterStatus,
    bool? filterEnabled,
    AppSortType? sortType,
    bool? sortAscending,
  }) {
    return AppsLoaded(
      apps: apps ?? this.apps,
      filteredApps: filteredApps ?? this.filteredApps,
      selectedApps: selectedApps ?? this.selectedApps,
      searchQuery: searchQuery ?? this.searchQuery,
      filterType: filterType ?? this.filterType,
      filterStatus: filterStatus ?? this.filterStatus,
      filterEnabled: filterEnabled ?? this.filterEnabled,
      sortType: sortType ?? this.sortType,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  List<Object?> get props => [
    apps,
    filteredApps,
    selectedApps,
    searchQuery,
    filterType,
    filterStatus,
    filterEnabled,
    sortType,
    sortAscending,
  ];
}

class AppManagerError extends AppManagerState {
  final String message;

  const AppManagerError(this.message);

  @override
  List<Object?> get props => [message];
}

class AppOperationLoading extends AppManagerState {
  final String packageName;
  final String operation;

  const AppOperationLoading({
    required this.packageName,
    required this.operation,
  });

  @override
  List<Object?> get props => [packageName, operation];
}

class AppOperationSuccess extends AppManagerState {
  final String packageName;
  final String operation;
  final String message;

  const AppOperationSuccess({
    required this.packageName,
    required this.operation,
    required this.message,
  });

  @override
  List<Object?> get props => [packageName, operation, message];
}

class AppOperationError extends AppManagerState {
  final String packageName;
  final String operation;
  final String message;

  const AppOperationError({
    required this.packageName,
    required this.operation,
    required this.message,
  });

  @override
  List<Object?> get props => [packageName, operation, message];
}

class BatchOperationStarted extends AppManagerState {
  final AppBatchOperation operation;

  const BatchOperationStarted(this.operation);

  @override
  List<Object?> get props => [operation];
}

class BatchOperationProgress extends AppManagerState {
  final AppBatchOperation operation;

  const BatchOperationProgress(this.operation);

  @override
  List<Object?> get props => [operation];
}

class BatchOperationCompleted extends AppManagerState {
  final AppBatchOperation operation;

  const BatchOperationCompleted(this.operation);

  @override
  List<Object?> get props => [operation];
}

class BatchOperationError extends AppManagerState {
  final String message;

  const BatchOperationError(this.message);

  @override
  List<Object?> get props => [message];
}

class AppUsageStatsLoaded extends AppManagerState {
  final List<AppUsageStats> stats;

  const AppUsageStatsLoaded(this.stats);

  @override
  List<Object?> get props => [stats];
}

class AppAnalysisLoaded extends AppManagerState {
  final Map<String, int> sizeAnalysis;
  final List<AppInfo> largestApps;
  final List<AppInfo> unusedApps;

  const AppAnalysisLoaded({
    required this.sizeAnalysis,
    required this.largestApps,
    required this.unusedApps,
  });

  @override
  List<Object?> get props => [sizeAnalysis, largestApps, unusedApps];
}

class InstallationProgress extends AppManagerState {
  final String apkPath;
  final double progress;

  const InstallationProgress({required this.apkPath, required this.progress});

  @override
  List<Object?> get props => [apkPath, progress];
}

class InstallationCompleted extends AppManagerState {
  final String apkPath;

  const InstallationCompleted(this.apkPath);

  @override
  List<Object?> get props => [apkPath];
}

class BackupProgress extends AppManagerState {
  final String packageName;
  final String status;

  const BackupProgress({required this.packageName, required this.status});

  @override
  List<Object?> get props => [packageName, status];
}

class BackupCompleted extends AppManagerState {
  final String packageName;
  final String backupPath;

  const BackupCompleted({required this.packageName, required this.backupPath});

  @override
  List<Object?> get props => [packageName, backupPath];
}

class RestoreProgress extends AppManagerState {
  final String backupPath;
  final String status;

  const RestoreProgress({required this.backupPath, required this.status});

  @override
  List<Object?> get props => [backupPath, status];
}

class RestoreCompleted extends AppManagerState {
  final String backupPath;

  const RestoreCompleted(this.backupPath);

  @override
  List<Object?> get props => [backupPath];
}

// AppSortType is imported from app_manager_event.dart
