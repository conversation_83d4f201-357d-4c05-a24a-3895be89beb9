class AppConstants {
  // App Info
  static const String appName = 'HD File Explorer';
  static const String appVersion = '1.0.0';
  
  // Storage Paths
  static const String internalStoragePath = '/storage/emulated/0';
  static const String externalStoragePath = '/storage';
  static const String systemPath = '/system';
  static const String dataPath = '/data';
  static const String rootPath = '/';
  
  // File Extensions
  static const List<String> imageExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
  ];
  
  static const List<String> videoExtensions = [
    '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'
  ];
  
  static const List<String> audioExtensions = [
    '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'
  ];
  
  static const List<String> documentExtensions = [
    '.pdf', '.doc', '.docx', '.txt', '.rtf', '.xls', '.xlsx', '.ppt', '.pptx'
  ];
  
  static const List<String> archiveExtensions = [
    '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'
  ];
  
  static const List<String> apkExtensions = ['.apk'];
  
  // Network
  static const int defaultFtpPort = 21;
  static const int defaultSftpPort = 22;
  static const int connectionTimeout = 30000; // 30 seconds
  
  // Security
  static const String vaultFolderName = '.hd_vault';
  static const String hiddenPrefix = '.';
  
  // Cache
  static const String cacheFolderName = '.hd_cache';
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // File Operations
  static const int bufferSize = 64 * 1024; // 64KB
  static const int maxFileNameLength = 255;
  
  // Search
  static const int maxSearchResults = 1000;
  static const int searchDebounceMs = 300;
  
  // Root Commands
  static const String suCommand = 'su';
  static const String mountCommand = 'mount';
  static const String chmodCommand = 'chmod';
  static const String chownCommand = 'chown';
  static const String rebootCommand = 'reboot';
}
