import 'package:equatable/equatable.dart';

class VaultItem extends Equatable {
  final String id;
  final String originalPath;
  final String encryptedPath;
  final String name;
  final int size;
  final DateTime addedDate;
  final VaultItemType type;
  final bool isLocked;

  const VaultItem({
    required this.id,
    required this.originalPath,
    required this.encryptedPath,
    required this.name,
    required this.size,
    required this.addedDate,
    required this.type,
    required this.isLocked,
  });

  @override
  List<Object?> get props => [
        id,
        originalPath,
        encryptedPath,
        name,
        size,
        addedDate,
        type,
        isLocked,
      ];
}

enum VaultItemType {
  image,
  video,
  document,
  audio,
  other,
}

class SecureFolder extends Equatable {
  final String id;
  final String path;
  final String name;
  final bool isLocked;
  final DateTime createdDate;
  final List<String> allowedUsers;
  final SecurityLevel securityLevel;

  const SecureFolder({
    required this.id,
    required this.path,
    required this.name,
    required this.isLocked,
    required this.createdDate,
    required this.allowedUsers,
    required this.securityLevel,
  });

  @override
  List<Object?> get props => [
        id,
        path,
        name,
        isLocked,
        createdDate,
        allowedUsers,
        securityLevel,
      ];
}

enum SecurityLevel {
  pin,
  fingerprint,
  faceId,
  pattern,
}
