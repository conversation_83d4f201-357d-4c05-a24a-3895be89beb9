import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/search_result.dart';
import '../../domain/entities/search_filter.dart';
import '../../domain/repositories/search_repository.dart';
import '../datasources/search_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: SearchRepository)
class SearchRepositoryImpl implements SearchRepository {
  final SearchDataSource dataSource;

  const SearchRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, SearchResult>> searchFiles(SearchFilter filter) async {
    try {
      final result = await dataSource.searchFiles(filter);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<SearchResult> searchFilesStream(SearchFilter filter) {
    return dataSource.searchFilesStream(filter);
  }

  @override
  Future<Either<Failure, void>> cancelSearch() async {
    try {
      await dataSource.cancelSearch();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel search: $e'));
    }
  }

  @override
  Future<Either<Failure, DuplicateSearchResult>> findDuplicates(
    DuplicateSearchFilter filter,
  ) async {
    try {
      final result = await dataSource.findDuplicates(filter);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<DuplicateSearchResult> findDuplicatesStream(DuplicateSearchFilter filter) {
    return dataSource.findDuplicatesStream(filter);
  }

  @override
  Future<Either<Failure, void>> cancelDuplicateSearch() async {
    try {
      await dataSource.cancelDuplicateSearch();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel duplicate search: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SearchFilter>>> getSearchHistory() async {
    try {
      final result = await dataSource.getSearchHistory();
      return Right(result);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to get search history: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveSearchToHistory(SearchFilter filter) async {
    try {
      await dataSource.saveSearchToHistory(filter);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to save search to history: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearSearchHistory() async {
    try {
      await dataSource.clearSearchHistory();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to clear search history: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getSearchSuggestions(String query) async {
    try {
      final result = await dataSource.getSearchSuggestions(query);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get search suggestions: $e'));
    }
  }

  @override
  Future<Either<Failure, SearchResult>> quickSearch(String query) async {
    try {
      final result = await dataSource.quickSearch(query);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
