import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/search_bloc.dart';
import '../bloc/search_event.dart';
import '../bloc/search_state.dart';
import '../../domain/entities/search_filter.dart';
import '../../../../core/constants/app_constants.dart';

class SearchFiltersWidget extends StatefulWidget {
  const SearchFiltersWidget({super.key});

  @override
  State<SearchFiltersWidget> createState() => _SearchFiltersWidgetState();
}

class _SearchFiltersWidgetState extends State<SearchFiltersWidget> {
  bool _isExpanded = false;
  SearchFilter _currentFilter = const SearchFilter();

  @override
  Widget build(BuildContext context) {
    return BlocListener<SearchBloc, SearchState>(
      listener: (context, state) {
        if (state is SearchFilterUpdated) {
          setState(() {
            _currentFilter = state.filter;
          });
        }
      },
      child: Animated<PERSON>ontainer(
        duration: const Duration(milliseconds: 300),
        child: Card(
          margin: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
          ),
          child: Column(
            children: [
              // Header
              ListTile(
                leading: const Icon(Icons.filter_list),
                title: const Text('Search Filters'),
                subtitle: _buildFilterSummary(),
                trailing: IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
              ),

              // Expandable content
              if (_isExpanded) ...[
                const Divider(),
                Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick file type filters
                      _buildQuickFileTypeFilters(),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Quick size filters
                      _buildQuickSizeFilters(),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Quick date filters
                      _buildQuickDateFilters(),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget? _buildFilterSummary() {
    final activeFilters = <String>[];

    if (_currentFilter.fileTypes.isNotEmpty &&
        !_currentFilter.fileTypes.contains(SearchFileType.all)) {
      activeFilters.add('${_currentFilter.fileTypes.length} file types');
    }

    if (_currentFilter.minSize != null || _currentFilter.maxSize != null) {
      activeFilters.add('Size range');
    }

    if (_currentFilter.modifiedAfter != null ||
        _currentFilter.modifiedBefore != null) {
      activeFilters.add('Date range');
    }

    if (_currentFilter.includeHidden) {
      activeFilters.add('Hidden files');
    }

    if (activeFilters.isEmpty) {
      return const Text('No filters applied');
    }

    return Text(activeFilters.join(', '));
  }

  Widget _buildQuickFileTypeFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'File Types',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children:
              [
                SearchFileType.images,
                SearchFileType.videos,
                SearchFileType.audio,
                SearchFileType.documents,
                SearchFileType.folders,
              ].map((type) {
                final isSelected = _currentFilter.fileTypes.contains(type);
                return FilterChip(
                  label: Text(_getFileTypeName(type)),
                  selected: isSelected,
                  onSelected: (selected) => _toggleFileType(type, selected),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickSizeFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'File Size',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children: [
            _buildSizeChip('Small (< 1MB)', null, 1024 * 1024),
            _buildSizeChip('Medium (1-10MB)', 1024 * 1024, 10 * 1024 * 1024),
            _buildSizeChip(
              'Large (10-100MB)',
              10 * 1024 * 1024,
              100 * 1024 * 1024,
            ),
            _buildSizeChip('Very Large (> 100MB)', 100 * 1024 * 1024, null),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Modified Date',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children: [
            _buildDateChip(
              'Today',
              DateTime.now().subtract(const Duration(days: 1)),
            ),
            _buildDateChip(
              'This Week',
              DateTime.now().subtract(const Duration(days: 7)),
            ),
            _buildDateChip(
              'This Month',
              DateTime.now().subtract(const Duration(days: 30)),
            ),
            _buildDateChip(
              'This Year',
              DateTime.now().subtract(const Duration(days: 365)),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSizeChip(String label, int? minSize, int? maxSize) {
    final isSelected =
        _currentFilter.minSize == minSize && _currentFilter.maxSize == maxSize;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _updateFilter(
            _currentFilter.copyWith(minSize: minSize, maxSize: maxSize),
          );
        } else {
          _updateFilter(_currentFilter.copyWith(minSize: null, maxSize: null));
        }
      },
    );
  }

  Widget _buildDateChip(String label, DateTime afterDate) {
    final isSelected =
        _currentFilter.modifiedAfter != null &&
        _currentFilter.modifiedAfter!.isAfter(
          afterDate.subtract(const Duration(hours: 1)),
        );

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _updateFilter(_currentFilter.copyWith(modifiedAfter: afterDate));
        } else {
          _updateFilter(_currentFilter.copyWith(modifiedAfter: null));
        }
      },
    );
  }

  void _toggleFileType(SearchFileType type, bool selected) {
    List<SearchFileType> newTypes = List.from(_currentFilter.fileTypes);

    if (selected) {
      if (!newTypes.contains(type)) {
        newTypes.add(type);
      }
    } else {
      newTypes.remove(type);
    }

    _updateFilter(_currentFilter.copyWith(fileTypes: newTypes));
  }

  void _updateFilter(SearchFilter filter) {
    setState(() {
      _currentFilter = filter;
    });
    context.read<SearchBloc>().add(UpdateSearchFilterEvent(filter));
  }

  String _getFileTypeName(SearchFileType type) {
    switch (type) {
      case SearchFileType.all:
        return 'All';
      case SearchFileType.images:
        return 'Images';
      case SearchFileType.videos:
        return 'Videos';
      case SearchFileType.audio:
        return 'Audio';
      case SearchFileType.documents:
        return 'Documents';
      case SearchFileType.archives:
        return 'Archives';
      case SearchFileType.applications:
        return 'Apps';
      case SearchFileType.folders:
        return 'Folders';
    }
  }
}
