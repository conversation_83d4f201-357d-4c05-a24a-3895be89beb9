import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? code;
  
  const Failure({
    required this.message,
    this.code,
  });
  
  @override
  List<Object?> get props => [message, code];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });
}

// File system failures
class FileSystemFailure extends Failure {
  const FileSystemFailure({
    required super.message,
    super.code,
  });
}

class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code,
  });
}

class FileNotFoundFailure extends Failure {
  const FileNotFoundFailure({
    required super.message,
    super.code,
  });
}

class StorageFullFailure extends Failure {
  const StorageFullFailure({
    required super.message,
    super.code,
  });
}

// Security failures
class AuthenticationFailure extends Failure {
  const AuthenticationFailure({
    required super.message,
    super.code,
  });
}

class EncryptionFailure extends Failure {
  const EncryptionFailure({
    required super.message,
    super.code,
  });
}

// Network specific failures
class FtpConnectionFailure extends Failure {
  const FtpConnectionFailure({
    required super.message,
    super.code,
  });
}

class LanConnectionFailure extends Failure {
  const LanConnectionFailure({
    required super.message,
    super.code,
  });
}

// App management failures
class AppInstallationFailure extends Failure {
  const AppInstallationFailure({
    required super.message,
    super.code,
  });
}

class AppUninstallationFailure extends Failure {
  const AppUninstallationFailure({
    required super.message,
    super.code,
  });
}

// Root access failures
class RootAccessFailure extends Failure {
  const RootAccessFailure({
    required super.message,
    super.code,
  });
}

class SystemCommandFailure extends Failure {
  const SystemCommandFailure({
    required super.message,
    super.code,
  });
}

// Archive failures
class CompressionFailure extends Failure {
  const CompressionFailure({
    required super.message,
    super.code,
  });
}

class ExtractionFailure extends Failure {
  const ExtractionFailure({
    required super.message,
    super.code,
  });
}
