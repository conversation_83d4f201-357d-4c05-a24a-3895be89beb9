import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class StorageChart extends StatelessWidget {
  final Map<String, int> categorizedUsage;

  const StorageChart({
    super.key,
    required this.categorizedUsage,
  });

  @override
  Widget build(BuildContext context) {
    final totalSize = categorizedUsage.values.fold<int>(0, (sum, size) => sum + size);
    
    if (totalSize == 0) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Text('No data available'),
          ),
        ),
      );
    }

    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];

    final sections = categorizedUsage.entries.map((entry) {
      final index = categorizedUsage.keys.toList().indexOf(entry.key);
      final percentage = (entry.value / totalSize) * 100;
      
      return PieChartSectionData(
        color: colors[index % colors.length],
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Legend
            Wrap(
              spacing: AppConstants.defaultPadding,
              runSpacing: AppConstants.smallPadding,
              children: categorizedUsage.entries.map((entry) {
                final index = categorizedUsage.keys.toList().indexOf(entry.key);
                final color = colors[index % colors.length];
                final size = FileUtils.formatFileSize(entry.value);
                
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      '${entry.key} ($size)',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
