import 'package:flutter/material.dart';

/// ألوان التطبيق
class AppColors {
  AppColors._();

  // Primary Colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryVariant = Color(0xFF1976D2);
  static const Color onPrimary = Color(0xFFFFFFFF);

  // Secondary Colors
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryVariant = Color(0xFF018786);
  static const Color onSecondary = Color(0xFF000000);

  // Background Colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF000000);

  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF000000);

  // Error Colors
  static const Color error = Color(0xFFB00020);
  static const Color onError = Color(0xFFFFFFFF);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);

  // Outline Colors
  static const Color outline = Color(0xFFE0E0E0);
  static const Color outlineVariant = Color(0xFFF5F5F5);

  // Success Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color onSuccess = Color(0xFFFFFFFF);

  // Warning Colors
  static const Color warning = Color(0xFFFF9800);
  static const Color onWarning = Color(0xFFFFFFFF);

  // Info Colors
  static const Color info = Color(0xFF2196F3);
  static const Color onInfo = Color(0xFFFFFFFF);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkOnBackground = Color(0xFFFFFFFF);
  static const Color darkOnSurface = Color(0xFFFFFFFF);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);

  // File Type Colors
  static const Color fileTypeDocument = Color(0xFF1976D2);
  static const Color fileTypeImage = Color(0xFF388E3C);
  static const Color fileTypeVideo = Color(0xFFD32F2F);
  static const Color fileTypeAudio = Color(0xFF7B1FA2);
  static const Color fileTypeArchive = Color(0xFFFF8F00);
  static const Color fileTypeCode = Color(0xFF455A64);
  static const Color fileTypeOther = Color(0xFF616161);

  // Status Colors
  static const Color statusOnline = Color(0xFF4CAF50);
  static const Color statusOffline = Color(0xFF9E9E9E);
  static const Color statusBusy = Color(0xFFFF5722);
  static const Color statusAway = Color(0xFFFF9800);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Overlay Colors
  static const Color overlayLight = Color(0x0A000000);
  static const Color overlayMedium = Color(0x1F000000);
  static const Color overlayDark = Color(0x33000000);

  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF9E9E9E);

  // Disabled Colors
  static const Color disabled = Color(0xFFBDBDBD);
  static const Color onDisabled = Color(0xFF9E9E9E);

  // Helper methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color blend(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  // Color schemes
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary,
    onPrimary: onPrimary,
    secondary: secondary,
    onSecondary: onSecondary,
    error: error,
    onError: onError,
    background: background,
    onBackground: onBackground,
    surface: surface,
    onSurface: onSurface,
  );

  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primary,
    onPrimary: onPrimary,
    secondary: secondary,
    onSecondary: onSecondary,
    error: error,
    onError: onError,
    background: darkBackground,
    onBackground: darkOnBackground,
    surface: darkSurface,
    onSurface: darkOnSurface,
  );

  // Material 3 Colors
  static const Color primaryContainer = Color(0xFFBBDEFB);
  static const Color onPrimaryContainer = Color(0xFF0D47A1);
  static const Color secondaryContainer = Color(0xFFB2DFDB);
  static const Color onSecondaryContainer = Color(0xFF004D40);
  static const Color tertiaryContainer = Color(0xFFE1BEE7);
  static const Color onTertiaryContainer = Color(0xFF4A148C);

  // Semantic Colors
  static const Color semanticPositive = Color(0xFF4CAF50);
  static const Color semanticNegative = Color(0xFFF44336);
  static const Color semanticNeutral = Color(0xFF9E9E9E);
  static const Color semanticCaution = Color(0xFFFF9800);

  // Brand Colors
  static const Color brandPrimary = Color(0xFF2196F3);
  static const Color brandSecondary = Color(0xFF03DAC6);
  static const Color brandAccent = Color(0xFFFF4081);

  // System Colors
  static const Color systemBlue = Color(0xFF007AFF);
  static const Color systemGreen = Color(0xFF34C759);
  static const Color systemIndigo = Color(0xFF5856D6);
  static const Color systemOrange = Color(0xFFFF9500);
  static const Color systemPink = Color(0xFFFF2D92);
  static const Color systemPurple = Color(0xFFAF52DE);
  static const Color systemRed = Color(0xFFFF3B30);
  static const Color systemTeal = Color(0xFF5AC8FA);
  static const Color systemYellow = Color(0xFFFFCC00);

  // File Manager Specific Colors
  static const Color folderColor = Color(0xFF2196F3);
  static const Color fileColor = Color(0xFF757575);
  static const Color selectedColor = Color(0xFFE3F2FD);
  static const Color cutColor = Color(0xFFFFEBEE);
  static const Color copyColor = Color(0xFFE8F5E8);

  // Network Colors
  static const Color networkConnected = Color(0xFF4CAF50);
  static const Color networkDisconnected = Color(0xFFF44336);
  static const Color networkConnecting = Color(0xFFFF9800);

  // Root Access Colors
  static const Color rootGranted = Color(0xFF4CAF50);
  static const Color rootDenied = Color(0xFFF44336);
  static const Color rootUnknown = Color(0xFF9E9E9E);

  // Storage Colors
  static const Color storageUsed = Color(0xFF2196F3);
  static const Color storageFree = Color(0xFFE0E0E0);
  static const Color storageSystem = Color(0xFFFF9800);
  static const Color storageApps = Color(0xFF4CAF50);
  static const Color storageMedia = Color(0xFF9C27B0);
  static const Color storageOther = Color(0xFF607D8B);
}
