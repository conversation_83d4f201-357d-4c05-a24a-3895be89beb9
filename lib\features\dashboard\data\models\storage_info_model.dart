import '../../domain/entities/storage_info.dart';

class StorageInfoModel extends StorageInfo {
  const StorageInfoModel({
    required super.totalSpace,
    required super.freeSpace,
    required super.usedSpace,
    required super.path,
    required super.type,
    required super.isRemovable,
    required super.isEmulated,
  });

  factory StorageInfoModel.fromJson(Map<String, dynamic> json) {
    return StorageInfoModel(
      totalSpace: json['totalSpace'] as int,
      freeSpace: json['freeSpace'] as int,
      usedSpace: json['usedSpace'] as int,
      path: json['path'] as String,
      type: StorageType.values[json['type'] as int],
      isRemovable: json['isRemovable'] as bool,
      isEmulated: json['isEmulated'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSpace': totalSpace,
      'freeSpace': freeSpace,
      'usedSpace': usedSpace,
      'path': path,
      'type': type.index,
      'isRemovable': isRemovable,
      'isEmulated': isEmulated,
    };
  }

  StorageInfoModel copyWith({
    int? totalSpace,
    int? freeSpace,
    int? usedSpace,
    String? path,
    StorageType? type,
    bool? isRemovable,
    bool? isEmulated,
  }) {
    return StorageInfoModel(
      totalSpace: totalSpace ?? this.totalSpace,
      freeSpace: freeSpace ?? this.freeSpace,
      usedSpace: usedSpace ?? this.usedSpace,
      path: path ?? this.path,
      type: type ?? this.type,
      isRemovable: isRemovable ?? this.isRemovable,
      isEmulated: isEmulated ?? this.isEmulated,
    );
  }
}
