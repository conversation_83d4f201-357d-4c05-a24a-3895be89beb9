import 'package:equatable/equatable.dart';

class CleanupItem extends Equatable {
  final String id;
  final CleanupType type;
  final String name;
  final String description;
  final List<String> filePaths;
  final int totalSize;
  final int fileCount;
  final CleanupPriority priority;
  final bool isSelected;
  final bool isSafeToDelete;
  final DateTime lastModified;

  const CleanupItem({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.filePaths,
    required this.totalSize,
    required this.fileCount,
    required this.priority,
    this.isSelected = false,
    this.isSafeToDelete = true,
    required this.lastModified,
  });

  CleanupItem copyWith({
    String? id,
    CleanupType? type,
    String? name,
    String? description,
    List<String>? filePaths,
    int? totalSize,
    int? fileCount,
    CleanupPriority? priority,
    bool? isSelected,
    bool? isSafeToDelete,
    DateTime? lastModified,
  }) {
    return CleanupItem(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      filePaths: filePaths ?? this.filePaths,
      totalSize: totalSize ?? this.totalSize,
      fileCount: fileCount ?? this.fileCount,
      priority: priority ?? this.priority,
      isSelected: isSelected ?? this.isSelected,
      isSafeToDelete: isSafeToDelete ?? this.isSafeToDelete,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        description,
        filePaths,
        totalSize,
        fileCount,
        priority,
        isSelected,
        isSafeToDelete,
        lastModified,
      ];
}

enum CleanupType {
  cache,
  temporaryFiles,
  logs,
  thumbnails,
  downloads,
  emptyFolders,
  largeFiles,
  oldFiles,
  apkFiles,
  residualFiles,
}

enum CleanupPriority {
  low,
  medium,
  high,
  critical,
}

class SystemAnalysis extends Equatable {
  final int totalFiles;
  final int totalFolders;
  final int totalSize;
  final int availableSpace;
  final int usedSpace;
  final Map<CleanupType, int> cleanupOpportunities;
  final List<CleanupItem> cleanupItems;
  final Map<String, int> largestDirectories;
  final Map<String, int> fileTypeDistribution;
  final DateTime analysisTime;
  final Duration analysisDuration;

  const SystemAnalysis({
    required this.totalFiles,
    required this.totalFolders,
    required this.totalSize,
    required this.availableSpace,
    required this.usedSpace,
    required this.cleanupOpportunities,
    required this.cleanupItems,
    required this.largestDirectories,
    required this.fileTypeDistribution,
    required this.analysisTime,
    required this.analysisDuration,
  });

  double get usagePercentage => (usedSpace / (usedSpace + availableSpace)) * 100;
  
  int get totalCleanupSize => cleanupItems.fold(0, (sum, item) => sum + item.totalSize);
  
  int get selectedCleanupSize => cleanupItems
      .where((item) => item.isSelected)
      .fold(0, (sum, item) => sum + item.totalSize);

  @override
  List<Object?> get props => [
        totalFiles,
        totalFolders,
        totalSize,
        availableSpace,
        usedSpace,
        cleanupOpportunities,
        cleanupItems,
        largestDirectories,
        fileTypeDistribution,
        analysisTime,
        analysisDuration,
      ];
}

class CleanupResult extends Equatable {
  final List<String> deletedFiles;
  final int totalDeletedSize;
  final int totalDeletedCount;
  final List<String> failedDeletions;
  final Duration cleanupDuration;
  final bool isComplete;
  final String? errorMessage;

  const CleanupResult({
    required this.deletedFiles,
    required this.totalDeletedSize,
    required this.totalDeletedCount,
    required this.failedDeletions,
    required this.cleanupDuration,
    this.isComplete = true,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        deletedFiles,
        totalDeletedSize,
        totalDeletedCount,
        failedDeletions,
        cleanupDuration,
        isComplete,
        errorMessage,
      ];
}
