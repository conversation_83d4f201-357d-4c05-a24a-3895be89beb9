import 'package:equatable/equatable.dart';
import '../../domain/entities/file_operation.dart';

abstract class FileOperationsState extends Equatable {
  const FileOperationsState();

  @override
  List<Object?> get props => [];
}

class FileOperationsInitial extends FileOperationsState {
  const FileOperationsInitial();
}

class FileOperationsLoading extends FileOperationsState {
  const FileOperationsLoading();
}

class FileOperationsLoaded extends FileOperationsState {
  final List<FileOperation> activeOperations;
  final List<FileOperation> completedOperations;
  final List<String> conflictingFiles;

  const FileOperationsLoaded({
    required this.activeOperations,
    required this.completedOperations,
    this.conflictingFiles = const [],
  });

  FileOperationsLoaded copyWith({
    List<FileOperation>? activeOperations,
    List<FileOperation>? completedOperations,
    List<String>? conflictingFiles,
  }) {
    return FileOperationsLoaded(
      activeOperations: activeOperations ?? this.activeOperations,
      completedOperations: completedOperations ?? this.completedOperations,
      conflictingFiles: conflictingFiles ?? this.conflictingFiles,
    );
  }

  @override
  List<Object?> get props => [activeOperations, completedOperations, conflictingFiles];
}

class FileOperationsError extends FileOperationsState {
  final String message;

  const FileOperationsError(this.message);

  @override
  List<Object?> get props => [message];
}

class OperationStarted extends FileOperationsState {
  final String operationId;
  final FileOperationType type;

  const OperationStarted({
    required this.operationId,
    required this.type,
  });

  @override
  List<Object?> get props => [operationId, type];
}

class ConflictsDetected extends FileOperationsState {
  final List<String> conflictingFiles;
  final List<String> sourcePaths;
  final String destinationPath;

  const ConflictsDetected({
    required this.conflictingFiles,
    required this.sourcePaths,
    required this.destinationPath,
  });

  @override
  List<Object?> get props => [conflictingFiles, sourcePaths, destinationPath];
}

class OperationProgress extends FileOperationsState {
  final FileOperation operation;

  const OperationProgress(this.operation);

  @override
  List<Object?> get props => [operation];
}

class OperationCompleted extends FileOperationsState {
  final FileOperation operation;

  const OperationCompleted(this.operation);

  @override
  List<Object?> get props => [operation];
}

class OperationFailed extends FileOperationsState {
  final FileOperation operation;

  const OperationFailed(this.operation);

  @override
  List<Object?> get props => [operation];
}
