import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/cleaner_bloc.dart';
import '../bloc/cleaner_event.dart';
import '../bloc/cleaner_state.dart';
import '../widgets/system_analysis_widget.dart';
import '../widgets/cleanup_items_widget.dart';
import '../widgets/cleanup_history_widget.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/injection_container.dart';

class CleanerPage extends StatelessWidget {
  const CleanerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CleanerBloc>()..add(const StartSystemAnalysisEvent()),
      child: const CleanerView(),
    );
  }
}

class CleanerView extends StatelessWidget {
  const CleanerView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('System Cleaner'),
          bottom: const TabBar(
            tabs: [
              Tab(
                icon: Icon(Icons.analytics),
                text: 'Analysis',
              ),
              Tab(
                icon: Icon(Icons.cleaning_services),
                text: 'Cleanup',
              ),
              Tab(
                icon: Icon(Icons.history),
                text: 'History',
              ),
            ],
          ),
          actions: [
            BlocBuilder<CleanerBloc, CleanerState>(
              builder: (context, state) {
                return IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: state is SystemAnalysisLoading || state is CleanupLoading
                      ? null
                      : () {
                          context.read<CleanerBloc>().add(const RefreshAnalysisEvent());
                        },
                );
              },
            ),
          ],
        ),
        body: BlocConsumer<CleanerBloc, CleanerState>(
          listener: (context, state) {
            if (state is SystemAnalysisError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is CleanupError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is CleanupCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Cleanup completed! Freed ${_formatFileSize(state.result.totalDeletedSize)} of space',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is SystemAnalysisCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Analysis completed in ${state.analysis.analysisDuration.inSeconds}s',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            }
          },
          builder: (context, state) {
            return const TabBarView(
              children: [
                SystemAnalysisTab(),
                CleanupTab(),
                HistoryTab(),
              ],
            );
          },
        ),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

class SystemAnalysisTab extends StatelessWidget {
  const SystemAnalysisTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const SystemAnalysisWidget();
  }
}

class CleanupTab extends StatelessWidget {
  const CleanupTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const CleanupItemsWidget();
  }
}

class HistoryTab extends StatelessWidget {
  const HistoryTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<CleanerBloc>()..add(const LoadCleanupHistoryEvent()),
      child: const CleanupHistoryWidget(),
    );
  }
}

class EmptyCleanerState extends StatelessWidget {
  const EmptyCleanerState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cleaning_services,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'System Cleaner',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Analyze your system to find files that can be cleaned',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              context.read<CleanerBloc>().add(const StartSystemAnalysisEvent());
            },
            icon: const Icon(Icons.analytics),
            label: const Text('Start Analysis'),
          ),
        ],
      ),
    );
  }
}
