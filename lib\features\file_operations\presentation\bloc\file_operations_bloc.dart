import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../domain/usecases/copy_files.dart';
import '../../domain/usecases/compress_files.dart';
import '../../domain/repositories/file_operations_repository.dart';
import '../../domain/entities/file_operation.dart';
import 'file_operations_event.dart';
import 'file_operations_state.dart';

@injectable
class FileOperationsBloc
    extends Bloc<FileOperationsEvent, FileOperationsState> {
  final CopyFiles _copyFiles;
  final CompressFiles _compressFiles;
  final FileOperationsRepository _repository;

  StreamSubscription? _operationsSubscription;

  FileOperationsBloc(this._copyFiles, this._compressFiles, this._repository)
    : super(const FileOperationsInitial()) {
    on<LoadActiveOperationsEvent>(_onLoadActiveOperations);
    on<StartCopyOperationEvent>(_onStartCopyOperation);
    on<StartMoveOperationEvent>(_onStartMoveOperation);
    on<StartDeleteOperationEvent>(_onStartDeleteOperation);
    on<StartCompressionEvent>(_onStartCompression);
    on<StartExtractionEvent>(_onStartExtraction);
    on<PauseOperationEvent>(_onPauseOperation);
    on<ResumeOperationEvent>(_onResumeOperation);
    on<CancelOperationEvent>(_onCancelOperation);
    on<CheckConflictsEvent>(_onCheckConflicts);
    on<OperationUpdatedEvent>(_onOperationUpdated);
    on<OperationsListUpdatedEvent>(_onOperationsListUpdated);

    // Listen to all operations updates
    _operationsSubscription = _repository.watchAllOperations().listen((
      operations,
    ) {
      add(OperationsListUpdatedEvent(operations));
    });
  }

  @override
  Future<void> close() {
    _operationsSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLoadActiveOperations(
    LoadActiveOperationsEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    emit(const FileOperationsLoading());

    final result = await _repository.getActiveOperations();

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operations) => emit(
        FileOperationsLoaded(
          activeOperations: operations,
          completedOperations: const [],
        ),
      ),
    );
  }

  Future<void> _onStartCopyOperation(
    StartCopyOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _copyFiles(
      sourcePaths: event.sourcePaths,
      destinationPath: event.destinationPath,
    );

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operationId) => emit(
        OperationStarted(
          operationId: operationId,
          type: FileOperationType.copy,
        ),
      ),
    );
  }

  Future<void> _onStartMoveOperation(
    StartMoveOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.moveFiles(
      event.sourcePaths,
      event.destinationPath,
    );

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operationId) => emit(
        OperationStarted(
          operationId: operationId,
          type: FileOperationType.move,
        ),
      ),
    );
  }

  Future<void> _onStartDeleteOperation(
    StartDeleteOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.deleteFiles(event.filePaths);

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operationId) => emit(
        OperationStarted(
          operationId: operationId,
          type: FileOperationType.delete,
        ),
      ),
    );
  }

  Future<void> _onStartCompression(
    StartCompressionEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _compressFiles(
      sourcePaths: event.sourcePaths,
      destinationPath: event.destinationPath,
      options: event.options,
    );

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operationId) => emit(
        OperationStarted(
          operationId: operationId,
          type: FileOperationType.compress,
        ),
      ),
    );
  }

  Future<void> _onStartExtraction(
    StartExtractionEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.extractArchive(
      event.archivePath,
      event.destinationPath,
      event.password,
    );

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (operationId) => emit(
        OperationStarted(
          operationId: operationId,
          type: FileOperationType.extract,
        ),
      ),
    );
  }

  Future<void> _onPauseOperation(
    PauseOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.pauseOperation(event.operationId);

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (_) {}, // Success handled by stream updates
    );
  }

  Future<void> _onResumeOperation(
    ResumeOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.resumeOperation(event.operationId);

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (_) {}, // Success handled by stream updates
    );
  }

  Future<void> _onCancelOperation(
    CancelOperationEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.cancelOperation(event.operationId);

    result.fold(
      (failure) => emit(FileOperationsError(failure.message)),
      (_) {}, // Success handled by stream updates
    );
  }

  Future<void> _onCheckConflicts(
    CheckConflictsEvent event,
    Emitter<FileOperationsState> emit,
  ) async {
    final result = await _repository.getConflictingFiles(
      event.sourcePaths,
      event.destinationPath,
    );

    result.fold((failure) => emit(FileOperationsError(failure.message)), (
      conflicts,
    ) {
      if (conflicts.isNotEmpty) {
        emit(
          ConflictsDetected(
            conflictingFiles: conflicts,
            sourcePaths: event.sourcePaths,
            destinationPath: event.destinationPath,
          ),
        );
      }
    });
  }

  void _onOperationUpdated(
    OperationUpdatedEvent event,
    Emitter<FileOperationsState> emit,
  ) {
    final operation = event.operation;

    switch (operation.status) {
      case FileOperationStatus.inProgress:
        emit(OperationProgress(operation));
        break;
      case FileOperationStatus.completed:
        emit(OperationCompleted(operation));
        break;
      case FileOperationStatus.failed:
        emit(OperationFailed(operation));
        break;
      default:
        break;
    }
  }

  void _onOperationsListUpdated(
    OperationsListUpdatedEvent event,
    Emitter<FileOperationsState> emit,
  ) {
    final operations = event.operations;

    final activeOps =
        operations
            .where((op) => op.status.index <= 2) // pending, inProgress, paused
            .toList();

    final completedOps =
        operations
            .where((op) => op.status.index > 2) // completed, failed, cancelled
            .toList();

    if (state is FileOperationsLoaded) {
      final currentState = state as FileOperationsLoaded;
      emit(
        currentState.copyWith(
          activeOperations: activeOps,
          completedOperations: completedOps,
        ),
      );
    } else {
      emit(
        FileOperationsLoaded(
          activeOperations: activeOps,
          completedOperations: completedOps,
        ),
      );
    }
  }
}
