import 'package:equatable/equatable.dart';

class AppInfo extends Equatable {
  final String packageName;
  final String appName;
  final String version;
  final int versionCode;
  final int size;
  final DateTime installDate;
  final DateTime updateDate;
  final bool isSystemApp;
  final bool isEnabled;
  final String? iconPath;
  final String? apkPath;
  final String? dataDir;
  final List<String>? permissions;

  const AppInfo({
    required this.packageName,
    required this.appName,
    required this.version,
    required this.versionCode,
    required this.size,
    required this.installDate,
    required this.updateDate,
    required this.isSystemApp,
    required this.isEnabled,
    this.iconPath,
    this.apkPath,
    this.dataDir,
    this.permissions,
  });

  String get displaySize {
    if (size == 0) return 'Unknown';
    
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    int i = 0;
    double sizeDouble = size.toDouble();
    
    while (sizeDouble >= 1024 && i < suffixes.length - 1) {
      sizeDouble /= 1024;
      i++;
    }
    
    return '${sizeDouble.toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }

  String get appType => isSystemApp ? 'System App' : 'User App';

  @override
  List<Object?> get props => [
        packageName,
        appName,
        version,
        versionCode,
        size,
        installDate,
        updateDate,
        isSystemApp,
        isEnabled,
        iconPath,
        apkPath,
        dataDir,
        permissions,
      ];
}
