import 'package:equatable/equatable.dart';

class AppInfo extends Equatable {
  final String packageName;
  final String appName;
  final String version;
  final String versionCode;
  final int installedSize;
  final int dataSize;
  final int cacheSize;
  final DateTime installDate;
  final DateTime lastUpdateDate;
  final DateTime lastUsedDate;
  final String? iconPath;
  final AppType type;
  final AppStatus status;
  final List<String> permissions;
  final bool isSystemApp;
  final bool isEnabled;
  final bool canUninstall;
  final bool canDisable;
  final bool canClearData;
  final bool canClearCache;
  final String? apkPath;
  final String? dataPath;
  final int targetSdkVersion;
  final int minSdkVersion;
  final String? signature;

  const AppInfo({
    required this.packageName,
    required this.appName,
    required this.version,
    required this.versionCode,
    required this.installedSize,
    required this.dataSize,
    required this.cacheSize,
    required this.installDate,
    required this.lastUpdateDate,
    required this.lastUsedDate,
    this.iconPath,
    required this.type,
    required this.status,
    required this.permissions,
    required this.isSystemApp,
    required this.isEnabled,
    required this.canUninstall,
    required this.canDisable,
    required this.canClearData,
    required this.canClearCache,
    this.apkPath,
    this.dataPath,
    required this.targetSdkVersion,
    required this.minSdkVersion,
    this.signature,
  });

  int get totalSize => installedSize + dataSize + cacheSize;

  String get displaySize {
    if (totalSize == 0) return 'Unknown';
    
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    int i = 0;
    double sizeDouble = totalSize.toDouble();
    
    while (sizeDouble >= 1024 && i < suffixes.length - 1) {
      sizeDouble /= 1024;
      i++;
    }
    
    return '${sizeDouble.toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }

  String get appType => isSystemApp ? 'System App' : 'User App';

  AppInfo copyWith({
    String? packageName,
    String? appName,
    String? version,
    String? versionCode,
    int? installedSize,
    int? dataSize,
    int? cacheSize,
    DateTime? installDate,
    DateTime? lastUpdateDate,
    DateTime? lastUsedDate,
    String? iconPath,
    AppType? type,
    AppStatus? status,
    List<String>? permissions,
    bool? isSystemApp,
    bool? isEnabled,
    bool? canUninstall,
    bool? canDisable,
    bool? canClearData,
    bool? canClearCache,
    String? apkPath,
    String? dataPath,
    int? targetSdkVersion,
    int? minSdkVersion,
    String? signature,
  }) {
    return AppInfo(
      packageName: packageName ?? this.packageName,
      appName: appName ?? this.appName,
      version: version ?? this.version,
      versionCode: versionCode ?? this.versionCode,
      installedSize: installedSize ?? this.installedSize,
      dataSize: dataSize ?? this.dataSize,
      cacheSize: cacheSize ?? this.cacheSize,
      installDate: installDate ?? this.installDate,
      lastUpdateDate: lastUpdateDate ?? this.lastUpdateDate,
      lastUsedDate: lastUsedDate ?? this.lastUsedDate,
      iconPath: iconPath ?? this.iconPath,
      type: type ?? this.type,
      status: status ?? this.status,
      permissions: permissions ?? this.permissions,
      isSystemApp: isSystemApp ?? this.isSystemApp,
      isEnabled: isEnabled ?? this.isEnabled,
      canUninstall: canUninstall ?? this.canUninstall,
      canDisable: canDisable ?? this.canDisable,
      canClearData: canClearData ?? this.canClearData,
      canClearCache: canClearCache ?? this.canClearCache,
      apkPath: apkPath ?? this.apkPath,
      dataPath: dataPath ?? this.dataPath,
      targetSdkVersion: targetSdkVersion ?? this.targetSdkVersion,
      minSdkVersion: minSdkVersion ?? this.minSdkVersion,
      signature: signature ?? this.signature,
    );
  }

  @override
  List<Object?> get props => [
        packageName,
        appName,
        version,
        versionCode,
        installedSize,
        dataSize,
        cacheSize,
        installDate,
        lastUpdateDate,
        lastUsedDate,
        iconPath,
        type,
        status,
        permissions,
        isSystemApp,
        isEnabled,
        canUninstall,
        canDisable,
        canClearData,
        canClearCache,
        apkPath,
        dataPath,
        targetSdkVersion,
        minSdkVersion,
        signature,
      ];
}

enum AppType {
  user,
  system,
  updated,
  disabled,
}

enum AppStatus {
  running,
  stopped,
  cached,
  background,
}

class AppUsageStats extends Equatable {
  final String packageName;
  final Duration totalTimeInForeground;
  final int launchCount;
  final DateTime lastTimeUsed;
  final Duration screenTime;
  final int notificationCount;
  final Map<DateTime, Duration> dailyUsage;

  const AppUsageStats({
    required this.packageName,
    required this.totalTimeInForeground,
    required this.launchCount,
    required this.lastTimeUsed,
    required this.screenTime,
    required this.notificationCount,
    required this.dailyUsage,
  });

  @override
  List<Object?> get props => [
        packageName,
        totalTimeInForeground,
        launchCount,
        lastTimeUsed,
        screenTime,
        notificationCount,
        dailyUsage,
      ];
}

class AppBatchOperation extends Equatable {
  final String id;
  final AppBatchOperationType type;
  final List<String> packageNames;
  final AppBatchOperationStatus status;
  final int totalApps;
  final int processedApps;
  final int successfulApps;
  final int failedApps;
  final List<String> failedPackages;
  final DateTime startTime;
  final DateTime? endTime;
  final String? errorMessage;

  const AppBatchOperation({
    required this.id,
    required this.type,
    required this.packageNames,
    required this.status,
    required this.totalApps,
    required this.processedApps,
    required this.successfulApps,
    required this.failedApps,
    required this.failedPackages,
    required this.startTime,
    this.endTime,
    this.errorMessage,
  });

  double get progress => totalApps > 0 ? processedApps / totalApps : 0.0;

  @override
  List<Object?> get props => [
        id,
        type,
        packageNames,
        status,
        totalApps,
        processedApps,
        successfulApps,
        failedApps,
        failedPackages,
        startTime,
        endTime,
        errorMessage,
      ];
}

enum AppBatchOperationType {
  uninstall,
  disable,
  enable,
  clearData,
  clearCache,
  backup,
}

enum AppBatchOperationStatus {
  pending,
  running,
  completed,
  failed,
  cancelled,
}
