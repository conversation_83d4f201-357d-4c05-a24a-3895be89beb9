import 'package:equatable/equatable.dart';

class StorageInfo extends Equatable {
  final int totalSpace;
  final int freeSpace;
  final int usedSpace;
  final String path;
  final StorageType type;
  final bool isRemovable;
  final bool isEmulated;

  const StorageInfo({
    required this.totalSpace,
    required this.freeSpace,
    required this.usedSpace,
    required this.path,
    required this.type,
    required this.isRemovable,
    required this.isEmulated,
  });

  double get usagePercentage {
    if (totalSpace == 0) return 0.0;
    return (usedSpace / totalSpace) * 100;
  }

  String get displayName {
    switch (type) {
      case StorageType.internal:
        return 'Internal Storage';
      case StorageType.external:
        return 'SD Card';
      case StorageType.usb:
        return 'USB Storage';
      case StorageType.network:
        return 'Network Storage';
    }
  }

  @override
  List<Object?> get props => [
        totalSpace,
        freeSpace,
        usedSpace,
        path,
        type,
        isRemovable,
        isEmulated,
      ];
}

enum StorageType {
  internal,
  external,
  usb,
  network,
}
