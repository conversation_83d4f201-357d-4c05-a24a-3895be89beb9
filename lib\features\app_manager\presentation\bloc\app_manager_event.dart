import 'package:equatable/equatable.dart';
import '../../domain/entities/app_info.dart';

abstract class AppManagerEvent extends Equatable {
  const AppManagerEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllAppsEvent extends AppManagerEvent {
  const LoadAllAppsEvent();
}

class LoadUserAppsEvent extends AppManagerEvent {
  const LoadUserAppsEvent();
}

class LoadSystemAppsEvent extends AppManagerEvent {
  const LoadSystemAppsEvent();
}

class LoadRunningAppsEvent extends AppManagerEvent {
  const LoadRunningAppsEvent();
}

class RefreshAppsEvent extends AppManagerEvent {
  const RefreshAppsEvent();
}

class SearchAppsEvent extends AppManagerEvent {
  final String query;

  const SearchAppsEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class FilterAppsEvent extends AppManagerEvent {
  final AppType? type;
  final AppStatus? status;
  final bool? isEnabled;

  const FilterAppsEvent({
    this.type,
    this.status,
    this.isEnabled,
  });

  @override
  List<Object?> get props => [type, status, isEnabled];
}

class SortAppsEvent extends AppManagerEvent {
  final AppSortType sortType;
  final bool ascending;

  const SortAppsEvent({
    required this.sortType,
    this.ascending = true,
  });

  @override
  List<Object?> get props => [sortType, ascending];
}

class UninstallAppEvent extends AppManagerEvent {
  final String packageName;

  const UninstallAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class DisableAppEvent extends AppManagerEvent {
  final String packageName;

  const DisableAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class EnableAppEvent extends AppManagerEvent {
  final String packageName;

  const EnableAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class ClearAppDataEvent extends AppManagerEvent {
  final String packageName;

  const ClearAppDataEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class ClearAppCacheEvent extends AppManagerEvent {
  final String packageName;

  const ClearAppCacheEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class ForceStopAppEvent extends AppManagerEvent {
  final String packageName;

  const ForceStopAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class LaunchAppEvent extends AppManagerEvent {
  final String packageName;

  const LaunchAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class StartBatchOperationEvent extends AppManagerEvent {
  final AppBatchOperationType type;
  final List<String> packageNames;

  const StartBatchOperationEvent({
    required this.type,
    required this.packageNames,
  });

  @override
  List<Object?> get props => [type, packageNames];
}

class CancelBatchOperationEvent extends AppManagerEvent {
  final String operationId;

  const CancelBatchOperationEvent(this.operationId);

  @override
  List<Object?> get props => [operationId];
}

class LoadAppUsageStatsEvent extends AppManagerEvent {
  final String? packageName;

  const LoadAppUsageStatsEvent({this.packageName});

  @override
  List<Object?> get props => [packageName];
}

class LoadAppAnalysisEvent extends AppManagerEvent {
  const LoadAppAnalysisEvent();
}

class SelectAppEvent extends AppManagerEvent {
  final String packageName;
  final bool isSelected;

  const SelectAppEvent({
    required this.packageName,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [packageName, isSelected];
}

class SelectAllAppsEvent extends AppManagerEvent {
  final bool isSelected;

  const SelectAllAppsEvent(this.isSelected);

  @override
  List<Object?> get props => [isSelected];
}

class InstallApkEvent extends AppManagerEvent {
  final String apkPath;

  const InstallApkEvent(this.apkPath);

  @override
  List<Object?> get props => [apkPath];
}

class BackupAppEvent extends AppManagerEvent {
  final String packageName;
  final String backupPath;

  const BackupAppEvent({
    required this.packageName,
    required this.backupPath,
  });

  @override
  List<Object?> get props => [packageName, backupPath];
}

class RestoreAppEvent extends AppManagerEvent {
  final String backupPath;

  const RestoreAppEvent(this.backupPath);

  @override
  List<Object?> get props => [backupPath];
}

enum AppSortType {
  name,
  size,
  installDate,
  lastUsed,
  packageName,
}
