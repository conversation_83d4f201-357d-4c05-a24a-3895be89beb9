import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/search_bloc.dart';
import '../bloc/search_state.dart';
import '../../../file_browser/presentation/widgets/file_item_tile.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class SearchResultsWidget extends StatelessWidget {
  const SearchResultsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        if (state is SearchInitial) {
          return const EmptySearchState();
        }

        if (state is SearchLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: AppConstants.defaultPadding),
                Text('Searching files...'),
              ],
            ),
          );
        }

        if (state is SearchInProgress) {
          return _buildSearchResults(
            context,
            state.partialResult,
            isInProgress: true,
          );
        }

        if (state is SearchCompleted) {
          return _buildSearchResults(context, state.result);
        }

        if (state is SearchError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Search Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Retry last search
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return const EmptySearchState();
      },
    );
  }

  Widget _buildSearchResults(
    BuildContext context,
    dynamic result, {
    bool isInProgress = false,
  }) {
    if (result.files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Results Found',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Try adjusting your search terms or filters',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Search Results Header
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${result.totalResults} results found',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (!isInProgress)
                      Text(
                        'Search completed in ${result.searchDuration.inMilliseconds}ms',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              if (isInProgress) ...[
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Searching...',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
              IconButton(
                icon: const Icon(Icons.sort),
                onPressed: () => _showSortOptions(context),
              ),
            ],
          ),
        ),

        // Results List
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            itemCount: result.files.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final file = result.files[index];
              return FileItemTile(
                file: file,
                onTap: () => _handleFileTap(context, file),
                onLongPress: () => _showFileOptions(context, file),
              );
            },
          ),
        ),
      ],
    );
  }

  void _handleFileTap(BuildContext context, dynamic file) {
    // TODO: Handle file tap (open file or navigate to directory)
    if (file.isDirectory) {
      // Navigate to file browser with this directory
    } else {
      // Open file
    }
  }

  void _showFileOptions(BuildContext context, dynamic file) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.folder_open),
                  title: const Text('Show in Folder'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Navigate to parent folder
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('Properties'),
                  onTap: () {
                    Navigator.pop(context);
                    _showFileProperties(context, file);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share),
                  title: const Text('Share'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Share file
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showFileProperties(BuildContext context, dynamic file) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('File Properties'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPropertyRow('Name', file.name),
                _buildPropertyRow('Path', file.path),
                _buildPropertyRow('Size', FileUtils.formatFileSize(file.size)),
                _buildPropertyRow('Modified', file.modifiedDate.toString()),
                if (file.mimeType != null)
                  _buildPropertyRow('Type', file.mimeType!),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildPropertyRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Sort Results',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ListTile(
                  leading: const Icon(Icons.sort_by_alpha),
                  title: const Text('Name (A-Z)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by name ascending
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.sort_by_alpha),
                  title: const Text('Name (Z-A)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by name descending
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.data_usage),
                  title: const Text('Size (Small to Large)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by size ascending
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.data_usage),
                  title: const Text('Size (Large to Small)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by size descending
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.access_time),
                  title: const Text('Date Modified (Newest)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by date descending
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.access_time),
                  title: const Text('Date Modified (Oldest)'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Sort by date ascending
                  },
                ),
              ],
            ),
          ),
    );
  }
}

class EmptySearchState extends StatelessWidget {
  const EmptySearchState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Search for Files',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Enter a search term to find files and folders',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
