import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/search_bloc.dart';
import '../bloc/search_event.dart';
import '../bloc/search_state.dart';
import '../../domain/entities/search_filter.dart';
import '../../../../core/constants/app_constants.dart';

class SearchBarWidget extends StatefulWidget {
  const SearchBarWidget({super.key});

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  SearchFilter _currentFilter = const SearchFilter();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SearchBloc, SearchState>(
      listener: (context, state) {
        if (state is SearchFilterUpdated) {
          _currentFilter = state.filter;
        }
      },
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: 'Search files and folders...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_controller.text.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          setState(() {});
                        },
                      ),
                    IconButton(
                      icon: const Icon(Icons.tune),
                      onPressed: _showAdvancedFilters,
                    ),
                  ],
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                filled: true,
                fillColor: Theme.of(context).cardColor,
              ),
              onChanged: (value) {
                setState(() {});
                // TODO: Implement real-time search suggestions
              },
              onSubmitted: _performSearch,
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          ElevatedButton.icon(
            onPressed: _controller.text.isNotEmpty ? () => _performSearch(_controller.text) : null,
            icon: const Icon(Icons.search),
            label: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    final filter = _currentFilter.copyWith(query: query.trim());
    
    context.read<SearchBloc>().add(StartSearchEvent(filter));
    
    // Save to history
    context.read<SearchBloc>().add(SaveSearchToHistoryEvent(filter));
    
    // Unfocus the text field
    _focusNode.unfocus();
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => AdvancedFiltersSheet(
          currentFilter: _currentFilter,
          scrollController: scrollController,
          onFilterChanged: (filter) {
            setState(() {
              _currentFilter = filter;
            });
            context.read<SearchBloc>().add(UpdateSearchFilterEvent(filter));
          },
        ),
      ),
    );
  }
}

class AdvancedFiltersSheet extends StatefulWidget {
  final SearchFilter currentFilter;
  final ScrollController scrollController;
  final Function(SearchFilter) onFilterChanged;

  const AdvancedFiltersSheet({
    super.key,
    required this.currentFilter,
    required this.scrollController,
    required this.onFilterChanged,
  });

  @override
  State<AdvancedFiltersSheet> createState() => _AdvancedFiltersSheetState();
}

class _AdvancedFiltersSheetState extends State<AdvancedFiltersSheet> {
  late SearchFilter _filter;

  @override
  void initState() {
    super.initState();
    _filter = widget.currentFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppConstants.defaultBorderRadius),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Title
          Text(
            'Advanced Search Filters',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          Expanded(
            child: ListView(
              controller: widget.scrollController,
              children: [
                // File Types
                _buildFileTypesSection(),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Size Range
                _buildSizeRangeSection(),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Date Range
                _buildDateRangeSection(),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Search Options
                _buildSearchOptionsSection(),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Search Depth
                _buildSearchDepthSection(),
              ],
            ),
          ),
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset'),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFileTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'File Types',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children: SearchFileType.values.map((type) {
            final isSelected = _filter.fileTypes.contains(type);
            return FilterChip(
              label: Text(_getFileTypeName(type)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _filter = _filter.copyWith(
                      fileTypes: [..._filter.fileTypes, type],
                    );
                  } else {
                    _filter = _filter.copyWith(
                      fileTypes: _filter.fileTypes.where((t) => t != type).toList(),
                    );
                  }
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSizeRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'File Size',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Min Size (MB)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: _filter.minSize != null 
                    ? (_filter.minSize! / (1024 * 1024)).toString()
                    : '',
                onChanged: (value) {
                  final size = double.tryParse(value);
                  setState(() {
                    _filter = _filter.copyWith(
                      minSize: size != null ? (size * 1024 * 1024).round() : null,
                    );
                  });
                },
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Max Size (MB)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                initialValue: _filter.maxSize != null 
                    ? (_filter.maxSize! / (1024 * 1024)).toString()
                    : '',
                onChanged: (value) {
                  final size = double.tryParse(value);
                  setState(() {
                    _filter = _filter.copyWith(
                      maxSize: size != null ? (size * 1024 * 1024).round() : null,
                    );
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Modified Date',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(true),
                icon: const Icon(Icons.calendar_today),
                label: Text(_filter.modifiedAfter != null 
                    ? 'After: ${_formatDate(_filter.modifiedAfter!)}'
                    : 'After Date'),
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(false),
                icon: const Icon(Icons.calendar_today),
                label: Text(_filter.modifiedBefore != null 
                    ? 'Before: ${_formatDate(_filter.modifiedBefore!)}'
                    : 'Before Date'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Options',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        CheckboxListTile(
          title: const Text('Case Sensitive'),
          value: _filter.caseSensitive,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(caseSensitive: value ?? false);
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('Use Regular Expressions'),
          value: _filter.useRegex,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(useRegex: value ?? false);
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('Include Hidden Files'),
          value: _filter.includeHidden,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(includeHidden: value ?? false);
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('Include System Files'),
          value: _filter.includeSystemFiles,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(includeSystemFiles: value ?? false);
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildSearchDepthSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Depth',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children: SearchDepth.values.map((depth) {
            return ChoiceChip(
              label: Text(_getDepthName(depth)),
              selected: _filter.depth == depth,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _filter = _filter.copyWith(depth: depth);
                  });
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getFileTypeName(SearchFileType type) {
    switch (type) {
      case SearchFileType.all:
        return 'All';
      case SearchFileType.images:
        return 'Images';
      case SearchFileType.videos:
        return 'Videos';
      case SearchFileType.audio:
        return 'Audio';
      case SearchFileType.documents:
        return 'Documents';
      case SearchFileType.archives:
        return 'Archives';
      case SearchFileType.applications:
        return 'Apps';
      case SearchFileType.folders:
        return 'Folders';
    }
  }

  String _getDepthName(SearchDepth depth) {
    switch (depth) {
      case SearchDepth.currentFolder:
        return 'Current Folder';
      case SearchDepth.oneLevel:
        return 'One Level';
      case SearchDepth.twoLevels:
        return 'Two Levels';
      case SearchDepth.unlimited:
        return 'Unlimited';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate(bool isAfter) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isAfter) {
          _filter = _filter.copyWith(modifiedAfter: date);
        } else {
          _filter = _filter.copyWith(modifiedBefore: date);
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _filter = const SearchFilter();
    });
  }

  void _applyFilters() {
    widget.onFilterChanged(_filter);
    Navigator.of(context).pop();
  }
}
