import 'package:equatable/equatable.dart';
import '../../domain/entities/file_item.dart';
import 'file_browser_event.dart';

abstract class FileBrowserState extends Equatable {
  const FileBrowserState();

  @override
  List<Object?> get props => [];
}

class File<PERSON>rowserInitial extends FileBrowserState {
  const FileBrowserInitial();
}

class FileBrowserLoading extends FileBrowserState {
  const FileBrowserLoading();
}

class FileBrowserLoaded extends FileBrowserState {
  final List<FileItem> files;
  final String currentPath;
  final List<String> navigationHistory;
  final bool showHiddenFiles;
  final SortOrder sortOrder;
  final ViewMode viewMode;
  final bool canNavigateBack;

  const FileBrowserLoaded({
    required this.files,
    required this.currentPath,
    required this.navigationHistory,
    this.showHiddenFiles = false,
    this.sortOrder = SortOrder.nameAsc,
    this.viewMode = ViewMode.list,
    this.canNavigateBack = false,
  });

  List<FileItem> get filteredFiles {
    var filtered = files;
    
    if (!showHiddenFiles) {
      filtered = filtered.where((file) => !file.isHidden).toList();
    }
    
    // Apply sorting
    switch (sortOrder) {
      case SortOrder.nameAsc:
        filtered.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
        break;
      case SortOrder.nameDesc:
        filtered.sort((a, b) => b.name.toLowerCase().compareTo(a.name.toLowerCase()));
        break;
      case SortOrder.sizeAsc:
        filtered.sort((a, b) => a.size.compareTo(b.size));
        break;
      case SortOrder.sizeDesc:
        filtered.sort((a, b) => b.size.compareTo(a.size));
        break;
      case SortOrder.dateAsc:
        filtered.sort((a, b) => a.modifiedDate.compareTo(b.modifiedDate));
        break;
      case SortOrder.dateDesc:
        filtered.sort((a, b) => b.modifiedDate.compareTo(a.modifiedDate));
        break;
      case SortOrder.typeAsc:
        filtered.sort((a, b) {
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return (a.extension ?? '').compareTo(b.extension ?? '');
        });
        break;
      case SortOrder.typeDesc:
        filtered.sort((a, b) {
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return (b.extension ?? '').compareTo(a.extension ?? '');
        });
        break;
    }
    
    return filtered;
  }

  FileBrowserLoaded copyWith({
    List<FileItem>? files,
    String? currentPath,
    List<String>? navigationHistory,
    bool? showHiddenFiles,
    SortOrder? sortOrder,
    ViewMode? viewMode,
    bool? canNavigateBack,
  }) {
    return FileBrowserLoaded(
      files: files ?? this.files,
      currentPath: currentPath ?? this.currentPath,
      navigationHistory: navigationHistory ?? this.navigationHistory,
      showHiddenFiles: showHiddenFiles ?? this.showHiddenFiles,
      sortOrder: sortOrder ?? this.sortOrder,
      viewMode: viewMode ?? this.viewMode,
      canNavigateBack: canNavigateBack ?? this.canNavigateBack,
    );
  }

  @override
  List<Object?> get props => [
        files,
        currentPath,
        navigationHistory,
        showHiddenFiles,
        sortOrder,
        viewMode,
        canNavigateBack,
      ];
}

class FileBrowserError extends FileBrowserState {
  final String message;

  const FileBrowserError(this.message);

  @override
  List<Object?> get props => [message];
}

class FileOperationInProgress extends FileBrowserState {
  final String operation;
  final String fileName;

  const FileOperationInProgress(this.operation, this.fileName);

  @override
  List<Object?> get props => [operation, fileName];
}

class FileOperationSuccess extends FileBrowserState {
  final String message;

  const FileOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class FileOperationError extends FileBrowserState {
  final String message;

  const FileOperationError(this.message);

  @override
  List<Object?> get props => [message];
}
