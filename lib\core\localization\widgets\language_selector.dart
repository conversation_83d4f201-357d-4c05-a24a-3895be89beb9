import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../models/language.dart';
import '../bloc/language_bloc.dart';
import '../bloc/language_event.dart';
import '../bloc/language_state.dart';
import '../extensions/translation_extensions.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// ويدجت اختيار اللغة
class LanguageSelector extends StatelessWidget {
  final bool showFlags;
  final bool showNativeNames;
  final bool isCompact;
  final VoidCallback? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.showFlags = true,
    this.showNativeNames = true,
    this.isCompact = false,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        Language currentLanguage = SupportedLanguages.defaultLanguage;

        if (state is LanguageLoaded) {
          currentLanguage = state.language;
        } else if (state is LanguageChanged) {
          currentLanguage = state.currentLanguage;
        }

        if (isCompact) {
          return _buildCompactSelector(context, currentLanguage);
        } else {
          return _buildFullSelector(context, currentLanguage);
        }
      },
    );
  }

  Widget _buildCompactSelector(BuildContext context, Language currentLanguage) {
    return PopupMenuButton<Language>(
      initialValue: currentLanguage,
      onSelected: (language) => _changeLanguage(context, language),
      itemBuilder:
          (context) =>
              SupportedLanguages.primary
                  .map(
                    (language) => PopupMenuItem<Language>(
                      value: language,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (showFlags) ...[
                            Text(
                              language.flag,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            showNativeNames
                                ? language.nativeName
                                : language.name,
                            style: AppTextStyles.bodyMedium,
                          ),
                          if (language == currentLanguage) ...[
                            const SizedBox(width: 8),
                            Icon(
                              Icons.check,
                              color: AppColors.primary,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                    ),
                  )
                  .toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.outline.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showFlags) ...[
              Text(currentLanguage.flag, style: const TextStyle(fontSize: 18)),
              const SizedBox(width: 8),
            ],
            Text(
              showNativeNames
                  ? currentLanguage.nativeName
                  : currentLanguage.name,
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(width: 4),
            Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
          ],
        ),
      ),
    );
  }

  Widget _buildFullSelector(BuildContext context, Language currentLanguage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('settings.language'),
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...SupportedLanguages.primary.map(
          (language) =>
              _buildLanguageOption(context, language, currentLanguage),
        ),
      ],
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    Language language,
    Language currentLanguage,
  ) {
    final isSelected = language == currentLanguage;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _changeLanguage(context, language),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    isSelected
                        ? AppColors.primary.withValues(alpha: 0.3)
                        : AppColors.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                if (showFlags) ...[
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        language.flag,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        showNativeNames ? language.nativeName : language.name,
                        style: AppTextStyles.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected ? AppColors.primary : null,
                        ),
                      ),
                      if (showNativeNames &&
                          language.name != language.nativeName) ...[
                        const SizedBox(height: 2),
                        Text(
                          language.name,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  language.isRTL
                                      ? Colors.orange.withValues(alpha: 0.1)
                                      : Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              language.isRTL ? 'RTL' : 'LTR',
                              style: AppTextStyles.bodySmall.copyWith(
                                color:
                                    language.isRTL
                                        ? Colors.orange[700]
                                        : Colors.blue[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            language.locale.toString(),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (isSelected) ...[
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ] else ...[
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.outline.withValues(alpha: 0.3),
                      ),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _changeLanguage(BuildContext context, Language language) {
    context.read<LanguageBloc>().add(ChangeLanguageEvent(language));
    onLanguageChanged?.call();
  }
}

/// ويدجت مبسط لاختيار اللغة في شريط التطبيق
class LanguageSelectorAppBar extends StatelessWidget {
  const LanguageSelectorAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        Language currentLanguage = SupportedLanguages.defaultLanguage;

        if (state is LanguageLoaded) {
          currentLanguage = state.language;
        } else if (state is LanguageChanged) {
          currentLanguage = state.currentLanguage;
        }

        return PopupMenuButton<Language>(
          initialValue: currentLanguage,
          onSelected: (language) {
            context.read<LanguageBloc>().add(ChangeLanguageEvent(language));
          },
          itemBuilder:
              (context) =>
                  SupportedLanguages.primary
                      .map(
                        (language) => PopupMenuItem<Language>(
                          value: language,
                          child: Row(
                            children: [
                              Text(
                                language.flag,
                                style: const TextStyle(fontSize: 18),
                              ),
                              const SizedBox(width: 12),
                              Text(language.nativeName),
                              if (language == currentLanguage) ...[
                                const Spacer(),
                                Icon(
                                  Icons.check,
                                  color: AppColors.primary,
                                  size: 16,
                                ),
                              ],
                            ],
                          ),
                        ),
                      )
                      .toList(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  currentLanguage.flag,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.arrow_drop_down, size: 16),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// ويدجت لعرض معلومات اللغة الحالية
class LanguageInfo extends StatelessWidget {
  const LanguageInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        Language currentLanguage = SupportedLanguages.defaultLanguage;

        if (state is LanguageLoaded) {
          currentLanguage = state.language;
        } else if (state is LanguageChanged) {
          currentLanguage = state.currentLanguage;
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      currentLanguage.flag,
                      style: const TextStyle(fontSize: 32),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            currentLanguage.nativeName,
                            style: AppTextStyles.headlineSmall.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            currentLanguage.name,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildInfoRow(
                  context,
                  'Code',
                  currentLanguage.code.toUpperCase(),
                ),
                _buildInfoRow(
                  context,
                  'Locale',
                  currentLanguage.locale.toString(),
                ),
                _buildInfoRow(
                  context,
                  'Direction',
                  currentLanguage.isRTL
                      ? 'Right-to-Left (RTL)'
                      : 'Left-to-Right (LTR)',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(child: Text(value, style: AppTextStyles.bodyMedium)),
        ],
      ),
    );
  }
}
