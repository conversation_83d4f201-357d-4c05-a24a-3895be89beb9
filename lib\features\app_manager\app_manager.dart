// App Manager Feature Export File

// Domain Layer
export 'domain/entities/app_info.dart';
export 'domain/repositories/app_manager_repository.dart';
export 'domain/usecases/get_all_apps.dart';
export 'domain/usecases/manage_app.dart';
export 'domain/usecases/batch_operations.dart';

// Data Layer
export 'data/datasources/app_manager_datasource.dart';
export 'data/repositories/app_manager_repository_impl.dart';

// Presentation Layer
export 'presentation/bloc/app_manager_bloc.dart';
export 'presentation/bloc/app_manager_event.dart';
export 'presentation/bloc/app_manager_state.dart';
export 'presentation/pages/app_manager_page.dart';
export 'presentation/widgets/app_list_widget.dart';
export 'presentation/widgets/app_filters_widget.dart';
export 'presentation/widgets/app_analysis_widget.dart';
export 'presentation/widgets/batch_operations_widget.dart';

// Dependency Injection
export 'app_manager_injection.dart';
