import '../../../../core/utils/typedef.dart';
import '../entities/cleanup_item.dart';

abstract class CleanerRepository {
  // System Analysis
  ResultFuture<SystemAnalysis> analyzeSystem();
  Stream<SystemAnalysis> analyzeSystemStream();
  ResultVoid cancelAnalysis();
  
  // Cleanup Operations
  ResultFuture<CleanupResult> performCleanup(List<CleanupItem> items);
  Stream<CleanupResult> performCleanupStream(List<CleanupItem> items);
  ResultVoid cancelCleanup();
  
  // Specific Cleanup Types
  ResultFuture<List<CleanupItem>> findCacheFiles();
  ResultFuture<List<CleanupItem>> findTemporaryFiles();
  ResultFuture<List<CleanupItem>> findLogFiles();
  ResultFuture<List<CleanupItem>> findThumbnails();
  ResultFuture<List<CleanupItem>> findEmptyFolders();
  ResultFuture<List<CleanupItem>> findLargeFiles(int minSizeMB);
  ResultFuture<List<CleanupItem>> findOldFiles(int daysOld);
  ResultFuture<List<CleanupItem>> findApkFiles();
  ResultFuture<List<CleanupItem>> findResidualFiles();
  
  // Storage Analysis
  ResultFuture<Map<String, int>> analyzeLargestDirectories();
  ResultFuture<Map<String, int>> analyzeFileTypeDistribution();
  ResultFuture<int> calculateDirectorySize(String path);
  
  // Cleanup History
  ResultFuture<List<CleanupResult>> getCleanupHistory();
  ResultVoid saveCleanupResult(CleanupResult result);
  ResultVoid clearCleanupHistory();
}
