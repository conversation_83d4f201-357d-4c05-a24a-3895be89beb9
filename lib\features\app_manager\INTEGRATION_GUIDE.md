# App Manager Integration Guide

This guide explains how to integrate the App Manager feature into your HD File Explorer application.

## 1. Dependencies Setup

First, ensure you have the required dependencies in your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  injectable: ^2.3.2
  get_it: ^7.6.4
  dartz: ^0.10.1
  equatable: ^2.0.5
  uuid: ^4.1.0
  fl_chart: ^0.64.0

dev_dependencies:
  injectable_generator: ^2.4.1
  build_runner: ^2.4.7
```

## 2. Dependency Injection Setup

The App Manager feature is already integrated into the dependency injection system. The setup is done in:

- `lib/features/app_manager/app_manager_injection.dart` - Feature-specific DI setup
- `lib/core/di/injection_container.dart` - Main DI container (already updated)

## 3. Navigation Integration

### Option A: Direct Navigation (Recommended)

Add App Manager to your dashboard's quick access grid (already implemented):

```dart
// In QuickAccessGrid widget
case QuickAccessType.apps:
  Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => const AppManagerPage()),
  );
  break;
```

### Option B: Bottom Navigation Integration

To add App Manager as a main tab in your bottom navigation:

```dart
// In lib/app.dart - MainNavigationPage
final List<Widget> _pages = [
  const DashboardPage(),
  const FileBrowserPage(),
  const AppManagerPage(), // Add this line
  const SearchPage(),
  const FileOperationsPage(),
  const CleanerPage(),
];

// Update bottom navigation items
items: const [
  BottomNavigationBarItem(
    icon: Icon(Icons.dashboard),
    label: 'Dashboard',
  ),
  BottomNavigationBarItem(icon: Icon(Icons.folder), label: 'Files'),
  BottomNavigationBarItem(icon: Icon(Icons.apps), label: 'Apps'), // Add this
  BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Search'),
  BottomNavigationBarItem(
    icon: Icon(Icons.settings),
    label: 'Operations',
  ),
  BottomNavigationBarItem(
    icon: Icon(Icons.cleaning_services),
    label: 'Cleaner',
  ),
],
```

### Option C: Drawer Integration

Add App Manager to a navigation drawer:

```dart
Drawer(
  child: ListView(
    children: [
      const DrawerHeader(
        decoration: BoxDecoration(color: Colors.blue),
        child: Text('HD File Explorer'),
      ),
      ListTile(
        leading: const Icon(Icons.dashboard),
        title: const Text('Dashboard'),
        onTap: () => Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const DashboardPage()),
        ),
      ),
      ListTile(
        leading: const Icon(Icons.apps),
        title: const Text('App Manager'),
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AppManagerPage()),
        ),
      ),
      // ... other items
    ],
  ),
)
```

## 4. Custom Integration Examples

### Basic Usage

```dart
import 'package:your_app/features/app_manager/app_manager.dart';

// Navigate to App Manager
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const AppManagerPage()),
);
```

### Custom App List

```dart
BlocProvider(
  create: (context) => sl<AppManagerBloc>()..add(const LoadAllAppsEvent()),
  child: BlocBuilder<AppManagerBloc, AppManagerState>(
    builder: (context, state) {
      if (state is AppsLoaded) {
        return ListView.builder(
          itemCount: state.filteredApps.length,
          itemBuilder: (context, index) {
            final app = state.filteredApps[index];
            return ListTile(
              title: Text(app.appName),
              subtitle: Text(app.packageName),
              trailing: Text(app.displaySize),
            );
          },
        );
      }
      return const CircularProgressIndicator();
    },
  ),
)
```

### Batch Operations

```dart
// Start batch uninstall
context.read<AppManagerBloc>().add(
  StartBatchOperationEvent(
    type: AppBatchOperationType.uninstall,
    packageNames: ['com.example.app1', 'com.example.app2'],
  ),
);

// Listen for batch operation progress
BlocListener<AppManagerBloc, AppManagerState>(
  listener: (context, state) {
    if (state is BatchOperationProgress) {
      // Update progress UI
      print('Progress: ${state.operation.progress}');
    } else if (state is BatchOperationCompleted) {
      // Show completion message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Operation completed')),
      );
    }
  },
  child: YourWidget(),
)
```

## 5. Customization Options

### Theme Integration

The App Manager uses your app's theme automatically. To customize colors:

```dart
// In your app theme
ThemeData(
  primarySwatch: Colors.blue,
  // App Manager will use these colors automatically
)
```

### Custom App Actions

Extend app actions by modifying the `AppAction` enum and handling in `AppListWidget`:

```dart
enum AppAction {
  launch,
  uninstall,
  disable,
  enable,
  clearData,
  clearCache,
  forceStop,
  details,
  backup, // Add custom action
}
```

### Custom Filters

Add custom filters by extending the `FilterAppsEvent`:

```dart
context.read<AppManagerBloc>().add(
  FilterAppsEvent(
    type: AppType.user,
    status: AppStatus.running,
    isEnabled: true,
  ),
);
```

## 6. Error Handling

The App Manager includes comprehensive error handling:

```dart
BlocListener<AppManagerBloc, AppManagerState>(
  listener: (context, state) {
    if (state is AppOperationError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
        ),
      );
    } else if (state is AppManagerError) {
      // Handle general errors
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text(state.message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  },
  child: YourWidget(),
)
```

## 7. Testing

The feature includes comprehensive testing support:

```dart
// Unit test example
testWidgets('App Manager loads apps correctly', (tester) async {
  final mockBloc = MockAppManagerBloc();
  
  when(() => mockBloc.state).thenReturn(
    AppsLoaded(
      apps: [mockApp],
      filteredApps: [mockApp],
    ),
  );
  
  await tester.pumpWidget(
    BlocProvider<AppManagerBloc>.value(
      value: mockBloc,
      child: const AppManagerPage(),
    ),
  );
  
  expect(find.text('Mock App'), findsOneWidget);
});
```

## 8. Performance Considerations

- Apps are loaded lazily to improve startup time
- Large app lists are efficiently handled with ListView.builder
- Image loading is optimized for app icons
- Batch operations run in background to prevent UI blocking

## 9. Security Notes

- All app operations require appropriate permissions
- Package names are validated before operations
- Sensitive operations show confirmation dialogs
- System apps have restricted operations

## 10. Troubleshooting

### Common Issues

1. **Apps not loading**: Check if the data source is properly initialized
2. **Operations failing**: Verify app permissions and system restrictions
3. **UI not updating**: Ensure BlocProvider is properly set up
4. **Memory issues**: Check for proper stream disposal in BLoC

### Debug Mode

Enable debug logging by setting:

```dart
// In main.dart
void main() {
  // Enable debug logging
  if (kDebugMode) {
    // Add logging configuration
  }
  
  runApp(const HDFileExplorerApp());
}
```

## 11. Future Enhancements

The App Manager is designed to be extensible. Future enhancements could include:

- Real Android system integration
- Cloud backup synchronization
- Advanced analytics
- Multi-device management
- Integration with package managers

For more examples, see `lib/features/app_manager/example_usage.dart`.
