import 'package:equatable/equatable.dart';
import '../../../file_browser/domain/entities/file_item.dart';

class SearchResult extends Equatable {
  final List<FileItem> files;
  final String query;
  final SearchFilter filter;
  final int totalResults;
  final Duration searchDuration;

  const SearchResult({
    required this.files,
    required this.query,
    required this.filter,
    required this.totalResults,
    required this.searchDuration,
  });

  @override
  List<Object?> get props => [files, query, filter, totalResults, searchDuration];
}

class SearchFilter extends Equatable {
  final String? fileType;
  final int? minSize;
  final int? maxSize;
  final DateTime? modifiedAfter;
  final DateTime? modifiedBefore;
  final bool includeHidden;
  final bool includeSystemFiles;
  final List<String>? extensions;

  const SearchFilter({
    this.fileType,
    this.minSize,
    this.maxSize,
    this.modifiedAfter,
    this.modifiedBefore,
    this.includeHidden = false,
    this.includeSystemFiles = false,
    this.extensions,
  });

  SearchFilter copyWith({
    String? fileType,
    int? minSize,
    int? maxSize,
    DateTime? modifiedAfter,
    DateTime? modifiedBefore,
    bool? includeHidden,
    bool? includeSystemFiles,
    List<String>? extensions,
  }) {
    return SearchFilter(
      fileType: fileType ?? this.fileType,
      minSize: minSize ?? this.minSize,
      maxSize: maxSize ?? this.maxSize,
      modifiedAfter: modifiedAfter ?? this.modifiedAfter,
      modifiedBefore: modifiedBefore ?? this.modifiedBefore,
      includeHidden: includeHidden ?? this.includeHidden,
      includeSystemFiles: includeSystemFiles ?? this.includeSystemFiles,
      extensions: extensions ?? this.extensions,
    );
  }

  @override
  List<Object?> get props => [
        fileType,
        minSize,
        maxSize,
        modifiedAfter,
        modifiedBefore,
        includeHidden,
        includeSystemFiles,
        extensions,
      ];
}

class DuplicateFile extends Equatable {
  final List<FileItem> duplicates;
  final int totalSize;
  final String hash;

  const DuplicateFile({
    required this.duplicates,
    required this.totalSize,
    required this.hash,
  });

  int get duplicateCount => duplicates.length;
  int get wastedSpace => totalSize * (duplicateCount - 1);

  @override
  List<Object?> get props => [duplicates, totalSize, hash];
}
