import 'package:equatable/equatable.dart';
import '../../../file_browser/domain/entities/file_item.dart';
import 'search_filter.dart';

class SearchResult extends Equatable {
  final List<FileItem> files;
  final String query;
  final SearchFilter filter;
  final int totalResults;
  final Duration searchDuration;
  final bool isComplete;
  final String? errorMessage;

  const SearchResult({
    required this.files,
    required this.query,
    required this.filter,
    required this.totalResults,
    required this.searchDuration,
    this.isComplete = true,
    this.errorMessage,
  });

  SearchResult copyWith({
    List<FileItem>? files,
    String? query,
    SearchFilter? filter,
    int? totalResults,
    Duration? searchDuration,
    bool? isComplete,
    String? errorMessage,
  }) {
    return SearchResult(
      files: files ?? this.files,
      query: query ?? this.query,
      filter: filter ?? this.filter,
      totalResults: totalResults ?? this.totalResults,
      searchDuration: searchDuration ?? this.searchDuration,
      isComplete: isComplete ?? this.isComplete,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    files,
    query,
    filter,
    totalResults,
    searchDuration,
    isComplete,
    errorMessage,
  ];
}

class DuplicateGroup extends Equatable {
  final String id;
  final List<FileItem> duplicates;
  final int totalSize;
  final String? hash;
  final DuplicateSearchMethod detectionMethod;

  const DuplicateGroup({
    required this.id,
    required this.duplicates,
    required this.totalSize,
    this.hash,
    required this.detectionMethod,
  });

  int get duplicateCount => duplicates.length;
  int get wastedSpace => totalSize * (duplicateCount - 1);

  FileItem get originalFile => duplicates.first;
  List<FileItem> get duplicateFiles => duplicates.skip(1).toList();

  @override
  List<Object?> get props => [id, duplicates, totalSize, hash, detectionMethod];
}

class DuplicateSearchResult extends Equatable {
  final List<DuplicateGroup> duplicateGroups;
  final int totalDuplicates;
  final int totalWastedSpace;
  final Duration searchDuration;
  final DuplicateSearchFilter filter;
  final bool isComplete;
  final String? errorMessage;

  const DuplicateSearchResult({
    required this.duplicateGroups,
    required this.totalDuplicates,
    required this.totalWastedSpace,
    required this.searchDuration,
    required this.filter,
    this.isComplete = true,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
    duplicateGroups,
    totalDuplicates,
    totalWastedSpace,
    searchDuration,
    filter,
    isComplete,
    errorMessage,
  ];
}
