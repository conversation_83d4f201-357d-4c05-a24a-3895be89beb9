import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../bloc/root_tools_bloc.dart';
import '../bloc/root_tools_event.dart';
import '../bloc/root_tools_state.dart';
import '../widgets/root_access_card.dart';
import '../widgets/system_info_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../widgets/system_monitoring_card.dart';
import '../widgets/recent_commands_card.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/loading_overlay.dart';
import '../../../../core/widgets/error_dialog.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

class RootToolsPage extends StatefulWidget {
  const RootToolsPage({super.key});

  @override
  State<RootToolsPage> createState() => _RootToolsPageState();
}

class _RootToolsPageState extends State<RootToolsPage>
    with TickerProviderStateMixin {
  late RootToolsBloc _bloc;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _bloc = GetIt.instance<RootToolsBloc>();
    _tabController = TabController(length: 5, vsync: this);

    // Check root access on page load
    _bloc.add(const CheckRootAccessEvent());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'أدوات الروت',
          subtitle: 'إدارة متقدمة للنظام',
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _bloc.add(const CheckRootAccessEvent()),
              tooltip: 'تحديث حالة الروت',
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _showSettingsDialog(),
              tooltip: 'الإعدادات',
            ),
          ],
        ),
        body: BlocConsumer<RootToolsBloc, RootToolsState>(
          listener: _handleStateChanges,
          builder: (context, state) {
            return LoadingOverlay(
              isLoading: _isLoadingState(state),
              loadingMessage: _getLoadingMessage(state),
              child: Column(
                children: [
                  // Root Access Status Card
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: RootAccessCard(
                      onRequestRoot:
                          () => _bloc.add(const RequestRootPermissionEvent()),
                      onRevokeRoot:
                          () => _bloc.add(const RevokeRootPermissionEvent()),
                    ),
                  ),

                  // Tab Bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      labelColor: AppColors.primary,
                      unselectedLabelColor: AppColors.textSecondary,
                      indicatorColor: AppColors.primary,
                      labelStyle: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      tabs: const [
                        Tab(text: 'نظرة عامة'),
                        Tab(text: 'العمليات'),
                        Tab(text: 'الخدمات'),
                        Tab(text: 'الأمان'),
                        Tab(text: 'الأدوات'),
                      ],
                    ),
                  ),

                  // Tab Views
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildProcessesTab(),
                        _buildServicesTab(),
                        _buildSecurityTab(),
                        _buildToolsTab(),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System Information Card
          const SystemInfoCard(),
          const SizedBox(height: 16),

          // Quick Actions Grid
          const QuickActionsGrid(),
          const SizedBox(height: 16),

          // System Monitoring Card
          const SystemMonitoringCard(),
          const SizedBox(height: 16),

          // Recent Commands Card
          const RecentCommandsCard(),
        ],
      ),
    );
  }

  Widget _buildProcessesTab() {
    return Column(
      children: [
        // Process Controls
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: AppColors.surface,
            border: Border(
              bottom: BorderSide(
                color: AppColors.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'البحث في العمليات...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () => _bloc.add(const LoadRunningProcessesEvent()),
                tooltip: 'تحديث العمليات',
              ),
              IconButton(
                icon: const Icon(Icons.sort),
                onPressed: () => _showSortDialog(),
                tooltip: 'ترتيب',
              ),
            ],
          ),
        ),

        // Process List
        Expanded(
          child: BlocBuilder<RootToolsBloc, RootToolsState>(
            builder: (context, state) {
              if (state is RunningProcessesLoaded) {
                return ListView.builder(
                  itemCount: state.processes.length,
                  itemBuilder: (context, index) {
                    final process = state.processes[index];
                    return _buildProcessTile(process);
                  },
                );
              } else if (state is RootToolsLoading) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.memory,
                        size: 64,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اضغط على تحديث لعرض العمليات',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed:
                            () => _bloc.add(const LoadRunningProcessesEvent()),
                        icon: const Icon(Icons.refresh),
                        label: const Text('تحديث العمليات'),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildServicesTab() {
    return Column(
      children: [
        // Service Controls
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: AppColors.surface,
            border: Border(
              bottom: BorderSide(
                color: AppColors.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'البحث في الخدمات...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () => _bloc.add(const LoadSystemServicesEvent()),
                tooltip: 'تحديث الخدمات',
              ),
            ],
          ),
        ),

        // Service List
        Expanded(
          child: BlocBuilder<RootToolsBloc, RootToolsState>(
            builder: (context, state) {
              if (state is SystemServicesLoaded) {
                return ListView.builder(
                  itemCount: state.services.length,
                  itemBuilder: (context, index) {
                    final service = state.services[index];
                    return _buildServiceTile(service);
                  },
                );
              } else if (state is RootToolsLoading) {
                return const Center(child: CircularProgressIndicator());
              } else {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.miscellaneous_services,
                        size: 64,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اضغط على تحديث لعرض الخدمات',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed:
                            () => _bloc.add(const LoadSystemServicesEvent()),
                        icon: const Icon(Icons.refresh),
                        label: const Text('تحديث الخدمات'),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('أدوات الأمان والخصوصية', style: AppTextStyles.headlineSmall),
          const SizedBox(height: 16),

          // Security Actions
          _buildSecurityActionCard(
            title: 'حظر الإعلانات',
            subtitle: 'حظر الإعلانات على مستوى النظام',
            icon: Icons.block,
            onTap: () => _bloc.add(const BlockAdsEvent()),
          ),

          _buildSecurityActionCard(
            title: 'جدار الحماية',
            subtitle: 'إدارة قواعد جدار الحماية',
            icon: Icons.security,
            onTap: () => _showFirewallDialog(),
          ),

          _buildSecurityActionCard(
            title: 'إزالة تطبيقات النظام',
            subtitle: 'إزالة التطبيقات غير المرغوب فيها',
            icon: Icons.delete_forever,
            onTap: () => _showSystemAppsDialog(),
          ),

          _buildSecurityActionCard(
            title: 'تشفير البيانات',
            subtitle: 'تشفير بيانات المستخدم',
            icon: Icons.lock,
            onTap: () => _showEncryptionDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('أدوات متقدمة', style: AppTextStyles.headlineSmall),
          const SizedBox(height: 16),

          // Advanced Tools
          _buildToolCard(
            title: 'نسخ احتياطي للنظام',
            subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
            icon: Icons.backup,
            onTap: () => _showBackupDialog(),
          ),

          _buildToolCard(
            title: 'إدارة التقسيمات',
            subtitle: 'تركيب وإلغاء تركيب التقسيمات',
            icon: Icons.storage,
            onTap: () => _showPartitionsDialog(),
          ),

          _buildToolCard(
            title: 'تحسين الأداء',
            subtitle: 'تحسين أداء النظام',
            icon: Icons.speed,
            onTap: () => _showPerformanceDialog(),
          ),

          _buildToolCard(
            title: 'سجلات النظام',
            subtitle: 'عرض وتحليل سجلات النظام',
            icon: Icons.article,
            onTap: () => _showLogsDialog(),
          ),

          _buildToolCard(
            title: 'تشخيص النظام',
            subtitle: 'فحص صحة النظام',
            icon: Icons.health_and_safety,
            onTap: () => _bloc.add(const RunSystemDiagnosticsEvent()),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessTile(dynamic process) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Text(
            process.pid.toString(),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          process.name,
          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          'CPU: ${process.cpuUsage.toStringAsFixed(1)}% | RAM: ${(process.memoryUsage / 1024 / 1024).toStringAsFixed(1)} MB',
          style: AppTextStyles.bodySmall,
        ),
        trailing: PopupMenuButton(
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'kill',
                  child: const Row(
                    children: [
                      Icon(Icons.stop, color: Colors.red),
                      SizedBox(width: 8),
                      Text('إنهاء العملية'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'priority',
                  child: const Row(
                    children: [
                      Icon(Icons.priority_high),
                      SizedBox(width: 8),
                      Text('تغيير الأولوية'),
                    ],
                  ),
                ),
              ],
          onSelected: (value) {
            if (value == 'kill') {
              _showKillProcessDialog(process);
            } else if (value == 'priority') {
              _showChangePriorityDialog(process);
            }
          },
        ),
      ),
    );
  }

  Widget _buildServiceTile(dynamic service) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              service.isRunning
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
          child: Icon(
            service.isRunning ? Icons.play_arrow : Icons.stop,
            color: service.isRunning ? Colors.green : Colors.red,
          ),
        ),
        title: Text(
          service.name,
          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          service.description ?? 'لا يوجد وصف',
          style: AppTextStyles.bodySmall,
        ),
        trailing: PopupMenuButton(
          itemBuilder:
              (context) => [
                if (!service.isRunning)
                  PopupMenuItem(
                    value: 'start',
                    child: const Row(
                      children: [
                        Icon(Icons.play_arrow, color: Colors.green),
                        SizedBox(width: 8),
                        Text('تشغيل'),
                      ],
                    ),
                  ),
                if (service.isRunning)
                  PopupMenuItem(
                    value: 'stop',
                    child: const Row(
                      children: [
                        Icon(Icons.stop, color: Colors.red),
                        SizedBox(width: 8),
                        Text('إيقاف'),
                      ],
                    ),
                  ),
                if (service.isRunning)
                  PopupMenuItem(
                    value: 'restart',
                    child: const Row(
                      children: [
                        Icon(Icons.refresh, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('إعادة تشغيل'),
                      ],
                    ),
                  ),
              ],
          onSelected: (value) {
            switch (value) {
              case 'start':
                _bloc.add(StartServiceEvent(service.name));
                break;
              case 'stop':
                _bloc.add(StopServiceEvent(service.name));
                break;
              case 'restart':
                _bloc.add(RestartServiceEvent(service.name));
                break;
            }
          },
        ),
      ),
    );
  }

  Widget _buildSecurityActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Icon(icon, color: AppColors.primary),
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  Widget _buildToolCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.secondary.withValues(alpha: 0.1),
          child: Icon(icon, color: AppColors.secondary),
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => _showCommandDialog(),
      tooltip: 'تنفيذ أمر',
      child: const Icon(Icons.terminal),
    );
  }

  void _handleStateChanges(BuildContext context, RootToolsState state) {
    if (state is RootToolsError) {
      showDialog(
        context: context,
        builder:
            (context) => ErrorDialog(
              title: 'خطأ في أدوات الروت',
              message: state.message,
            ),
      );
    } else if (state is RootPermissionGranted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم منح صلاحيات الروت بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (state is RootPermissionRevoked) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إلغاء صلاحيات الروت'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  bool _isLoadingState(RootToolsState state) {
    return state is RootToolsLoading ||
        state is RootAccessLoading ||
        state is CommandExecutionLoading ||
        state is SystemOperationLoading;
  }

  String _getLoadingMessage(RootToolsState state) {
    if (state is RootAccessLoading) {
      return 'جاري فحص صلاحيات الروت...';
    } else if (state is CommandExecutionLoading) {
      return 'جاري تنفيذ الأمر...';
    } else if (state is SystemOperationLoading) {
      return state.operation;
    } else if (state is RootToolsLoading) {
      return state.message ?? 'جاري التحميل...';
    }
    return 'جاري التحميل...';
  }

  // Dialog methods
  void _showSettingsDialog() {
    // TODO: Implement settings dialog
  }

  void _showSortDialog() {
    // TODO: Implement sort dialog
  }

  void _showKillProcessDialog(dynamic process) {
    // TODO: Implement kill process dialog
  }

  void _showChangePriorityDialog(dynamic process) {
    // TODO: Implement change priority dialog
  }

  void _showFirewallDialog() {
    // TODO: Implement firewall dialog
  }

  void _showSystemAppsDialog() {
    // TODO: Implement system apps dialog
  }

  void _showEncryptionDialog() {
    // TODO: Implement encryption dialog
  }

  void _showBackupDialog() {
    // TODO: Implement backup dialog
  }

  void _showPartitionsDialog() {
    // TODO: Implement partitions dialog
  }

  void _showPerformanceDialog() {
    // TODO: Implement performance dialog
  }

  void _showLogsDialog() {
    // TODO: Implement logs dialog
  }

  void _showCommandDialog() {
    // TODO: Implement command dialog
  }
}
