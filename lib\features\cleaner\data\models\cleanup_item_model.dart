import '../../domain/entities/cleanup_item.dart';

class CleanupItemModel extends CleanupItem {
  const CleanupItemModel({
    required super.id,
    required super.type,
    required super.name,
    required super.description,
    required super.filePaths,
    required super.totalSize,
    required super.fileCount,
    required super.priority,
    super.isSelected,
    super.isSafeToDelete,
    required super.lastModified,
  });

  factory CleanupItemModel.fromJson(Map<String, dynamic> json) {
    return CleanupItemModel(
      id: json['id'] as String,
      type: CleanupType.values[json['type'] as int],
      name: json['name'] as String,
      description: json['description'] as String,
      filePaths: (json['filePaths'] as List<dynamic>).cast<String>(),
      totalSize: json['totalSize'] as int,
      fileCount: json['fileCount'] as int,
      priority: CleanupPriority.values[json['priority'] as int],
      isSelected: json['isSelected'] as bool? ?? false,
      isSafeToDelete: json['isSafeToDelete'] as bool? ?? true,
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'name': name,
      'description': description,
      'filePaths': filePaths,
      'totalSize': totalSize,
      'fileCount': fileCount,
      'priority': priority.index,
      'isSelected': isSelected,
      'isSafeToDelete': isSafeToDelete,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  factory CleanupItemModel.fromEntity(CleanupItem entity) {
    return CleanupItemModel(
      id: entity.id,
      type: entity.type,
      name: entity.name,
      description: entity.description,
      filePaths: entity.filePaths,
      totalSize: entity.totalSize,
      fileCount: entity.fileCount,
      priority: entity.priority,
      isSelected: entity.isSelected,
      isSafeToDelete: entity.isSafeToDelete,
      lastModified: entity.lastModified,
    );
  }
}

class SystemAnalysisModel extends SystemAnalysis {
  const SystemAnalysisModel({
    required super.totalFiles,
    required super.totalFolders,
    required super.totalSize,
    required super.availableSpace,
    required super.usedSpace,
    required super.cleanupOpportunities,
    required super.cleanupItems,
    required super.largestDirectories,
    required super.fileTypeDistribution,
    required super.analysisTime,
    required super.analysisDuration,
  });

  factory SystemAnalysisModel.fromJson(Map<String, dynamic> json) {
    return SystemAnalysisModel(
      totalFiles: json['totalFiles'] as int,
      totalFolders: json['totalFolders'] as int,
      totalSize: json['totalSize'] as int,
      availableSpace: json['availableSpace'] as int,
      usedSpace: json['usedSpace'] as int,
      cleanupOpportunities: Map<CleanupType, int>.from(
        (json['cleanupOpportunities'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(
            CleanupType.values[int.parse(key)],
            value as int,
          ),
        ),
      ),
      cleanupItems: (json['cleanupItems'] as List<dynamic>)
          .map((e) => CleanupItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      largestDirectories: Map<String, int>.from(json['largestDirectories'] as Map),
      fileTypeDistribution: Map<String, int>.from(json['fileTypeDistribution'] as Map),
      analysisTime: DateTime.parse(json['analysisTime'] as String),
      analysisDuration: Duration(milliseconds: json['analysisDurationMs'] as int),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalFiles': totalFiles,
      'totalFolders': totalFolders,
      'totalSize': totalSize,
      'availableSpace': availableSpace,
      'usedSpace': usedSpace,
      'cleanupOpportunities': cleanupOpportunities.map(
        (key, value) => MapEntry(key.index.toString(), value),
      ),
      'cleanupItems': cleanupItems.map((e) => (e as CleanupItemModel).toJson()).toList(),
      'largestDirectories': largestDirectories,
      'fileTypeDistribution': fileTypeDistribution,
      'analysisTime': analysisTime.toIso8601String(),
      'analysisDurationMs': analysisDuration.inMilliseconds,
    };
  }
}

class CleanupResultModel extends CleanupResult {
  const CleanupResultModel({
    required super.deletedFiles,
    required super.totalDeletedSize,
    required super.totalDeletedCount,
    required super.failedDeletions,
    required super.cleanupDuration,
    super.isComplete,
    super.errorMessage,
  });

  factory CleanupResultModel.fromJson(Map<String, dynamic> json) {
    return CleanupResultModel(
      deletedFiles: (json['deletedFiles'] as List<dynamic>).cast<String>(),
      totalDeletedSize: json['totalDeletedSize'] as int,
      totalDeletedCount: json['totalDeletedCount'] as int,
      failedDeletions: (json['failedDeletions'] as List<dynamic>).cast<String>(),
      cleanupDuration: Duration(milliseconds: json['cleanupDurationMs'] as int),
      isComplete: json['isComplete'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deletedFiles': deletedFiles,
      'totalDeletedSize': totalDeletedSize,
      'totalDeletedCount': totalDeletedCount,
      'failedDeletions': failedDeletions,
      'cleanupDurationMs': cleanupDuration.inMilliseconds,
      'isComplete': isComplete,
      'errorMessage': errorMessage,
    };
  }
}
