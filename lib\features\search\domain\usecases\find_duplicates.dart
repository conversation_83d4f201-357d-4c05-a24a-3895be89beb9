import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/search_result.dart';
import '../entities/search_filter.dart';
import '../repositories/search_repository.dart';

@lazySingleton
class FindDuplicates {
  final SearchRepository repository;

  const FindDuplicates(this.repository);

  ResultFuture<DuplicateSearchResult> call(DuplicateSearchFilter filter) async {
    return await repository.findDuplicates(filter);
  }

  Stream<DuplicateSearchResult> stream(DuplicateSearchFilter filter) {
    return repository.findDuplicatesStream(filter);
  }
}
