import 'package:equatable/equatable.dart';
import '../../domain/entities/storage_info.dart';
import '../../../file_browser/domain/entities/file_item.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

class DashboardLoaded extends DashboardState {
  final List<StorageInfo> storageInfo;
  final List<FileItem> recentFiles;
  final Map<String, int> categorizedUsage;
  final QuickAccessData quickAccess;

  const DashboardLoaded({
    required this.storageInfo,
    required this.recentFiles,
    required this.categorizedUsage,
    required this.quickAccess,
  });

  StorageInfo get primaryStorage => storageInfo.isNotEmpty 
      ? storageInfo.first 
      : const StorageInfo(
          totalSpace: 0,
          freeSpace: 0,
          usedSpace: 0,
          path: '',
          type: StorageType.internal,
          isRemovable: false,
          isEmulated: true,
        );

  DashboardLoaded copyWith({
    List<StorageInfo>? storageInfo,
    List<FileItem>? recentFiles,
    Map<String, int>? categorizedUsage,
    QuickAccessData? quickAccess,
  }) {
    return DashboardLoaded(
      storageInfo: storageInfo ?? this.storageInfo,
      recentFiles: recentFiles ?? this.recentFiles,
      categorizedUsage: categorizedUsage ?? this.categorizedUsage,
      quickAccess: quickAccess ?? this.quickAccess,
    );
  }

  @override
  List<Object?> get props => [
        storageInfo,
        recentFiles,
        categorizedUsage,
        quickAccess,
      ];
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

class QuickAccessData extends Equatable {
  final int imageCount;
  final int videoCount;
  final int audioCount;
  final int documentCount;
  final int downloadCount;
  final int appCount;

  const QuickAccessData({
    required this.imageCount,
    required this.videoCount,
    required this.audioCount,
    required this.documentCount,
    required this.downloadCount,
    required this.appCount,
  });

  @override
  List<Object?> get props => [
        imageCount,
        videoCount,
        audioCount,
        documentCount,
        downloadCount,
        appCount,
      ];
}
