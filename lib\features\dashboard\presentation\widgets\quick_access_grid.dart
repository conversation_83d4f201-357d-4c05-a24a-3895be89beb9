import 'package:flutter/material.dart';
import '../bloc/dashboard_state.dart';
import '../bloc/dashboard_event.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../app_manager/presentation/pages/app_manager_page.dart';

class QuickAccessGrid extends StatelessWidget {
  final QuickAccessData quickAccess;

  const QuickAccessGrid({super.key, required this.quickAccess});

  @override
  Widget build(BuildContext context) {
    final quickAccessItems = [
      QuickAccessItem(
        icon: Icons.image,
        title: 'Images',
        count: quickAccess.imageCount,
        color: Colors.green,
        type: QuickAccessType.images,
      ),
      QuickAccessItem(
        icon: Icons.video_library,
        title: 'Videos',
        count: quickAccess.videoCount,
        color: Colors.red,
        type: QuickAccessType.videos,
      ),
      QuickAccessItem(
        icon: Icons.music_note,
        title: 'Audio',
        count: quickAccess.audioCount,
        color: Colors.orange,
        type: QuickAccessType.audio,
      ),
      QuickAccessItem(
        icon: Icons.description,
        title: 'Documents',
        count: quickAccess.documentCount,
        color: Colors.blue,
        type: QuickAccessType.documents,
      ),
      QuickAccessItem(
        icon: Icons.download,
        title: 'Downloads',
        count: quickAccess.downloadCount,
        color: Colors.purple,
        type: QuickAccessType.downloads,
      ),
      QuickAccessItem(
        icon: Icons.apps,
        title: 'Apps',
        count: quickAccess.appCount,
        color: Colors.teal,
        type: QuickAccessType.apps,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
      ),
      itemCount: quickAccessItems.length,
      itemBuilder: (context, index) {
        final item = quickAccessItems[index];
        return QuickAccessCard(
          item: item,
          onTap: () {
            _handleQuickAccessTap(context, item.type);
          },
        );
      },
    );
  }

  void _handleQuickAccessTap(BuildContext context, QuickAccessType type) {
    switch (type) {
      case QuickAccessType.apps:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AppManagerPage()),
        );
        break;
      case QuickAccessType.images:
        // Navigate to images
        break;
      case QuickAccessType.videos:
        // Navigate to videos
        break;
      case QuickAccessType.audio:
        // Navigate to audio
        break;
      case QuickAccessType.documents:
        // Navigate to documents
        break;
      case QuickAccessType.downloads:
        // Navigate to downloads
        break;
      case QuickAccessType.favorites:
        // Navigate to favorites
        break;
    }
  }
}

class QuickAccessItem {
  final IconData icon;
  final String title;
  final int count;
  final Color color;
  final QuickAccessType type;

  const QuickAccessItem({
    required this.icon,
    required this.title,
    required this.count,
    required this.color,
    required this.type,
  });
}

class QuickAccessCard extends StatelessWidget {
  final QuickAccessItem item;
  final VoidCallback onTap;

  const QuickAccessCard({super.key, required this.item, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: item.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    AppConstants.defaultBorderRadius,
                  ),
                ),
                child: Icon(item.icon, size: 32, color: item.color),
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              Text(
                item.title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.smallPadding),

              Text(
                '${item.count} items',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
