import '../../../../core/utils/typedef.dart';
import '../entities/file_item.dart';

abstract class FileRepository {
  ResultFuture<List<FileItem>> getDirectoryContents(String path);
  ResultFuture<FileItem> getFileInfo(String path);
  ResultFuture<bool> createDirectory(String path);
  ResultFuture<bool> deleteFile(String path);
  ResultFuture<bool> deleteDirectory(String path);
  ResultFuture<bool> copyFile(String sourcePath, String destinationPath);
  ResultFuture<bool> moveFile(String sourcePath, String destinationPath);
  ResultFuture<bool> renameFile(String oldPath, String newPath);
  ResultFuture<List<String>> getRootDirectories();
  ResultFuture<bool> fileExists(String path);
  ResultFuture<bool> directoryExists(String path);
}
