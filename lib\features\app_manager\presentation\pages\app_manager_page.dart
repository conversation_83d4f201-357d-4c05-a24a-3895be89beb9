import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/app_manager_bloc.dart';
import '../bloc/app_manager_event.dart';
import '../bloc/app_manager_state.dart';
import '../widgets/app_list_widget.dart';
import '../widgets/app_filters_widget.dart';
import '../widgets/app_analysis_widget.dart';
import '../widgets/batch_operations_widget.dart';
import '../../../../core/theme/app_theme.dart';

import '../../../../core/di/injection_container.dart';

class AppManagerPage extends StatelessWidget {
  const AppManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppManagerBloc>()..add(const LoadAllAppsEvent()),
      child: const AppManagerView(),
    );
  }
}

class AppManagerView extends StatelessWidget {
  const AppManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('App Manager'),
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(Icons.apps), text: 'All Apps'),
              Tab(icon: Icon(Icons.person), text: 'User Apps'),
              Tab(icon: Icon(Icons.settings), text: 'System Apps'),
              Tab(icon: Icon(Icons.analytics), text: 'Analysis'),
            ],
          ),
          actions: [
            BlocBuilder<AppManagerBloc, AppManagerState>(
              builder: (context, state) {
                return IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed:
                      state is AppManagerLoading
                          ? null
                          : () {
                            context.read<AppManagerBloc>().add(
                              const RefreshAppsEvent(),
                            );
                          },
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showSearchDialog(context),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(context, value),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'filter',
                      child: ListTile(
                        leading: Icon(Icons.filter_list),
                        title: Text('Filters'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'sort',
                      child: ListTile(
                        leading: Icon(Icons.sort),
                        title: Text('Sort'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'batch',
                      child: ListTile(
                        leading: Icon(Icons.select_all),
                        title: Text('Batch Operations'),
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'install',
                      child: ListTile(
                        leading: Icon(Icons.install_mobile),
                        title: Text('Install APK'),
                      ),
                    ),
                  ],
            ),
          ],
        ),
        body: BlocConsumer<AppManagerBloc, AppManagerState>(
          listener: (context, state) {
            if (state is AppOperationSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is AppOperationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is AppManagerError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is BatchOperationCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Batch operation completed: ${state.operation.successfulApps} successful, ${state.operation.failedApps} failed',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is InstallationCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('App installed successfully'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            }
          },
          builder: (context, state) {
            return const TabBarView(
              children: [
                AllAppsTab(),
                UserAppsTab(),
                SystemAppsTab(),
                AnalysisTab(),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Apps'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Enter app name or package name',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                context.read<AppManagerBloc>().add(SearchAppsEvent(query));
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  context.read<AppManagerBloc>().add(const SearchAppsEvent(''));
                  Navigator.of(context).pop();
                },
                child: const Text('Clear'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'filter':
        _showFiltersDialog(context);
        break;
      case 'sort':
        _showSortDialog(context);
        break;
      case 'batch':
        _showBatchOperationsDialog(context);
        break;
      case 'install':
        _showInstallApkDialog(context);
        break;
    }
  }

  void _showFiltersDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AppFiltersDialog(),
    );
  }

  void _showSortDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sort Apps'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  AppSortType.values.map((sortType) {
                    return ListTile(
                      title: Text(_getSortTypeName(sortType)),
                      onTap: () {
                        context.read<AppManagerBloc>().add(
                          SortAppsEvent(sortType: sortType),
                        );
                        Navigator.of(context).pop();
                      },
                    );
                  }).toList(),
            ),
          ),
    );
  }

  void _showBatchOperationsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const BatchOperationsDialog(),
    );
  }

  void _showInstallApkDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Install APK'),
            content: const Text('Select APK file to install'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement file picker
                  Navigator.of(context).pop();
                },
                child: const Text('Browse'),
              ),
            ],
          ),
    );
  }

  String _getSortTypeName(AppSortType sortType) {
    switch (sortType) {
      case AppSortType.name:
        return 'Name';
      case AppSortType.size:
        return 'Size';
      case AppSortType.installDate:
        return 'Install Date';
      case AppSortType.lastUsed:
        return 'Last Used';
      case AppSortType.packageName:
        return 'Package Name';
    }
  }
}

class AllAppsTab extends StatelessWidget {
  const AllAppsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<AppManagerBloc>()..add(const LoadAllAppsEvent()),
      child: const AppListWidget(),
    );
  }
}

class UserAppsTab extends StatelessWidget {
  const UserAppsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<AppManagerBloc>()..add(const LoadUserAppsEvent()),
      child: const AppListWidget(),
    );
  }
}

class SystemAppsTab extends StatelessWidget {
  const SystemAppsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<AppManagerBloc>()..add(const LoadSystemAppsEvent()),
      child: const AppListWidget(),
    );
  }
}

class AnalysisTab extends StatelessWidget {
  const AnalysisTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<AppManagerBloc>()..add(const LoadAppAnalysisEvent()),
      child: const AppAnalysisWidget(),
    );
  }
}

class AppFiltersDialog extends StatelessWidget {
  const AppFiltersDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AppFiltersWidget();
  }
}

class BatchOperationsDialog extends StatelessWidget {
  const BatchOperationsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const BatchOperationsWidget();
  }
}
