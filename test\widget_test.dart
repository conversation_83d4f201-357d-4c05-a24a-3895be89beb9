// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:hd_file_explore/app.dart';

void main() {
  testWidgets('HD File Explorer app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const HDFileExplorerApp());

    // Verify that the app loads with bottom navigation
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Verify that dashboard tab is initially selected
    expect(find.text('Dashboard'), findsOneWidget);
    expect(find.text('Files'), findsOneWidget);
    expect(find.text('Search'), findsOneWidget);
    expect(find.text('Operations'), findsOneWidget);
    expect(find.text('Cleaner'), findsOneWidget);
  });
}
