import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../bloc/dashboard_state.dart';
import '../widgets/storage_card.dart';
import '../widgets/quick_access_grid.dart';
import '../widgets/storage_chart.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/injection_container.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<DashboardBloc>()..add(const LoadDashboardEvent()),
      child: const DashboardView(),
    );
  }
}

class DashboardView extends StatelessWidget {
  const DashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HD File Explorer'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<DashboardBloc>().add(const RefreshStorageInfoEvent());
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to settings
            },
          ),
        ],
      ),
      body: BlocBuilder<DashboardBloc, DashboardState>(
        builder: (context, state) {
          if (state is DashboardLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is DashboardError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  ElevatedButton(
                    onPressed: () {
                      context.read<DashboardBloc>().add(const LoadDashboardEvent());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is DashboardLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<DashboardBloc>().add(const LoadDashboardEvent());
              },
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Storage Overview
                    Text(
                      'Storage Overview',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    
                    // Storage Cards
                    SizedBox(
                      height: 120,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: state.storageInfo.length,
                        itemBuilder: (context, index) {
                          final storage = state.storageInfo[index];
                          return Padding(
                            padding: EdgeInsets.only(
                              right: index < state.storageInfo.length - 1 
                                  ? AppConstants.defaultPadding 
                                  : 0,
                            ),
                            child: StorageCard(storage: storage),
                          );
                        },
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.largePadding),
                    
                    // Storage Usage Chart
                    Text(
                      'Storage Usage',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    
                    StorageChart(categorizedUsage: state.categorizedUsage),
                    
                    const SizedBox(height: AppConstants.largePadding),
                    
                    // Quick Access
                    Text(
                      'Quick Access',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    
                    QuickAccessGrid(quickAccess: state.quickAccess),
                    
                    const SizedBox(height: AppConstants.largePadding),
                    
                    // Recent Files
                    Text(
                      'Recent Files',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    
                    if (state.recentFiles.isEmpty)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(AppConstants.defaultPadding),
                          child: Row(
                            children: [
                              Icon(
                                Icons.history,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: AppConstants.defaultPadding),
                              Text(
                                'No recent files',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: state.recentFiles.length,
                        itemBuilder: (context, index) {
                          final file = state.recentFiles[index];
                          return ListTile(
                            leading: Icon(
                              file.isDirectory ? Icons.folder : Icons.insert_drive_file,
                            ),
                            title: Text(file.name),
                            subtitle: Text(file.path),
                            trailing: Text(
                              file.modifiedDate.toString().split(' ')[0],
                            ),
                            onTap: () {
                              // Navigate to file or directory
                            },
                          );
                        },
                      ),
                  ],
                ),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}
