import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/root_access.dart';
import '../repositories/root_tools_repository.dart';

@lazySingleton
class SystemManager {
  final RootToolsRepository repository;

  const SystemManager(this.repository);

  // System Information
  ResultFuture<List<SystemPartition>> getSystemPartitions() async {
    return await repository.getSystemPartitions();
  }

  ResultFuture<List<SystemProcess>> getRunningProcesses() async {
    return await repository.getRunningProcesses();
  }

  ResultFuture<List<SystemService>> getSystemServices() async {
    return await repository.getSystemServices();
  }

  ResultFuture<Map<String, String>> getSystemProperties() async {
    return await repository.getSystemProperties();
  }

  ResultFuture<Map<String, dynamic>> getSystemInfo() async {
    return await repository.getSystemInfo();
  }

  // Process Management
  ResultVoid killProcess(int pid) async {
    return await repository.killProcess(pid);
  }

  ResultVoid killProcessByName(String processName) async {
    return await repository.killProcessByName(processName);
  }

  ResultFuture<SystemProcess> getProcessInfo(int pid) async {
    return await repository.getProcessInfo(pid);
  }

  ResultVoid changeProcessPriority(int pid, int priority) async {
    return await repository.changeProcessPriority(pid, priority);
  }

  // Service Management
  ResultVoid startService(String serviceName) async {
    return await repository.startService(serviceName);
  }

  ResultVoid stopService(String serviceName) async {
    return await repository.stopService(serviceName);
  }

  ResultVoid restartService(String serviceName) async {
    return await repository.restartService(serviceName);
  }

  ResultVoid enableService(String serviceName) async {
    return await repository.enableService(serviceName);
  }

  ResultVoid disableService(String serviceName) async {
    return await repository.disableService(serviceName);
  }

  ResultFuture<ServiceState> getServiceState(String serviceName) async {
    return await repository.getServiceState(serviceName);
  }

  // File System Operations
  ResultVoid mountPartition(String partition, String mountPoint) async {
    return await repository.mountPartition(partition, mountPoint);
  }

  ResultVoid unmountPartition(String partition) async {
    return await repository.unmountPartition(partition);
  }

  ResultVoid remountPartition(String partition, bool readOnly) async {
    return await repository.remountPartition(partition, readOnly);
  }

  ResultVoid changeFilePermissions(String path, String permissions) async {
    return await repository.changeFilePermissions(path, permissions);
  }

  ResultVoid changeFileOwnership(String path, String owner, String group) async {
    return await repository.changeFileOwnership(path, owner, group);
  }

  ResultFuture<String> getFilePermissions(String path) async {
    return await repository.getFilePermissions(path);
  }

  ResultFuture<Map<String, String>> getFileOwnership(String path) async {
    return await repository.getFileOwnership(path);
  }

  // System Modifications
  ResultVoid modifySystemProperty(String property, String value) async {
    return await repository.modifySystemProperty(property, value);
  }

  ResultVoid modifyBuildProp(Map<String, String> properties) async {
    return await repository.modifyBuildProp(properties);
  }

  ResultVoid installSystemApp(String apkPath) async {
    return await repository.installSystemApp(apkPath);
  }

  ResultVoid uninstallSystemApp(String packageName) async {
    return await repository.uninstallSystemApp(packageName);
  }

  ResultVoid freezeApp(String packageName) async {
    return await repository.freezeApp(packageName);
  }

  ResultVoid unfreezeApp(String packageName) async {
    return await repository.unfreezeApp(packageName);
  }

  // Performance Optimization
  ResultVoid clearSystemCache() async {
    return await repository.clearSystemCache();
  }

  ResultVoid optimizeDatabase() async {
    return await repository.optimizeDatabase();
  }

  ResultVoid defragmentStorage() async {
    return await repository.defragmentStorage();
  }

  ResultVoid trimFilesystem() async {
    return await repository.trimFilesystem();
  }

  ResultVoid optimizeMemory() async {
    return await repository.optimizeMemory();
  }

  // System Tweaks
  ResultVoid enableDeveloperOptions() async {
    return await repository.enableDeveloperOptions();
  }

  ResultVoid enableAdbDebugging() async {
    return await repository.enableAdbDebugging();
  }

  ResultVoid setAnimationScale(double scale) async {
    return await repository.setAnimationScale(scale);
  }

  ResultVoid setDpi(int dpi) async {
    return await repository.setDpi(dpi);
  }

  ResultVoid setGovernor(String governor) async {
    return await repository.setGovernor(governor);
  }

  ResultVoid setCpuFrequency(int minFreq, int maxFreq) async {
    return await repository.setCpuFrequency(minFreq, maxFreq);
  }

  // Recovery and Repair
  ResultVoid fixPermissions() async {
    return await repository.fixPermissions();
  }

  ResultVoid rebuildDalvikCache() async {
    return await repository.rebuildDalvikCache();
  }

  ResultVoid clearDalvikCache() async {
    return await repository.clearDalvikCache();
  }

  ResultVoid fixBootloop() async {
    return await repository.fixBootloop();
  }

  ResultVoid repairFilesystem(String partition) async {
    return await repository.repairFilesystem(partition);
  }

  // Monitoring Streams
  Stream<Map<String, dynamic>> getSystemStats() {
    return repository.getSystemStats();
  }

  Stream<List<SystemProcess>> getProcessUpdates() {
    return repository.getProcessUpdates();
  }

  Stream<Map<String, double>> getCpuUsage() {
    return repository.getCpuUsage();
  }

  Stream<Map<String, int>> getMemoryUsage() {
    return repository.getMemoryUsage();
  }

  Stream<Map<String, int>> getNetworkUsage() {
    return repository.getNetworkUsage();
  }

  // Utility Methods
  bool isSystemPartition(String path) {
    final systemPaths = [
      '/system',
      '/vendor',
      '/boot',
      '/recovery',
      '/cache',
      '/data',
      '/proc',
      '/sys',
      '/dev',
    ];

    return systemPaths.any((systemPath) => path.startsWith(systemPath));
  }

  bool isCriticalProcess(String processName) {
    final criticalProcesses = [
      'init',
      'kernel',
      'kthreadd',
      'ksoftirqd',
      'migration',
      'rcu_',
      'watchdog',
      'systemd',
      'zygote',
      'system_server',
      'surfaceflinger',
      'servicemanager',
    ];

    return criticalProcesses.any((critical) => 
        processName.toLowerCase().contains(critical.toLowerCase()));
  }

  bool isCriticalService(String serviceName) {
    final criticalServices = [
      'init',
      'servicemanager',
      'surfaceflinger',
      'system_server',
      'zygote',
      'netd',
      'vold',
      'rild',
      'media',
      'keystore',
      'drm',
    ];

    return criticalServices.contains(serviceName.toLowerCase());
  }

  ProcessPriority getRecommendedPriority(String processName) {
    if (isCriticalProcess(processName)) {
      return ProcessPriority.high;
    }

    final backgroundProcesses = [
      'backup',
      'sync',
      'update',
      'download',
      'upload',
    ];

    if (backgroundProcesses.any((bg) => 
        processName.toLowerCase().contains(bg))) {
      return ProcessPriority.low;
    }

    return ProcessPriority.normal;
  }

  List<String> getRecommendedOptimizations() {
    return [
      'Clear system cache',
      'Optimize databases',
      'Trim filesystem',
      'Clear Dalvik cache',
      'Fix file permissions',
      'Remove temporary files',
      'Optimize memory usage',
      'Update system properties',
    ];
  }

  Map<String, String> getSystemTweakRecommendations() {
    return {
      'Animation Scale': '0.5x for better performance',
      'DPI': 'Adjust for better display scaling',
      'CPU Governor': 'Performance for speed, Powersave for battery',
      'I/O Scheduler': 'Deadline for better responsiveness',
      'TCP Congestion': 'BBR for better network performance',
      'Swappiness': 'Lower value for better RAM usage',
      'Dirty Ratio': 'Optimize for write performance',
      'Read Ahead': 'Increase for better sequential reads',
    };
  }
}

enum ProcessPriority {
  low,
  normal,
  high,
  realtime,
}
