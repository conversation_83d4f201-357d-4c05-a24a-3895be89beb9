import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/file_operation.dart';
import '../../domain/repositories/file_operations_repository.dart';
import '../datasources/file_operations_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: FileOperationsRepository)
class FileOperationsRepositoryImpl implements FileOperationsRepository {
  final FileOperationsDataSource dataSource;

  const FileOperationsRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, String>> copyFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    try {
      final result = await dataSource.copyFiles(sourcePaths, destinationPath);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> moveFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    try {
      final result = await dataSource.moveFiles(sourcePaths, destinationPath);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> deleteFiles(List<String> filePaths) async {
    try {
      final result = await dataSource.deleteFiles(filePaths);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> compressFiles(
    List<String> sourcePaths,
    String destinationPath,
    CompressionOptions options,
  ) async {
    try {
      final result = await dataSource.compressFiles(sourcePaths, destinationPath, options);
      return Right(result);
    } on CompressionException catch (e) {
      return Left(CompressionFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(CompressionFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> extractArchive(
    String archivePath,
    String destinationPath,
    String? password,
  ) async {
    try {
      final result = await dataSource.extractArchive(archivePath, destinationPath, password);
      return Right(result);
    } on CompressionException catch (e) {
      return Left(ExtractionFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ExtractionFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<FileOperation>>> getActiveOperations() async {
    try {
      final result = await dataSource.getActiveOperations();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get operations: $e'));
    }
  }

  @override
  Future<Either<Failure, FileOperation>> getOperation(String operationId) async {
    try {
      final result = await dataSource.getOperation(operationId);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get operation: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> pauseOperation(String operationId) async {
    try {
      await dataSource.pauseOperation(operationId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to pause operation: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resumeOperation(String operationId) async {
    try {
      await dataSource.resumeOperation(operationId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to resume operation: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelOperation(String operationId) async {
    try {
      await dataSource.cancelOperation(operationId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel operation: $e'));
    }
  }

  @override
  Stream<FileOperation> watchOperation(String operationId) {
    return dataSource.watchOperation(operationId);
  }

  @override
  Stream<List<FileOperation>> watchAllOperations() {
    return dataSource.watchAllOperations();
  }

  @override
  Future<Either<Failure, int>> calculateTotalSize(List<String> paths) async {
    try {
      final result = await dataSource.calculateTotalSize(paths);
      return Right(result);
    } catch (e) {
      return Left(FileSystemFailure(message: 'Failed to calculate size: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasEnoughSpace(
    String destinationPath,
    int requiredBytes,
  ) async {
    try {
      final result = await dataSource.hasEnoughSpace(destinationPath, requiredBytes);
      return Right(result);
    } catch (e) {
      return Left(StorageFullFailure(message: 'Failed to check space: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getConflictingFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    try {
      final result = await dataSource.getConflictingFiles(sourcePaths, destinationPath);
      return Right(result);
    } catch (e) {
      return Left(FileSystemFailure(message: 'Failed to check conflicts: $e'));
    }
  }
}
