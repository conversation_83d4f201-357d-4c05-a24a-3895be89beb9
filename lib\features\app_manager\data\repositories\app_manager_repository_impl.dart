import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/app_info.dart';
import '../../domain/repositories/app_manager_repository.dart';
import '../datasources/app_manager_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: AppManagerRepository)
class AppManagerRepositoryImpl implements AppManagerRepository {
  final AppManagerDataSource dataSource;

  const AppManagerRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, List<AppInfo>>> getAllApps() async {
    try {
      final result = await dataSource.getAllApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getUserApps() async {
    try {
      final result = await dataSource.getUserApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getSystemApps() async {
    try {
      final result = await dataSource.getSystemApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getRunningApps() async {
    try {
      final result = await dataSource.getRunningApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AppInfo>> getAppInfo(String packageName) async {
    try {
      final result = await dataSource.getAppInfo(packageName);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> uninstallApp(String packageName) async {
    try {
      await dataSource.uninstallApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> disableApp(String packageName) async {
    try {
      await dataSource.disableApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> enableApp(String packageName) async {
    try {
      await dataSource.enableApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAppData(String packageName) async {
    try {
      await dataSource.clearAppData(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAppCache(String packageName) async {
    try {
      await dataSource.clearAppCache(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> forceStopApp(String packageName) async {
    try {
      await dataSource.forceStopApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> launchApp(String packageName) async {
    try {
      await dataSource.launchApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AppBatchOperation>> startBatchOperation(
    AppBatchOperationType type,
    List<String> packageNames,
  ) async {
    try {
      final result = await dataSource.startBatchOperation(type, packageNames);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<AppBatchOperation> getBatchOperationStream(String operationId) {
    return dataSource.getBatchOperationStream(operationId);
  }

  @override
  Future<Either<Failure, void>> cancelBatchOperation(String operationId) async {
    try {
      await dataSource.cancelBatchOperation(operationId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, AppUsageStats>> getAppUsageStats(String packageName) async {
    try {
      final result = await dataSource.getAppUsageStats(packageName);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppUsageStats>>> getAllUsageStats() async {
    try {
      final result = await dataSource.getAllUsageStats();
      return Right(result.cast<AppUsageStats>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, Duration>>> getDailyUsageStats(DateTime date) async {
    try {
      final result = await dataSource.getDailyUsageStats(date);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> backupApp(String packageName, String backupPath) async {
    try {
      final result = await dataSource.backupApp(packageName, backupPath);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> restoreApp(String backupPath) async {
    try {
      await dataSource.restoreApp(backupPath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableBackups() async {
    try {
      final result = await dataSource.getAvailableBackups();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAppPermissions(String packageName) async {
    try {
      final result = await dataSource.getAppPermissions(packageName);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, bool>>> getPermissionStatus(String packageName) async {
    try {
      final result = await dataSource.getPermissionStatus(packageName);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> grantPermission(String packageName, String permission) async {
    try {
      await dataSource.grantPermission(packageName, permission);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> revokePermission(String packageName, String permission) async {
    try {
      await dataSource.revokePermission(packageName, permission);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, int>>> getAppSizeAnalysis() async {
    try {
      final result = await dataSource.getAppSizeAnalysis();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getLargestApps(int limit) async {
    try {
      final result = await dataSource.getLargestApps(limit);
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getUnusedApps(int daysUnused) async {
    try {
      final result = await dataSource.getUnusedApps(daysUnused);
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getAppsWithPermission(String permission) async {
    try {
      final result = await dataSource.getAppsWithPermission(permission);
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> installApk(String apkPath) async {
    try {
      await dataSource.installApk(apkPath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<double> getInstallationProgress(String apkPath) {
    return dataSource.getInstallationProgress(apkPath);
  }

  @override
  Future<Either<Failure, bool>> verifyApkSignature(String apkPath) async {
    try {
      final result = await dataSource.verifyApkSignature(apkPath);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getUpdatableApps() async {
    try {
      final result = await dataSource.getUpdatableApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateApp(String packageName) async {
    try {
      await dataSource.updateApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateAllApps() async {
    try {
      await dataSource.updateAllApps();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppInfo>>> getDefaultApps() async {
    try {
      final result = await dataSource.getDefaultApps();
      return Right(result.cast<AppInfo>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> setDefaultApp(String packageName, String category) async {
    try {
      await dataSource.setDefaultApp(packageName, category);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearDefaultApp(String category) async {
    try {
      await dataSource.clearDefaultApp(category);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
