import '../../domain/entities/app_info.dart';

class AppInfoModel extends AppInfo {
  const AppInfoModel({
    required super.packageName,
    required super.appName,
    required super.version,
    required super.versionCode,
    required super.size,
    required super.installDate,
    required super.updateDate,
    required super.isSystemApp,
    required super.isEnabled,
    super.iconPath,
    super.apkPath,
    super.dataDir,
    super.permissions,
  });

  factory AppInfoModel.fromJson(Map<String, dynamic> json) {
    return AppInfoModel(
      packageName: json['packageName'] as String,
      appName: json['appName'] as String,
      version: json['version'] as String,
      versionCode: json['versionCode'] as int,
      size: json['size'] as int,
      installDate: DateTime.parse(json['installDate'] as String),
      updateDate: DateTime.parse(json['updateDate'] as String),
      isSystemApp: json['isSystemApp'] as bool,
      isEnabled: json['isEnabled'] as bool,
      iconPath: json['iconPath'] as String?,
      apkPath: json['apkPath'] as String?,
      dataDir: json['dataDir'] as String?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'packageName': packageName,
      'appName': appName,
      'version': version,
      'versionCode': versionCode,
      'size': size,
      'installDate': installDate.toIso8601String(),
      'updateDate': updateDate.toIso8601String(),
      'isSystemApp': isSystemApp,
      'isEnabled': isEnabled,
      'iconPath': iconPath,
      'apkPath': apkPath,
      'dataDir': dataDir,
      'permissions': permissions,
    };
  }

  AppInfoModel copyWith({
    String? packageName,
    String? appName,
    String? version,
    int? versionCode,
    int? size,
    DateTime? installDate,
    DateTime? updateDate,
    bool? isSystemApp,
    bool? isEnabled,
    String? iconPath,
    String? apkPath,
    String? dataDir,
    List<String>? permissions,
  }) {
    return AppInfoModel(
      packageName: packageName ?? this.packageName,
      appName: appName ?? this.appName,
      version: version ?? this.version,
      versionCode: versionCode ?? this.versionCode,
      size: size ?? this.size,
      installDate: installDate ?? this.installDate,
      updateDate: updateDate ?? this.updateDate,
      isSystemApp: isSystemApp ?? this.isSystemApp,
      isEnabled: isEnabled ?? this.isEnabled,
      iconPath: iconPath ?? this.iconPath,
      apkPath: apkPath ?? this.apkPath,
      dataDir: dataDir ?? this.dataDir,
      permissions: permissions ?? this.permissions,
    );
  }
}
