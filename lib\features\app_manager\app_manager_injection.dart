import 'package:get_it/get_it.dart';

import 'data/datasources/app_manager_datasource.dart';
import 'data/repositories/app_manager_repository_impl.dart';
import 'domain/repositories/app_manager_repository.dart';
import 'domain/usecases/get_all_apps.dart';
import 'domain/usecases/manage_app.dart';
import 'domain/usecases/batch_operations.dart';
import 'presentation/bloc/app_manager_bloc.dart';

void initAppManagerInjection() {
  final sl = GetIt.instance;

  // Data Sources
  sl.registerLazySingleton<AppManagerDataSource>(
    () => AppManagerDataSourceImpl(),
  );

  // Repositories
  sl.registerLazySingleton<AppManagerRepository>(
    () => AppManagerRepositoryImpl(sl()),
  );

  // Use Cases
  sl.registerLazySingleton(() => GetAllApps(sl()));
  sl.registerLazySingleton(() => ManageApp(sl()));
  sl.registerLazySingleton(() => BatchOperations(sl()));

  // BLoC
  sl.registerFactory(() => AppManagerBloc(sl(), sl(), sl()));
}
