import 'package:equatable/equatable.dart';
import '../models/language.dart';

/// حالات إدارة اللغة
abstract class LanguageState extends Equatable {
  const LanguageState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class LanguageInitial extends LanguageState {
  const LanguageInitial();
}

/// جاري تحميل اللغة
class LanguageLoading extends LanguageState {
  final String? message;

  const LanguageLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// تم تحميل اللغة بنجاح
class LanguageLoaded extends LanguageState {
  final Language language;
  final Map<String, dynamic> translations;
  final bool isRTL;

  const LanguageLoaded({
    required this.language,
    required this.translations,
    required this.isRTL,
  });

  @override
  List<Object?> get props => [language, translations, isRTL];
}

/// تم تغيير اللغة
class LanguageChanged extends LanguageState {
  final Language previousLanguage;
  final Language currentLanguage;
  final Map<String, dynamic> translations;
  final bool isRTL;

  const LanguageChanged({
    required this.previousLanguage,
    required this.currentLanguage,
    required this.translations,
    required this.isRTL,
  });

  @override
  List<Object?> get props => [previousLanguage, currentLanguage, translations, isRTL];
}

/// خطأ في تحميل اللغة
class LanguageError extends LanguageState {
  final String message;
  final String? errorCode;
  final Language? fallbackLanguage;

  const LanguageError({
    required this.message,
    this.errorCode,
    this.fallbackLanguage,
  });

  @override
  List<Object?> get props => [message, errorCode, fallbackLanguage];
}

/// تم تحديث الترجمات
class TranslationsUpdated extends LanguageState {
  final Language language;
  final Map<String, dynamic> translations;
  final String? updatedKey;
  final String? updatedValue;

  const TranslationsUpdated({
    required this.language,
    required this.translations,
    this.updatedKey,
    this.updatedValue,
  });

  @override
  List<Object?> get props => [language, translations, updatedKey, updatedValue];
}

/// تم مسح الترجمات
class TranslationsCleared extends LanguageState {
  final Language language;

  const TranslationsCleared(this.language);

  @override
  List<Object?> get props => [language];
}

/// تم إعادة تحميل الترجمات
class TranslationsReloaded extends LanguageState {
  final Language language;
  final Map<String, dynamic> translations;

  const TranslationsReloaded({
    required this.language,
    required this.translations,
  });

  @override
  List<Object?> get props => [language, translations];
}

/// تم استيراد الترجمات
class TranslationsImported extends LanguageState {
  final Language language;
  final Map<String, dynamic> translations;
  final int importedCount;

  const TranslationsImported({
    required this.language,
    required this.translations,
    required this.importedCount,
  });

  @override
  List<Object?> get props => [language, translations, importedCount];
}

/// تم دمج الترجمات
class TranslationsMerged extends LanguageState {
  final Language language;
  final Map<String, dynamic> translations;
  final int mergedCount;

  const TranslationsMerged({
    required this.language,
    required this.translations,
    required this.mergedCount,
  });

  @override
  List<Object?> get props => [language, translations, mergedCount];
}

/// نتائج البحث في الترجمات
class TranslationSearchResults extends LanguageState {
  final String query;
  final List<String> results;
  final Language language;

  const TranslationSearchResults({
    required this.query,
    required this.results,
    required this.language,
  });

  @override
  List<Object?> get props => [query, results, language];
}

/// نتائج التحقق من الترجمات
class TranslationValidationResults extends LanguageState {
  final Language language;
  final Language referenceLanguage;
  final Map<String, dynamic> validationResults;

  const TranslationValidationResults({
    required this.language,
    required this.referenceLanguage,
    required this.validationResults,
  });

  @override
  List<Object?> get props => [language, referenceLanguage, validationResults];
}

/// تم تصدير الترجمات
class TranslationsExported extends LanguageState {
  final Language language;
  final String exportedData;
  final String? filePath;

  const TranslationsExported({
    required this.language,
    required this.exportedData,
    this.filePath,
  });

  @override
  List<Object?> get props => [language, exportedData, filePath];
}

/// معلومات الترجمة
class TranslationInfo extends LanguageState {
  final Language language;
  final Map<String, dynamic> info;

  const TranslationInfo({
    required this.language,
    required this.info,
  });

  @override
  List<Object?> get props => [language, info];
}

/// تم تحديد لغة النظام
class SystemLanguageDetected extends LanguageState {
  final Language detectedLanguage;
  final Language currentLanguage;
  final bool shouldChange;

  const SystemLanguageDetected({
    required this.detectedLanguage,
    required this.currentLanguage,
    required this.shouldChange,
  });

  @override
  List<Object?> get props => [detectedLanguage, currentLanguage, shouldChange];
}

/// تم تبديل اتجاه النص
class TextDirectionToggled extends LanguageState {
  final Language language;
  final bool isRTL;
  final bool wasToggled;

  const TextDirectionToggled({
    required this.language,
    required this.isRTL,
    required this.wasToggled,
  });

  @override
  List<Object?> get props => [language, isRTL, wasToggled];
}

/// تم إعادة تعيين اللغة للافتراضية
class LanguageResetToDefault extends LanguageState {
  final Language previousLanguage;
  final Language defaultLanguage;
  final Map<String, dynamic> translations;

  const LanguageResetToDefault({
    required this.previousLanguage,
    required this.defaultLanguage,
    required this.translations,
  });

  @override
  List<Object?> get props => [previousLanguage, defaultLanguage, translations];
}

/// تم حفظ تفضيلات اللغة
class LanguagePreferencesSaved extends LanguageState {
  final Language language;
  final Map<String, dynamic> preferences;

  const LanguagePreferencesSaved({
    required this.language,
    required this.preferences,
  });

  @override
  List<Object?> get props => [language, preferences];
}

/// تم تحميل تفضيلات اللغة
class LanguagePreferencesLoaded extends LanguageState {
  final Language language;
  final Map<String, dynamic> preferences;

  const LanguagePreferencesLoaded({
    required this.language,
    required this.preferences,
  });

  @override
  List<Object?> get props => [language, preferences];
}

/// تم تحديث إعدادات الترجمة
class TranslationSettingsUpdated extends LanguageState {
  final Language language;
  final Map<String, dynamic> settings;

  const TranslationSettingsUpdated({
    required this.language,
    required this.settings,
  });

  @override
  List<Object?> get props => [language, settings];
}

/// تم تفعيل/تعطيل الترجمة التلقائية
class AutoTranslationToggled extends LanguageState {
  final Language language;
  final bool isEnabled;

  const AutoTranslationToggled({
    required this.language,
    required this.isEnabled,
  });

  @override
  List<Object?> get props => [language, isEnabled];
}

/// تم تحديث ذاكرة التخزين المؤقت
class TranslationCacheUpdated extends LanguageState {
  final Language language;
  final int cacheSize;
  final DateTime lastUpdated;

  const TranslationCacheUpdated({
    required this.language,
    required this.cacheSize,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [language, cacheSize, lastUpdated];
}

/// تم مسح ذاكرة التخزين المؤقت
class TranslationCacheCleared extends LanguageState {
  final Language language;

  const TranslationCacheCleared(this.language);

  @override
  List<Object?> get props => [language];
}

/// تم تحميل ترجمات إضافية
class AdditionalTranslationsLoaded extends LanguageState {
  final Language language;
  final String module;
  final Map<String, dynamic> translations;

  const AdditionalTranslationsLoaded({
    required this.language,
    required this.module,
    required this.translations,
  });

  @override
  List<Object?> get props => [language, module, translations];
}

/// تم إلغاء تحميل ترجمات إضافية
class AdditionalTranslationsUnloaded extends LanguageState {
  final Language language;
  final String module;

  const AdditionalTranslationsUnloaded({
    required this.language,
    required this.module,
  });

  @override
  List<Object?> get props => [language, module];
}

/// تم تحديث قاموس الترجمة
class TranslationDictionaryUpdated extends LanguageState {
  final Language language;
  final Map<String, String> dictionary;
  final int updatedCount;

  const TranslationDictionaryUpdated({
    required this.language,
    required this.dictionary,
    required this.updatedCount,
  });

  @override
  List<Object?> get props => [language, dictionary, updatedCount];
}

/// تم إضافة ترجمة مخصصة
class CustomTranslationAdded extends LanguageState {
  final Language language;
  final String key;
  final String value;

  const CustomTranslationAdded({
    required this.language,
    required this.key,
    required this.value,
  });

  @override
  List<Object?> get props => [language, key, value];
}

/// تم إزالة ترجمة مخصصة
class CustomTranslationRemoved extends LanguageState {
  final Language language;
  final String key;

  const CustomTranslationRemoved({
    required this.language,
    required this.key,
  });

  @override
  List<Object?> get props => [language, key];
}

/// تم تحديث إعدادات الخط
class FontSettingsUpdated extends LanguageState {
  final Language language;
  final String fontFamily;
  final double fontSize;

  const FontSettingsUpdated({
    required this.language,
    required this.fontFamily,
    required this.fontSize,
  });

  @override
  List<Object?> get props => [language, fontFamily, fontSize];
}

/// تم تحديث إعدادات التخطيط
class LayoutSettingsUpdated extends LanguageState {
  final Language language;
  final bool isRTL;
  final String textAlign;

  const LayoutSettingsUpdated({
    required this.language,
    required this.isRTL,
    required this.textAlign,
  });

  @override
  List<Object?> get props => [language, isRTL, textAlign];
}

/// تم تحديث إعدادات التنسيق
class FormattingSettingsUpdated extends LanguageState {
  final Language language;
  final String dateFormat;
  final String timeFormat;
  final String numberFormat;

  const FormattingSettingsUpdated({
    required this.language,
    required this.dateFormat,
    required this.timeFormat,
    required this.numberFormat,
  });

  @override
  List<Object?> get props => [language, dateFormat, timeFormat, numberFormat];
}

/// تم تحديث إعدادات الإقليم
class LocaleSettingsUpdated extends LanguageState {
  final Language language;
  final String currency;
  final String timezone;
  final String calendar;

  const LocaleSettingsUpdated({
    required this.language,
    required this.currency,
    required this.timezone,
    required this.calendar,
  });

  @override
  List<Object?> get props => [language, currency, timezone, calendar];
}
