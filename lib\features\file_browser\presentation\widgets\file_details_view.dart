import 'package:flutter/material.dart';
import '../../domain/entities/file_item.dart';
import 'file_item_tile.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class FileDetailsView extends StatelessWidget {
  final List<FileItem> files;

  const FileDetailsView({
    super.key,
    required this.files,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columnSpacing: AppConstants.defaultPadding,
        columns: const [
          DataColumn(label: Text('Name')),
          DataColumn(label: Text('Size')),
          DataColumn(label: Text('Type')),
          DataColumn(label: Text('Modified')),
          DataColumn(label: Text('Permissions')),
        ],
        rows: files.map((file) => _buildDataRow(context, file)).toList(),
      ),
    );
  }

  DataRow _buildDataRow(BuildContext context, FileItem file) {
    return DataRow(
      cells: [
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                file.isDirectory ? Icons.folder : Icons.insert_drive_file,
                size: 20,
                color: file.isDirectory ? Colors.blue : Colors.grey[600],
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Flexible(
                child: Text(
                  file.name,
                  style: TextStyle(
                    fontWeight: file.isDirectory ? FontWeight.bold : FontWeight.normal,
                    color: file.isHidden ? Colors.grey[600] : null,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          onTap: () => _handleFileTap(context, file),
        ),
        DataCell(
          Text(
            file.isDirectory ? '-' : FileUtils.formatFileSize(file.size),
          ),
        ),
        DataCell(
          Text(
            file.isDirectory 
                ? 'Folder' 
                : file.extension?.toUpperCase() ?? 'File',
          ),
        ),
        DataCell(
          Text(
            '${file.modifiedDate.day}/${file.modifiedDate.month}/${file.modifiedDate.year}',
          ),
        ),
        DataCell(
          Text(file.permissions.permissionString),
        ),
      ],
    );
  }

  void _handleFileTap(BuildContext context, FileItem file) {
    if (file.isDirectory) {
      // Navigate to directory
    } else {
      // Open file
    }
  }
}
