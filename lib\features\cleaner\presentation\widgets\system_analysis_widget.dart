import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/cleaner_bloc.dart';
import '../bloc/cleaner_event.dart';
import '../bloc/cleaner_state.dart';
import '../../domain/entities/cleanup_item.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class SystemAnalysisWidget extends StatelessWidget {
  const SystemAnalysisWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CleanerBloc, CleanerState>(
      builder: (context, state) {
        if (state is CleanerInitial) {
          return const EmptyAnalysisState();
        }

        if (state is SystemAnalysisLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                Sized<PERSON>ox(height: AppConstants.defaultPadding),
                Text('Analyzing system...'),
                SizedBox(height: AppConstants.smallPadding),
                Text(
                  'This may take a few minutes',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        if (state is SystemAnalysisInProgress) {
          return _buildAnalysisResults(
            context,
            state.partialAnalysis,
            isInProgress: true,
          );
        }

        if (state is SystemAnalysisCompleted) {
          return _buildAnalysisResults(context, state.analysis);
        }

        if (state is SystemAnalysisError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Analysis Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () {
                    context.read<CleanerBloc>().add(
                      const StartSystemAnalysisEvent(),
                    );
                  },
                  child: const Text('Retry Analysis'),
                ),
              ],
            ),
          );
        }

        return const EmptyAnalysisState();
      },
    );
  }

  Widget _buildAnalysisResults(
    BuildContext context,
    SystemAnalysis analysis, {
    bool isInProgress = false,
  }) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Storage Overview
          _buildStorageOverview(context, analysis, isInProgress),

          const SizedBox(height: AppConstants.largePadding),

          // Cleanup Opportunities
          _buildCleanupOpportunities(context, analysis),

          const SizedBox(height: AppConstants.largePadding),

          // File Type Distribution
          if (analysis.fileTypeDistribution.isNotEmpty)
            _buildFileTypeDistribution(context, analysis),

          const SizedBox(height: AppConstants.largePadding),

          // Largest Directories
          if (analysis.largestDirectories.isNotEmpty)
            _buildLargestDirectories(context, analysis),
        ],
      ),
    );
  }

  Widget _buildStorageOverview(
    BuildContext context,
    SystemAnalysis analysis,
    bool isInProgress,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: AppTheme.primaryColor),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Storage Overview',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                if (isInProgress) ...[
                  const Spacer(),
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ],
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Storage Usage Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Used: ${FileUtils.formatFileSize(analysis.usedSpace)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      'Free: ${FileUtils.formatFileSize(analysis.availableSpace)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallPadding),
                LinearProgressIndicator(
                  value: analysis.usagePercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    analysis.usagePercentage > 80
                        ? AppTheme.errorColor
                        : analysis.usagePercentage > 60
                        ? AppTheme.warningColor
                        : AppTheme.successColor,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  '${analysis.usagePercentage.toStringAsFixed(1)}% used',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Statistics
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Files',
                    analysis.totalFiles.toString(),
                    Icons.description,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Folders',
                    analysis.totalFolders.toString(),
                    Icons.folder,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Can Clean',
                    FileUtils.formatFileSize(analysis.totalCleanupSize),
                    Icons.cleaning_services,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          Text(label, style: Theme.of(context).textTheme.bodySmall),
        ],
      ),
    );
  }

  Widget _buildCleanupOpportunities(
    BuildContext context,
    SystemAnalysis analysis,
  ) {
    if (analysis.cleanupOpportunities.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cleanup Opportunities',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            ...analysis.cleanupOpportunities.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.smallPadding,
                ),
                child: Row(
                  children: [
                    Icon(
                      _getCleanupTypeIcon(entry.key),
                      color: _getCleanupTypePriority(entry.key),
                      size: 20,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(child: Text(_getCleanupTypeName(entry.key))),
                    Text(
                      FileUtils.formatFileSize(entry.value),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getCleanupTypePriority(entry.key),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFileTypeDistribution(
    BuildContext context,
    SystemAnalysis analysis,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'File Type Distribution',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            ...analysis.fileTypeDistribution.entries.take(5).map((entry) {
              return Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.smallPadding,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getFileTypeColor(entry.key),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(child: Text(entry.key)),
                    Text(
                      '${entry.value} files',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildLargestDirectories(
    BuildContext context,
    SystemAnalysis analysis,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Largest Directories',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            ...analysis.largestDirectories.entries.take(5).map((entry) {
              return Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.smallPadding,
                ),
                child: Row(
                  children: [
                    const Icon(Icons.folder, size: 20),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        entry.key.split('/').last,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      FileUtils.formatFileSize(entry.value),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  IconData _getCleanupTypeIcon(CleanupType type) {
    switch (type) {
      case CleanupType.cache:
        return Icons.cached;
      case CleanupType.temporaryFiles:
        return Icons.schedule_send;
      case CleanupType.logs:
        return Icons.article;
      case CleanupType.thumbnails:
        return Icons.image;
      case CleanupType.downloads:
        return Icons.download;
      case CleanupType.emptyFolders:
        return Icons.folder_open;
      case CleanupType.largeFiles:
        return Icons.storage;
      case CleanupType.oldFiles:
        return Icons.access_time;
      case CleanupType.apkFiles:
        return Icons.android;
      case CleanupType.residualFiles:
        return Icons.delete_sweep;
    }
  }

  Color _getCleanupTypePriority(CleanupType type) {
    switch (type) {
      case CleanupType.cache:
      case CleanupType.temporaryFiles:
        return AppTheme.successColor;
      case CleanupType.logs:
      case CleanupType.thumbnails:
      case CleanupType.emptyFolders:
      case CleanupType.residualFiles:
        return AppTheme.warningColor;
      case CleanupType.downloads:
      case CleanupType.largeFiles:
      case CleanupType.oldFiles:
      case CleanupType.apkFiles:
        return AppTheme.errorColor;
    }
  }

  String _getCleanupTypeName(CleanupType type) {
    switch (type) {
      case CleanupType.cache:
        return 'Cache Files';
      case CleanupType.temporaryFiles:
        return 'Temporary Files';
      case CleanupType.logs:
        return 'Log Files';
      case CleanupType.thumbnails:
        return 'Thumbnails';
      case CleanupType.downloads:
        return 'Downloads';
      case CleanupType.emptyFolders:
        return 'Empty Folders';
      case CleanupType.largeFiles:
        return 'Large Files';
      case CleanupType.oldFiles:
        return 'Old Files';
      case CleanupType.apkFiles:
        return 'APK Files';
      case CleanupType.residualFiles:
        return 'Residual Files';
    }
  }

  Color _getFileTypeColor(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return Colors.blue;
      case '.mp4':
      case '.avi':
      case '.mkv':
        return Colors.red;
      case '.mp3':
      case '.wav':
      case '.flac':
        return Colors.green;
      case '.pdf':
      case '.doc':
      case '.txt':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

class EmptyAnalysisState extends StatelessWidget {
  const EmptyAnalysisState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'System Analysis',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Analyze your system to find cleanup opportunities',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              context.read<CleanerBloc>().add(const StartSystemAnalysisEvent());
            },
            icon: const Icon(Icons.analytics),
            label: const Text('Start Analysis'),
          ),
        ],
      ),
    );
  }
}
