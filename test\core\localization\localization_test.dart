import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:hd_file_explorer/core/localization/models/language.dart';
import 'package:hd_file_explorer/core/localization/services/translation_service.dart';
import 'package:hd_file_explorer/core/localization/bloc/language_bloc.dart';
import 'package:hd_file_explorer/core/localization/bloc/language_event.dart';
import 'package:hd_file_explorer/core/localization/bloc/language_state.dart';

void main() {
  group('Language Model Tests', () {
    test('should create language with correct properties', () {
      const language = Language(
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
        isRTL: true,
        locale: Locale('ar', 'SA'),
      );

      expect(language.code, 'ar');
      expect(language.name, 'Arabic');
      expect(language.nativeName, 'العربية');
      expect(language.flag, '🇸🇦');
      expect(language.isRTL, true);
      expect(language.locale, const Locale('ar', 'SA'));
    });

    test('should convert language to JSON', () {
      const language = SupportedLanguages.arabic;
      final json = language.toJson();

      expect(json['code'], 'ar');
      expect(json['name'], 'Arabic');
      expect(json['nativeName'], 'العربية');
      expect(json['isRTL'], true);
      expect(json['locale']['languageCode'], 'ar');
      expect(json['locale']['countryCode'], 'SA');
    });

    test('should create language from JSON', () {
      final json = {
        'code': 'en',
        'name': 'English',
        'nativeName': 'English',
        'flag': '🇺🇸',
        'isRTL': false,
        'locale': {
          'languageCode': 'en',
          'countryCode': 'US',
        },
      };

      final language = Language.fromJson(json);

      expect(language.code, 'en');
      expect(language.name, 'English');
      expect(language.nativeName, 'English');
      expect(language.isRTL, false);
      expect(language.locale, const Locale('en', 'US'));
    });

    test('should find language by code', () {
      final language = SupportedLanguages.findByCode('ar');
      expect(language, SupportedLanguages.arabic);

      final notFound = SupportedLanguages.findByCode('xx');
      expect(notFound, null);
    });

    test('should find language by locale', () {
      final language = SupportedLanguages.findByLocale(const Locale('ar', 'SA'));
      expect(language, SupportedLanguages.arabic);

      final notFound = SupportedLanguages.findByLocale(const Locale('xx', 'XX'));
      expect(notFound, null);
    });

    test('should check if language is supported', () {
      expect(SupportedLanguages.isSupported('ar'), true);
      expect(SupportedLanguages.isSupported('en'), true);
      expect(SupportedLanguages.isSupported('xx'), false);
    });

    test('should check if language is RTL', () {
      expect(SupportedLanguages.isRTL('ar'), true);
      expect(SupportedLanguages.isRTL('en'), false);
      expect(SupportedLanguages.isRTL('xx'), false);
    });
  });

  group('Translation Service Tests', () {
    late TranslationService translationService;

    setUp(() {
      translationService = TranslationService();
    });

    test('should initialize with default values', () {
      expect(translationService.currentLanguage, SupportedLanguages.defaultLanguage);
      expect(translationService.translations, isEmpty);
    });

    test('should update translation', () {
      translationService.updateTranslation('test.key', 'Test Value');
      
      expect(translationService.translate('test.key'), 'Test Value');
      expect(translationService.hasTranslation('test.key'), true);
    });

    test('should handle nested translation keys', () {
      translationService.updateTranslation('app.name', 'HD File Explorer');
      translationService.updateTranslation('app.version', '1.0.0');
      
      expect(translationService.translate('app.name'), 'HD File Explorer');
      expect(translationService.translate('app.version'), '1.0.0');
    });

    test('should return key when translation not found', () {
      expect(translationService.translate('nonexistent.key'), 'nonexistent.key');
    });

    test('should replace parameters in translation', () {
      translationService.updateTranslation('greeting', 'Hello {name}!');
      
      final result = translationService.translate('greeting', params: {'name': 'Ahmed'});
      expect(result, 'Hello Ahmed!');
    });

    test('should handle plural translations', () {
      translationService.updateTranslation('files_one', '{count} file');
      translationService.updateTranslation('files_many', '{count} files');
      
      expect(translationService.translatePlural('files', 1), '1 file');
      expect(translationService.translatePlural('files', 5), '5 files');
    });

    test('should get translation group', () {
      translationService.updateTranslation('app.name', 'HD File Explorer');
      translationService.updateTranslation('app.version', '1.0.0');
      
      final group = translationService.getTranslationGroup('app');
      expect(group, isNotNull);
      expect(group!['name'], 'HD File Explorer');
      expect(group['version'], '1.0.0');
    });

    test('should get all translation keys', () {
      translationService.updateTranslation('app.name', 'HD File Explorer');
      translationService.updateTranslation('common.ok', 'OK');
      
      final keys = translationService.getAllKeys();
      expect(keys, contains('app.name'));
      expect(keys, contains('common.ok'));
    });

    test('should search translations', () {
      translationService.updateTranslation('app.name', 'HD File Explorer');
      translationService.updateTranslation('app.description', 'File Manager');
      translationService.updateTranslation('common.file', 'File');
      
      final results = translationService.searchTranslations('file');
      expect(results.length, greaterThan(0));
    });

    test('should clear translations', () {
      translationService.updateTranslation('test.key', 'Test Value');
      expect(translationService.hasTranslation('test.key'), true);
      
      translationService.clearTranslations();
      expect(translationService.hasTranslation('test.key'), false);
    });

    test('should merge translations', () {
      translationService.updateTranslation('existing.key', 'Existing Value');
      
      final newTranslations = {
        'new': {'key': 'New Value'},
        'existing': {'key': 'Updated Value'},
      };
      
      translationService.mergeTranslations(newTranslations);
      
      expect(translationService.translate('new.key'), 'New Value');
      expect(translationService.translate('existing.key'), 'Updated Value');
    });

    test('should export translations', () {
      translationService.updateTranslation('test.key', 'Test Value');
      
      final exported = translationService.exportTranslations();
      expect(exported, isNotEmpty);
      expect(exported, contains('test'));
    });

    test('should import translations', () {
      const jsonString = '{"imported": {"key": "Imported Value"}}';
      
      translationService.importTranslations(jsonString);
      
      expect(translationService.translate('imported.key'), 'Imported Value');
    });
  });

  group('Language BLoC Tests', () {
    late LanguageBloc languageBloc;
    late TranslationService translationService;
    late SharedPreferences sharedPreferences;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      sharedPreferences = await SharedPreferences.getInstance();
      translationService = TranslationService();
      languageBloc = LanguageBloc(translationService, sharedPreferences);
    });

    tearDown(() {
      languageBloc.close();
    });

    test('should emit initial state', () {
      expect(languageBloc.state, isA<LanguageInitial>());
    });

    test('should load saved language', () async {
      // Set up saved language
      await sharedPreferences.setString('selected_language_code', 'ar');
      
      languageBloc.add(const LoadSavedLanguageEvent());
      
      await expectLater(
        languageBloc.stream,
        emitsInOrder([
          isA<LanguageLoading>(),
          isA<LanguageLoaded>(),
        ]),
      );
    });

    test('should change language', () async {
      languageBloc.add(ChangeLanguageEvent(SupportedLanguages.arabic));
      
      await expectLater(
        languageBloc.stream,
        emitsInOrder([
          isA<LanguageLoading>(),
          isA<LanguageChanged>(),
        ]),
      );
    });

    test('should reload translations', () async {
      languageBloc.add(const ReloadTranslationsEvent());
      
      await expectLater(
        languageBloc.stream,
        emitsInOrder([
          isA<LanguageLoading>(),
          isA<TranslationsReloaded>(),
        ]),
      );
    });

    test('should update translation', () async {
      languageBloc.add(const UpdateTranslationEvent('test.key', 'Test Value'));
      
      await expectLater(
        languageBloc.stream,
        emits(isA<TranslationsUpdated>()),
      );
    });

    test('should clear translations', () async {
      languageBloc.add(const ClearTranslationsEvent());
      
      await expectLater(
        languageBloc.stream,
        emits(isA<TranslationsCleared>()),
      );
    });

    test('should search translations', () async {
      languageBloc.add(const SearchTranslationsEvent('test'));
      
      await expectLater(
        languageBloc.stream,
        emits(isA<TranslationSearchResults>()),
      );
    });

    test('should export translations', () async {
      languageBloc.add(const ExportTranslationsEvent());
      
      await expectLater(
        languageBloc.stream,
        emits(isA<TranslationsExported>()),
      );
    });

    test('should detect system language', () async {
      languageBloc.add(const DetectSystemLanguageEvent());
      
      await expectLater(
        languageBloc.stream,
        emits(isA<SystemLanguageDetected>()),
      );
    });

    test('should reset to default language', () async {
      languageBloc.add(const ResetToDefaultLanguageEvent());
      
      await expectLater(
        languageBloc.stream,
        emitsInOrder([
          isA<LanguageLoading>(),
          isA<LanguageResetToDefault>(),
        ]),
      );
    });

    test('should save language preferences', () async {
      final preferences = {'autoDetect': true};
      languageBloc.add(SaveLanguagePreferencesEvent(preferences));
      
      await expectLater(
        languageBloc.stream,
        emits(isA<LanguagePreferencesSaved>()),
      );
    });

    test('should load language preferences', () async {
      languageBloc.add(const LoadLanguagePreferencesEvent());
      
      await expectLater(
        languageBloc.stream,
        emits(isA<LanguagePreferencesLoaded>()),
      );
    });
  });

  group('Integration Tests', () {
    testWidgets('should display translated text', (WidgetTester tester) async {
      // This would require a more complex setup with actual widget testing
      // For now, we'll skip this as it requires the full app context
    });

    testWidgets('should change language dynamically', (WidgetTester tester) async {
      // This would test the actual language switching in the UI
      // Requires full app setup with BlocProvider
    });

    testWidgets('should handle RTL layout', (WidgetTester tester) async {
      // This would test RTL layout changes
      // Requires testing with Arabic language
    });
  });
}
