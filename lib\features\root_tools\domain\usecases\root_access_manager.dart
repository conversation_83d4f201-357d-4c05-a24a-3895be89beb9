import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/root_access.dart';
import '../repositories/root_tools_repository.dart';

@lazySingleton
class RootAccessManager {
  final RootToolsRepository repository;

  const RootAccessManager(this.repository);

  /// Check if device has root access
  ResultFuture<RootAccess> checkRootAccess() async {
    return await repository.checkRootAccess();
  }

  /// Request root permission from user
  ResultFuture<bool> requestRootPermission() async {
    return await repository.requestRootPermission();
  }

  /// Revoke root permission
  ResultVoid revokeRootPermission() async {
    return await repository.revokeRootPermission();
  }

  /// Get list of available root commands
  ResultFuture<List<String>> getAvailableCommands() async {
    return await repository.getAvailableCommands();
  }

  /// Execute a single root command
  ResultFuture<RootCommandResult> executeCommand(String command) async {
    return await repository.executeCommand(command);
  }

  /// Execute command with timeout
  ResultFuture<RootCommandResult> executeCommandWithTimeout(
    String command,
    Duration timeout,
  ) async {
    return await repository.executeCommandWithTimeout(command, timeout);
  }

  /// Execute multiple commands in batch
  ResultFuture<List<RootCommandResult>> executeBatchCommands(
    List<String> commands,
  ) async {
    return await repository.executeBatchCommands(commands);
  }

  /// Execute command and get real-time output stream
  Stream<String> executeCommandStream(String command) {
    return repository.executeCommandStream(command);
  }

  /// Validate command safety before execution
  bool isCommandSafe(String command) {
    // List of dangerous commands that should be blocked or warned about
    final dangerousCommands = [
      'rm -rf /',
      'dd if=/dev/zero',
      'mkfs.',
      'fdisk',
      'parted',
      'format',
      '> /dev/',
      'chmod 000',
    ];

    final lowerCommand = command.toLowerCase().trim();
    
    for (final dangerous in dangerousCommands) {
      if (lowerCommand.contains(dangerous.toLowerCase())) {
        return false;
      }
    }

    return true;
  }

  /// Get command risk level
  CommandRiskLevel getCommandRiskLevel(String command) {
    final lowerCommand = command.toLowerCase().trim();

    // High risk commands
    final highRiskPatterns = [
      'rm -rf',
      'dd if=',
      'mkfs',
      'fdisk',
      'parted',
      'format',
      'chmod 000',
      'chown root',
      'mount -o remount',
    ];

    // Medium risk commands
    final mediumRiskPatterns = [
      'rm ',
      'mv ',
      'cp ',
      'chmod',
      'chown',
      'kill -9',
      'reboot',
      'shutdown',
    ];

    // Low risk commands
    final lowRiskPatterns = [
      'ls',
      'cat',
      'grep',
      'find',
      'ps',
      'top',
      'df',
      'du',
      'mount',
      'lsof',
    ];

    for (final pattern in highRiskPatterns) {
      if (lowerCommand.contains(pattern)) {
        return CommandRiskLevel.high;
      }
    }

    for (final pattern in mediumRiskPatterns) {
      if (lowerCommand.contains(pattern)) {
        return CommandRiskLevel.medium;
      }
    }

    for (final pattern in lowRiskPatterns) {
      if (lowerCommand.contains(pattern)) {
        return CommandRiskLevel.low;
      }
    }

    return CommandRiskLevel.unknown;
  }

  /// Get command description and help
  String getCommandDescription(String command) {
    final commandMap = {
      'ls': 'List directory contents',
      'cat': 'Display file contents',
      'grep': 'Search text patterns',
      'find': 'Search for files and directories',
      'ps': 'Display running processes',
      'top': 'Display system processes',
      'df': 'Display filesystem disk space usage',
      'du': 'Display directory space usage',
      'mount': 'Mount filesystems',
      'umount': 'Unmount filesystems',
      'chmod': 'Change file permissions',
      'chown': 'Change file ownership',
      'kill': 'Terminate processes',
      'reboot': 'Restart the system',
      'shutdown': 'Shutdown the system',
      'rm': 'Remove files and directories',
      'mv': 'Move/rename files and directories',
      'cp': 'Copy files and directories',
      'mkdir': 'Create directories',
      'rmdir': 'Remove empty directories',
      'touch': 'Create empty files or update timestamps',
      'ln': 'Create links between files',
      'tar': 'Archive files',
      'gzip': 'Compress files',
      'gunzip': 'Decompress files',
      'wget': 'Download files from web',
      'curl': 'Transfer data from servers',
      'ping': 'Test network connectivity',
      'netstat': 'Display network connections',
      'iptables': 'Configure firewall rules',
      'service': 'Control system services',
      'systemctl': 'Control systemd services',
      'crontab': 'Schedule tasks',
      'su': 'Switch user',
      'sudo': 'Execute commands as another user',
      'passwd': 'Change user password',
      'useradd': 'Add user account',
      'userdel': 'Delete user account',
      'groupadd': 'Add group',
      'groupdel': 'Delete group',
    };

    final baseCommand = command.split(' ').first.toLowerCase();
    return commandMap[baseCommand] ?? 'Unknown command';
  }

  /// Get suggested commands for common tasks
  List<String> getSuggestedCommands(String task) {
    final taskCommands = {
      'list_files': ['ls -la', 'find /path -type f', 'du -sh *'],
      'system_info': ['uname -a', 'cat /proc/version', 'df -h', 'free -h'],
      'processes': ['ps aux', 'top', 'htop', 'pgrep process_name'],
      'network': ['netstat -tuln', 'ss -tuln', 'ping google.com'],
      'permissions': ['ls -la', 'chmod 755 file', 'chown user:group file'],
      'services': ['service --status-all', 'systemctl status service'],
      'logs': ['logcat', 'dmesg', 'cat /var/log/syslog'],
      'cleanup': ['rm -rf /tmp/*', 'apt autoremove', 'yum clean all'],
      'backup': ['tar -czf backup.tar.gz /path', 'rsync -av source dest'],
      'monitoring': ['top', 'htop', 'iotop', 'nethogs'],
    };

    return taskCommands[task.toLowerCase()] ?? [];
  }
}

enum CommandRiskLevel {
  low,
  medium,
  high,
  unknown,
}
