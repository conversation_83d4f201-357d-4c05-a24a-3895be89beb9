import 'package:equatable/equatable.dart';

abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

class LoadDashboardEvent extends DashboardEvent {
  const LoadDashboardEvent();
}

class RefreshStorageInfoEvent extends DashboardEvent {
  const RefreshStorageInfoEvent();
}

class LoadQuickAccessEvent extends DashboardEvent {
  const LoadQuickAccessEvent();
}

class LoadRecentFilesEvent extends DashboardEvent {
  const LoadRecentFilesEvent();
}

class NavigateToQuickAccessEvent extends DashboardEvent {
  final QuickAccessType type;

  const NavigateToQuickAccessEvent(this.type);

  @override
  List<Object?> get props => [type];
}

enum QuickAccessType {
  images,
  videos,
  audio,
  documents,
  downloads,
  apps,
  favorites,
}
