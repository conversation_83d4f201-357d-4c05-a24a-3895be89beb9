import 'package:flutter/material.dart';
import '../../domain/entities/file_operation.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class OperationProgressCard extends StatelessWidget {
  final dynamic operation; // FileOperation
  final VoidCallback? onPause;
  final VoidCallback? onResume;
  final VoidCallback? onCancel;
  final bool isCompleted;

  const OperationProgressCard({
    super.key,
    required this.operation,
    this.onPause,
    this.onResume,
    this.onCancel,
    this.isCompleted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                _buildOperationIcon(),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getOperationTitle(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getOperationSubtitle(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Progress Bar (only for active operations)
            if (!isCompleted && _shouldShowProgress()) ...[
              LinearProgressIndicator(
                value: operation.progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${(operation.progress * 100).toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  if (operation.speedText.isNotEmpty)
                    Text(
                      operation.speedText,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                ],
              ),
              
              if (operation.remainingTimeText.isNotEmpty) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'Remaining: ${operation.remainingTimeText}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
            
            // File count and size info
            if (operation.totalFiles > 0 || operation.totalBytes > 0) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Row(
                children: [
                  if (operation.totalFiles > 0) ...[
                    Icon(
                      Icons.insert_drive_file,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${operation.processedFiles}/${operation.totalFiles} files',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                  if (operation.totalFiles > 0 && operation.totalBytes > 0)
                    const SizedBox(width: AppConstants.defaultPadding),
                  if (operation.totalBytes > 0) ...[
                    Icon(
                      Icons.data_usage,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${FileUtils.formatFileSize(operation.processedBytes)}/${FileUtils.formatFileSize(operation.totalBytes)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ],
              ),
            ],
            
            // Duration info
            if (operation.duration != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDuration(operation.duration),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
            
            // Error message
            if (operation.errorMessage != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 16,
                      color: AppTheme.errorColor,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        operation.errorMessage!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Action buttons (only for active operations)
            if (!isCompleted && _canControlOperation()) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (operation.status.toString() == 'FileOperationStatus.inProgress' && onPause != null)
                    TextButton.icon(
                      onPressed: onPause,
                      icon: const Icon(Icons.pause),
                      label: const Text('Pause'),
                    ),
                  if (operation.status.toString() == 'FileOperationStatus.paused' && onResume != null)
                    TextButton.icon(
                      onPressed: onResume,
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Resume'),
                    ),
                  if (onCancel != null) ...[
                    const SizedBox(width: AppConstants.smallPadding),
                    TextButton.icon(
                      onPressed: onCancel,
                      icon: const Icon(Icons.cancel),
                      label: const Text('Cancel'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppTheme.errorColor,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOperationIcon() {
    IconData iconData;
    Color iconColor;

    switch (operation.type.toString()) {
      case 'FileOperationType.copy':
        iconData = Icons.copy;
        iconColor = Colors.blue;
        break;
      case 'FileOperationType.move':
        iconData = Icons.drive_file_move;
        iconColor = Colors.orange;
        break;
      case 'FileOperationType.delete':
        iconData = Icons.delete;
        iconColor = Colors.red;
        break;
      case 'FileOperationType.compress':
        iconData = Icons.compress;
        iconColor = Colors.green;
        break;
      case 'FileOperationType.extract':
        iconData = Icons.folder_zip;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.settings;
        iconColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (operation.status.toString()) {
      case 'FileOperationStatus.pending':
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        statusText = 'Pending';
        break;
      case 'FileOperationStatus.inProgress':
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        statusText = 'In Progress';
        break;
      case 'FileOperationStatus.paused':
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        statusText = 'Paused';
        break;
      case 'FileOperationStatus.completed':
        backgroundColor = AppTheme.successColor;
        textColor = Colors.white;
        statusText = 'Completed';
        break;
      case 'FileOperationStatus.failed':
        backgroundColor = AppTheme.errorColor;
        textColor = Colors.white;
        statusText = 'Failed';
        break;
      case 'FileOperationStatus.cancelled':
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        statusText = 'Cancelled';
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        statusText = 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallPadding,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getOperationTitle() {
    switch (operation.type.toString()) {
      case 'FileOperationType.copy':
        return 'Copying Files';
      case 'FileOperationType.move':
        return 'Moving Files';
      case 'FileOperationType.delete':
        return 'Deleting Files';
      case 'FileOperationType.compress':
        return 'Compressing Files';
      case 'FileOperationType.extract':
        return 'Extracting Archive';
      default:
        return 'File Operation';
    }
  }

  String _getOperationSubtitle() {
    if (operation.sourcePaths.length == 1) {
      return operation.sourcePaths.first;
    } else {
      return '${operation.sourcePaths.length} items';
    }
  }

  Color _getProgressColor() {
    switch (operation.status.toString()) {
      case 'FileOperationStatus.inProgress':
        return Colors.blue;
      case 'FileOperationStatus.paused':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  bool _shouldShowProgress() {
    return operation.status.toString() == 'FileOperationStatus.inProgress' ||
           operation.status.toString() == 'FileOperationStatus.paused';
  }

  bool _canControlOperation() {
    return operation.status.toString() == 'FileOperationStatus.inProgress' ||
           operation.status.toString() == 'FileOperationStatus.paused';
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m ${duration.inSeconds % 60}s';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}
