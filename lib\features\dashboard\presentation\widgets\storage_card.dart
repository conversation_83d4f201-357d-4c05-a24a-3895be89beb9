import 'package:flutter/material.dart';
import '../../domain/entities/storage_info.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class StorageCard extends StatelessWidget {
  final StorageInfo storage;

  const StorageCard({
    super.key,
    required this.storage,
  });

  @override
  Widget build(BuildContext context) {
    final usagePercentage = storage.usagePercentage;
    final usedSpaceFormatted = FileUtils.formatFileSize(storage.usedSpace);
    final totalSpaceFormatted = FileUtils.formatFileSize(storage.totalSpace);
    
    Color getUsageColor() {
      if (usagePercentage < 50) return AppTheme.successColor;
      if (usagePercentage < 80) return AppTheme.warningColor;
      return AppTheme.errorColor;
    }

    IconData getStorageIcon() {
      switch (storage.type) {
        case StorageType.internal:
          return Icons.phone_android;
        case StorageType.external:
          return Icons.sd_card;
        case StorageType.usb:
          return Icons.usb;
        case StorageType.network:
          return Icons.cloud;
      }
    }

    return SizedBox(
      width: 280,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    getStorageIcon(),
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Text(
                      storage.displayName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (storage.isRemovable)
                    Icon(
                      Icons.eject,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Usage Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '$usedSpaceFormatted used',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        '${usagePercentage.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: getUsageColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.smallPadding),
                  
                  LinearProgressIndicator(
                    value: usagePercentage / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(getUsageColor()),
                    minHeight: 6,
                  ),
                  
                  const SizedBox(height: AppConstants.smallPadding),
                  
                  Text(
                    '$totalSpaceFormatted total',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
