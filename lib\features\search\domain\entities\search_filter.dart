import 'package:equatable/equatable.dart';

class SearchFilter extends Equatable {
  final String? query;
  final List<SearchFileType> fileTypes;
  final int? minSize;
  final int? maxSize;
  final DateTime? modifiedAfter;
  final DateTime? modifiedBefore;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final bool includeHidden;
  final bool includeSystemFiles;
  final List<String>? extensions;
  final List<String>? searchPaths;
  final bool caseSensitive;
  final bool useRegex;
  final SearchDepth depth;

  const SearchFilter({
    this.query,
    this.fileTypes = const [],
    this.minSize,
    this.maxSize,
    this.modifiedAfter,
    this.modifiedBefore,
    this.createdAfter,
    this.createdBefore,
    this.includeHidden = false,
    this.includeSystemFiles = false,
    this.extensions,
    this.searchPaths,
    this.caseSensitive = false,
    this.useRegex = false,
    this.depth = SearchDepth.unlimited,
  });

  SearchFilter copyWith({
    String? query,
    List<SearchFileType>? fileTypes,
    int? minSize,
    int? maxSize,
    DateTime? modifiedAfter,
    DateTime? modifiedBefore,
    DateTime? createdAfter,
    DateTime? createdBefore,
    bool? includeHidden,
    bool? includeSystemFiles,
    List<String>? extensions,
    List<String>? searchPaths,
    bool? caseSensitive,
    bool? useRegex,
    SearchDepth? depth,
  }) {
    return SearchFilter(
      query: query ?? this.query,
      fileTypes: fileTypes ?? this.fileTypes,
      minSize: minSize ?? this.minSize,
      maxSize: maxSize ?? this.maxSize,
      modifiedAfter: modifiedAfter ?? this.modifiedAfter,
      modifiedBefore: modifiedBefore ?? this.modifiedBefore,
      createdAfter: createdAfter ?? this.createdAfter,
      createdBefore: createdBefore ?? this.createdBefore,
      includeHidden: includeHidden ?? this.includeHidden,
      includeSystemFiles: includeSystemFiles ?? this.includeSystemFiles,
      extensions: extensions ?? this.extensions,
      searchPaths: searchPaths ?? this.searchPaths,
      caseSensitive: caseSensitive ?? this.caseSensitive,
      useRegex: useRegex ?? this.useRegex,
      depth: depth ?? this.depth,
    );
  }

  @override
  List<Object?> get props => [
        query,
        fileTypes,
        minSize,
        maxSize,
        modifiedAfter,
        modifiedBefore,
        createdAfter,
        createdBefore,
        includeHidden,
        includeSystemFiles,
        extensions,
        searchPaths,
        caseSensitive,
        useRegex,
        depth,
      ];
}

enum SearchFileType {
  all,
  images,
  videos,
  audio,
  documents,
  archives,
  applications,
  folders,
}

enum SearchDepth {
  currentFolder,
  oneLevel,
  twoLevels,
  unlimited,
}

class DuplicateSearchFilter extends Equatable {
  final List<String> searchPaths;
  final DuplicateSearchMethod method;
  final int? minFileSize;
  final List<SearchFileType> fileTypes;
  final bool includeHidden;
  final bool includeSystemFiles;

  const DuplicateSearchFilter({
    required this.searchPaths,
    this.method = DuplicateSearchMethod.nameAndSize,
    this.minFileSize,
    this.fileTypes = const [SearchFileType.all],
    this.includeHidden = false,
    this.includeSystemFiles = false,
  });

  @override
  List<Object?> get props => [
        searchPaths,
        method,
        minFileSize,
        fileTypes,
        includeHidden,
        includeSystemFiles,
      ];
}

enum DuplicateSearchMethod {
  nameOnly,
  nameAndSize,
  contentHash,
  fuzzyName,
}
