import 'dart:io';
import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:archive/archive.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;

import '../models/file_operation_model.dart';
import '../../domain/entities/file_operation.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/constants/app_constants.dart';

abstract class FileOperationsDataSource {
  Future<String> copyFiles(List<String> sourcePaths, String destinationPath);
  Future<String> moveFiles(List<String> sourcePaths, String destinationPath);
  Future<String> deleteFiles(List<String> filePaths);
  Future<String> compressFiles(
    List<String> sourcePaths,
    String destinationPath,
    CompressionOptions options,
  );
  Future<String> extractArchive(
    String archivePath,
    String destinationPath,
    String? password,
  );
  Future<List<FileOperationModel>> getActiveOperations();
  Future<FileOperationModel> getOperation(String operationId);
  Future<void> pauseOperation(String operationId);
  Future<void> resumeOperation(String operationId);
  Future<void> cancelOperation(String operationId);
  Stream<FileOperationModel> watchOperation(String operationId);
  Stream<List<FileOperationModel>> watchAllOperations();
  Future<int> calculateTotalSize(List<String> paths);
  Future<bool> hasEnoughSpace(String destinationPath, int requiredBytes);
  Future<List<String>> getConflictingFiles(
    List<String> sourcePaths,
    String destinationPath,
  );
}

@LazySingleton(as: FileOperationsDataSource)
class FileOperationsDataSourceImpl implements FileOperationsDataSource {
  final Map<String, FileOperationModel> _operations = {};
  final Map<String, StreamController<FileOperationModel>>
  _operationControllers = {};
  final StreamController<List<FileOperationModel>> _allOperationsController =
      StreamController<List<FileOperationModel>>.broadcast();
  final Uuid _uuid = const Uuid();

  @override
  Future<String> copyFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    final operationId = _uuid.v4();
    final operation = FileOperationModel(
      id: operationId,
      type: FileOperationType.copy,
      sourcePaths: sourcePaths,
      destinationPath: destinationPath,
      status: FileOperationStatus.pending,
      startTime: DateTime.now(),
    );

    _operations[operationId] = operation;
    _startOperation(operationId);

    return operationId;
  }

  @override
  Future<String> moveFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    final operationId = _uuid.v4();
    final operation = FileOperationModel(
      id: operationId,
      type: FileOperationType.move,
      sourcePaths: sourcePaths,
      destinationPath: destinationPath,
      status: FileOperationStatus.pending,
      startTime: DateTime.now(),
    );

    _operations[operationId] = operation;
    _startOperation(operationId);

    return operationId;
  }

  @override
  Future<String> deleteFiles(List<String> filePaths) async {
    final operationId = _uuid.v4();
    final operation = FileOperationModel(
      id: operationId,
      type: FileOperationType.delete,
      sourcePaths: filePaths,
      destinationPath: '',
      status: FileOperationStatus.pending,
      startTime: DateTime.now(),
    );

    _operations[operationId] = operation;
    _startOperation(operationId);

    return operationId;
  }

  @override
  Future<String> compressFiles(
    List<String> sourcePaths,
    String destinationPath,
    CompressionOptions options,
  ) async {
    final operationId = _uuid.v4();
    final operation = FileOperationModel(
      id: operationId,
      type: FileOperationType.compress,
      sourcePaths: sourcePaths,
      destinationPath: destinationPath,
      status: FileOperationStatus.pending,
      startTime: DateTime.now(),
    );

    _operations[operationId] = operation;
    _startCompressionOperation(operationId, options);

    return operationId;
  }

  @override
  Future<String> extractArchive(
    String archivePath,
    String destinationPath,
    String? password,
  ) async {
    final operationId = _uuid.v4();
    final operation = FileOperationModel(
      id: operationId,
      type: FileOperationType.extract,
      sourcePaths: [archivePath],
      destinationPath: destinationPath,
      status: FileOperationStatus.pending,
      startTime: DateTime.now(),
    );

    _operations[operationId] = operation;
    _startExtractionOperation(operationId, password);

    return operationId;
  }

  Future<void> _startOperation(String operationId) async {
    final operation = _operations[operationId];
    if (operation == null) return;

    try {
      _updateOperation(
        operationId,
        operation.copyWith(status: FileOperationStatus.inProgress),
      );

      switch (operation.type) {
        case FileOperationType.copy:
          await _performCopyOperation(operationId);
          break;
        case FileOperationType.move:
          await _performMoveOperation(operationId);
          break;
        case FileOperationType.delete:
          await _performDeleteOperation(operationId);
          break;
        default:
          throw const FileSystemException(
            message: 'Unsupported operation type',
          );
      }

      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.completed,
          progress: 1.0,
          endTime: DateTime.now(),
        ),
      );
    } catch (e) {
      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.failed,
          errorMessage: e.toString(),
          endTime: DateTime.now(),
        ),
      );
    }
  }

  Future<void> _performCopyOperation(String operationId) async {
    final operation = _operations[operationId]!;
    int processedFiles = 0;
    int processedBytes = 0;

    // Calculate total size first
    final totalSize = await calculateTotalSize(operation.sourcePaths);
    _updateOperation(operationId, operation.copyWith(totalBytes: totalSize));

    for (final sourcePath in operation.sourcePaths) {
      if (_operations[operationId]?.status != FileOperationStatus.inProgress) {
        break; // Operation was cancelled or paused
      }

      final sourceEntity = await FileSystemEntity.type(sourcePath);
      final fileName = path.basename(sourcePath);
      final destinationFilePath = path.join(
        operation.destinationPath,
        fileName,
      );

      if (sourceEntity == FileSystemEntityType.file) {
        await _copyFile(sourcePath, destinationFilePath, operationId);
        processedFiles++;
        final fileSize = await File(sourcePath).length();
        processedBytes += fileSize;
      } else if (sourceEntity == FileSystemEntityType.directory) {
        await _copyDirectory(sourcePath, destinationFilePath, operationId);
        processedFiles++;
      }

      final progress = processedBytes / totalSize;
      _updateOperation(
        operationId,
        operation.copyWith(
          processedFiles: processedFiles,
          processedBytes: processedBytes,
          progress: progress,
        ),
      );
    }
  }

  Future<void> _copyFile(
    String sourcePath,
    String destinationPath,
    String operationId,
  ) async {
    final sourceFile = File(sourcePath);
    final destinationFile = File(destinationPath);

    // Create destination directory if it doesn't exist
    await destinationFile.parent.create(recursive: true);

    // Copy file in chunks to allow for progress updates
    final sourceStream = sourceFile.openRead();
    final destinationSink = destinationFile.openWrite();

    await sourceStream.pipe(destinationSink);
    await destinationSink.close();
  }

  Future<void> _copyDirectory(
    String sourcePath,
    String destinationPath,
    String operationId,
  ) async {
    final sourceDir = Directory(sourcePath);
    final destinationDir = Directory(destinationPath);

    await destinationDir.create(recursive: true);

    await for (final entity in sourceDir.list(recursive: true)) {
      if (_operations[operationId]?.status != FileOperationStatus.inProgress) {
        break;
      }

      final relativePath = path.relative(entity.path, from: sourcePath);
      final destinationEntityPath = path.join(destinationPath, relativePath);

      if (entity is File) {
        await _copyFile(entity.path, destinationEntityPath, operationId);
      } else if (entity is Directory) {
        await Directory(destinationEntityPath).create(recursive: true);
      }
    }
  }

  Future<void> _performMoveOperation(String operationId) async {
    // Move is essentially copy + delete
    await _performCopyOperation(operationId);

    if (_operations[operationId]?.status == FileOperationStatus.inProgress) {
      // Delete source files after successful copy
      final operation = _operations[operationId]!;
      for (final sourcePath in operation.sourcePaths) {
        final entity = await FileSystemEntity.type(sourcePath);
        if (entity == FileSystemEntityType.file) {
          await File(sourcePath).delete();
        } else if (entity == FileSystemEntityType.directory) {
          await Directory(sourcePath).delete(recursive: true);
        }
      }
    }
  }

  Future<void> _performDeleteOperation(String operationId) async {
    final operation = _operations[operationId]!;
    int processedFiles = 0;

    for (final filePath in operation.sourcePaths) {
      if (_operations[operationId]?.status != FileOperationStatus.inProgress) {
        break;
      }

      final entity = await FileSystemEntity.type(filePath);
      if (entity == FileSystemEntityType.file) {
        await File(filePath).delete();
      } else if (entity == FileSystemEntityType.directory) {
        await Directory(filePath).delete(recursive: true);
      }

      processedFiles++;
      final progress = processedFiles / operation.sourcePaths.length;
      _updateOperation(
        operationId,
        operation.copyWith(processedFiles: processedFiles, progress: progress),
      );
    }
  }

  Future<void> _startCompressionOperation(
    String operationId,
    CompressionOptions options,
  ) async {
    // Implementation for compression using archive package
    // This is a simplified version
    final operation = _operations[operationId];
    if (operation == null) return;

    try {
      _updateOperation(
        operationId,
        operation.copyWith(status: FileOperationStatus.inProgress),
      );

      final archive = Archive();

      // Add files to archive
      for (final sourcePath in operation.sourcePaths) {
        await _addToArchive(archive, sourcePath);
      }

      // Create compressed file
      final encoder = ZipEncoder();
      final bytes = encoder.encode(archive);

      if (bytes != null) {
        final outputFile = File(operation.destinationPath);
        await outputFile.writeAsBytes(bytes);
      }

      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.completed,
          progress: 1.0,
          endTime: DateTime.now(),
        ),
      );
    } catch (e) {
      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.failed,
          errorMessage: e.toString(),
          endTime: DateTime.now(),
        ),
      );
    }
  }

  Future<void> _addToArchive(Archive archive, String filePath) async {
    final entity = await FileSystemEntity.type(filePath);

    if (entity == FileSystemEntityType.file) {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final archiveFile = ArchiveFile(
        path.basename(filePath),
        bytes.length,
        bytes,
      );
      archive.addFile(archiveFile);
    } else if (entity == FileSystemEntityType.directory) {
      final dir = Directory(filePath);
      await for (final entity in dir.list(recursive: true)) {
        if (entity is File) {
          final bytes = await entity.readAsBytes();
          final relativePath = path.relative(entity.path, from: filePath);
          final archiveFile = ArchiveFile(relativePath, bytes.length, bytes);
          archive.addFile(archiveFile);
        }
      }
    }
  }

  Future<void> _startExtractionOperation(
    String operationId,
    String? password,
  ) async {
    final operation = _operations[operationId];
    if (operation == null) return;

    try {
      _updateOperation(
        operationId,
        operation.copyWith(status: FileOperationStatus.inProgress),
      );

      final archiveFile = File(operation.sourcePaths.first);
      final bytes = await archiveFile.readAsBytes();

      Archive archive;
      final extension =
          path.extension(operation.sourcePaths.first).toLowerCase();

      switch (extension) {
        case '.zip':
          archive = ZipDecoder().decodeBytes(bytes);
          break;
        case '.tar':
          archive = TarDecoder().decodeBytes(bytes);
          break;
        case '.gz':
          final decompressed = GZipDecoder().decodeBytes(bytes);
          archive = TarDecoder().decodeBytes(decompressed);
          break;
        default:
          throw const CompressionException(
            message: 'Unsupported archive format',
          );
      }

      // Extract files
      for (final file in archive) {
        final filePath = path.join(operation.destinationPath, file.name);

        if (file.isFile) {
          final outputFile = File(filePath);
          await outputFile.parent.create(recursive: true);
          await outputFile.writeAsBytes(file.content as List<int>);
        } else {
          await Directory(filePath).create(recursive: true);
        }
      }

      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.completed,
          progress: 1.0,
          endTime: DateTime.now(),
        ),
      );
    } catch (e) {
      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.failed,
          errorMessage: e.toString(),
          endTime: DateTime.now(),
        ),
      );
    }
  }

  void _updateOperation(String operationId, FileOperationModel operation) {
    _operations[operationId] = operation;

    // Notify operation-specific listeners
    if (_operationControllers.containsKey(operationId)) {
      _operationControllers[operationId]!.add(operation);
    }

    // Notify all operations listeners
    _allOperationsController.add(_operations.values.toList());
  }

  @override
  Future<List<FileOperationModel>> getActiveOperations() async {
    return _operations.values
        .where(
          (op) =>
              op.status == FileOperationStatus.inProgress ||
              op.status == FileOperationStatus.pending ||
              op.status == FileOperationStatus.paused,
        )
        .toList();
  }

  @override
  Future<FileOperationModel> getOperation(String operationId) async {
    final operation = _operations[operationId];
    if (operation == null) {
      throw const FileSystemException(message: 'Operation not found');
    }
    return operation;
  }

  @override
  Future<void> pauseOperation(String operationId) async {
    final operation = _operations[operationId];
    if (operation != null &&
        operation.status == FileOperationStatus.inProgress) {
      _updateOperation(
        operationId,
        operation.copyWith(status: FileOperationStatus.paused),
      );
    }
  }

  @override
  Future<void> resumeOperation(String operationId) async {
    final operation = _operations[operationId];
    if (operation != null && operation.status == FileOperationStatus.paused) {
      _updateOperation(
        operationId,
        operation.copyWith(status: FileOperationStatus.inProgress),
      );
      _startOperation(operationId);
    }
  }

  @override
  Future<void> cancelOperation(String operationId) async {
    final operation = _operations[operationId];
    if (operation != null) {
      _updateOperation(
        operationId,
        operation.copyWith(
          status: FileOperationStatus.cancelled,
          endTime: DateTime.now(),
        ),
      );
    }
  }

  @override
  Stream<FileOperationModel> watchOperation(String operationId) {
    if (!_operationControllers.containsKey(operationId)) {
      _operationControllers[operationId] =
          StreamController<FileOperationModel>.broadcast();
    }
    return _operationControllers[operationId]!.stream;
  }

  @override
  Stream<List<FileOperationModel>> watchAllOperations() {
    return _allOperationsController.stream;
  }

  @override
  Future<int> calculateTotalSize(List<String> paths) async {
    int totalSize = 0;

    for (final path in paths) {
      final entity = await FileSystemEntity.type(path);

      if (entity == FileSystemEntityType.file) {
        final file = File(path);
        totalSize += await file.length();
      } else if (entity == FileSystemEntityType.directory) {
        totalSize += await _calculateDirectorySize(path);
      }
    }

    return totalSize;
  }

  Future<int> _calculateDirectorySize(String dirPath) async {
    int size = 0;
    final dir = Directory(dirPath);

    await for (final entity in dir.list(recursive: true)) {
      if (entity is File) {
        try {
          size += await entity.length();
        } catch (e) {
          // Skip files that can't be accessed
        }
      }
    }

    return size;
  }

  @override
  Future<bool> hasEnoughSpace(String destinationPath, int requiredBytes) async {
    try {
      // This is a simplified check - in a real implementation,
      // you would check the actual available space on the filesystem
      final destinationDir = Directory(destinationPath);
      if (!await destinationDir.exists()) {
        await destinationDir.create(recursive: true);
      }

      // For now, assume we have enough space
      // In a real implementation, you would use platform-specific code
      // to check available disk space
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<String>> getConflictingFiles(
    List<String> sourcePaths,
    String destinationPath,
  ) async {
    final conflicts = <String>[];

    for (final sourcePath in sourcePaths) {
      final fileName = path.basename(sourcePath);
      final destinationFilePath = path.join(destinationPath, fileName);

      if (await FileSystemEntity.type(destinationFilePath) !=
          FileSystemEntityType.notFound) {
        conflicts.add(destinationFilePath);
      }
    }

    return conflicts;
  }
}
