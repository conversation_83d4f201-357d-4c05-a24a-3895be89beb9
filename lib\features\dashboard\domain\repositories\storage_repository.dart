import '../../../../core/utils/typedef.dart';
import '../entities/storage_info.dart';

abstract class StorageRepository {
  ResultFuture<List<StorageInfo>> getStorageInfo();
  ResultFuture<StorageInfo> getInternalStorageInfo();
  ResultFuture<List<StorageInfo>> getExternalStorageInfo();
  ResultFuture<int> getDirectorySize(String path);
  ResultFuture<Map<String, int>> getCategorizedStorageUsage();
}
