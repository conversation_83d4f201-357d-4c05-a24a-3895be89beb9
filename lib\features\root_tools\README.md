# Root Tools Feature

## نظرة عامة

ميزة أدوات الروت توفر مجموعة شاملة من الأدوات المتقدمة لإدارة النظام باستخدام صلاحيات الروت. هذه الميزة مصممة للمستخدمين المتقدمين الذين يحتاجون إلى تحكم كامل في أجهزة Android الخاصة بهم.

## الميزات الرئيسية

### 🔐 إدارة صلاحيات الروت
- فحص حالة الروت
- طلب وإلغاء صلاحيات الروت
- عرض معلومات تفصيلية عن الروت

### 💻 تنفيذ الأوامر
- تنفيذ أوامر shell مع صلاحيات الروت
- تنفيذ مجموعة من الأوامر
- تنفيذ الأوامر مع timeout
- مراقبة تنفيذ الأوامر في الوقت الفعلي

### 📊 مراقبة النظام
- مراقبة استخدام المعالج والذاكرة
- مراقبة درجة الحرارة ومستوى البطارية
- مراقبة استخدام الشبكة
- عرض إحصائيات النظام في الوقت الفعلي

### ⚙️ إدارة العمليات والخدمات
- عرض العمليات الجارية
- إنهاء العمليات
- تغيير أولوية العمليات
- إدارة خدمات النظام (تشغيل/إيقاف/إعادة تشغيل)

### 💾 عمليات نظام الملفات
- تركيب وإلغاء تركيب التقسيمات
- تغيير صلاحيات الملفات
- تغيير ملكية الملفات
- إدارة التقسيمات

### 🔧 تعديلات النظام
- تعديل خصائص النظام
- تعديل ملف build.prop
- تثبيت وإلغاء تثبيت تطبيقات النظام
- تجميد وإلغاء تجميد التطبيقات

### 💾 النسخ الاحتياطي والاستعادة
- إنشاء نسخ احتياطية للنظام
- استعادة النسخ الاحتياطية
- إدارة النسخ الاحتياطية المتاحة

### 🚀 إدارة الإقلاع
- عرض معلومات الإقلاع
- فلاش recovery و kernel
- إعادة التشغيل إلى أوضاع مختلفة

### 🛡️ الأمان والخصوصية
- حظر الإعلانات على مستوى النظام
- إدارة جدار الحماية
- إزالة تطبيقات النظام غير المرغوب فيها
- تشفير البيانات

### ⚡ تحسين الأداء
- مسح ذاكرة النظام المؤقتة
- تحسين قواعد البيانات
- إلغاء تجزئة التخزين
- تحسين الذاكرة

### 📋 السجلات والتشخيص
- عرض سجلات النظام
- عرض سجلات النواة
- عرض سجلات التطبيقات
- تشخيص النظام

### 🔧 الإصلاح والاستعادة
- إصلاح صلاحيات النظام
- إعادة بناء ذاكرة Dalvik المؤقتة
- إصلاح مشاكل الإقلاع
- إصلاح نظام الملفات

## البنية المعمارية

### Domain Layer
```
domain/
├── entities/
│   └── root_access.dart          # كيانات الروت
├── repositories/
│   └── root_tools_repository.dart # واجهة المستودع
└── usecases/
    ├── root_access_manager.dart   # إدارة صلاحيات الروت
    ├── system_manager.dart        # إدارة النظام
    └── security_manager.dart      # إدارة الأمان
```

### Data Layer
```
data/
├── models/
│   └── root_access_model.dart     # نماذج البيانات
├── datasources/
│   └── root_tools_datasource.dart # مصادر البيانات
└── repositories/
    └── root_tools_repository_impl.dart # تنفيذ المستودع
```

### Presentation Layer
```
presentation/
├── bloc/
│   ├── root_tools_bloc.dart       # BLoC الرئيسي
│   ├── root_tools_event.dart      # الأحداث
│   └── root_tools_state.dart      # الحالات
├── pages/
│   └── root_tools_page.dart       # الصفحة الرئيسية
└── widgets/
    ├── root_access_card.dart      # بطاقة حالة الروت
    ├── system_info_card.dart      # بطاقة معلومات النظام
    ├── quick_actions_grid.dart    # شبكة الإجراءات السريعة
    ├── system_monitoring_card.dart # بطاقة مراقبة النظام
    └── recent_commands_card.dart   # بطاقة الأوامر الأخيرة
```

## الاستخدام

### التهيئة الأساسية

```dart
import 'package:hd_file_explorer/features/root_tools/root_tools.dart';

// تهيئة الميزة
await RootToolsFeature.initialize();

// فحص التوفر
bool isAvailable = await RootToolsFeature.isAvailable();

// الحصول على الحالة
Map<String, dynamic> status = await RootToolsFeature.getStatus();
```

### استخدام BLoC

```dart
// فحص صلاحيات الروت
context.read<RootToolsBloc>().add(const CheckRootAccessEvent());

// طلب صلاحيات الروت
context.read<RootToolsBloc>().add(const RequestRootPermissionEvent());

// تنفيذ أمر
context.read<RootToolsBloc>().add(const ExecuteCommandEvent('ls -la'));

// بدء مراقبة النظام
context.read<RootToolsBloc>().add(const StartSystemMonitoringEvent());
```

### استخدام الويدجت

```dart
// عرض صفحة أدوات الروت
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const RootToolsPage(),
  ),
);

// استخدام بطاقة حالة الروت
RootAccessCard(
  onRequestRoot: () => _requestRoot(),
  onRevokeRoot: () => _revokeRoot(),
)
```

## متطلبات النظام

### الحد الأدنى للمتطلبات
- Android 5.0+ (API Level 21)
- 1GB RAM
- 100MB مساحة تخزين فارغة
- صلاحيات الروت

### الصلاحيات المطلوبة
```xml
<uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.WRITE_SETTINGS" />
<uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
```

## الأمان والسلامة

### تحذيرات مهمة
- ⚠️ تتطلب هذه الميزة صلاحيات الروت
- ⚠️ قد تؤثر على استقرار النظام
- ⚠️ استخدم بحذر وعلى مسؤوليتك الخاصة
- ⚠️ قم بعمل نسخة احتياطية قبل إجراء تغييرات مهمة
- ⚠️ بعض العمليات قد تكون غير قابلة للتراجع

### الأوامر الخطيرة المحظورة
- `rm -rf /`
- `dd if=/dev/zero`
- `mkfs.*`
- `fdisk`
- `parted`
- `fastboot`
- `flash_image`

### التحقق من الأوامر
```dart
bool isSafe = RootToolsUtils.isCommandSafe(command);
if (!isSafe) {
  // عرض تحذير أو منع التنفيذ
}
```

## الاختبار

### اختبار الوحدة
```bash
flutter test test/features/root_tools/
```

### اختبار التكامل
```bash
flutter test integration_test/root_tools_test.dart
```

### اختبار الأداء
```bash
flutter test test/features/root_tools/performance/
```

## المساهمة

### إرشادات المساهمة
1. اتبع معايير الكود المحددة
2. اكتب اختبارات شاملة
3. وثق التغييرات
4. اختبر على أجهزة متعددة

### بنية الكود
- استخدم Clean Architecture
- اتبع مبادئ SOLID
- استخدم Dependency Injection
- اكتب كود قابل للاختبار

## الترخيص

هذه الميزة مرخصة تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسل فريق التطوير
- راجع الوثائق

## التحديثات المستقبلية

### الإصدار 1.1.0
- [ ] دعم أجهزة ARM64
- [ ] تحسين واجهة المستخدم
- [ ] إضافة المزيد من الأدوات

### الإصدار 1.2.0
- [ ] دعم Android 14
- [ ] تحسين الأداء
- [ ] إضافة ميزات جديدة

---

**تحذير**: استخدام أدوات الروت قد يؤدي إلى إلغاء الضمان أو تلف الجهاز. استخدم على مسؤوليتك الخاصة.
