{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\hd_file_explore\\android\\app\\.cxx\\Debug\\3wm15j1y\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\hd_file_explore\\android\\app\\.cxx\\Debug\\3wm15j1y\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}