import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/file_browser_bloc.dart';
import '../bloc/file_browser_event.dart';
import '../bloc/file_browser_state.dart';
import '../widgets/file_list_view.dart';
import '../widgets/file_grid_view.dart';
import '../widgets/file_details_view.dart';
import '../widgets/file_browser_app_bar.dart';
import '../widgets/file_operation_dialog.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/injection_container.dart';

class FileBrowserPage extends StatelessWidget {
  final String? initialPath;

  const FileBrowserPage({
    super.key,
    this.initialPath,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<FileBrowserBloc>()
        ..add(LoadDirectoryEvent(initialPath ?? '/storage/emulated/0')),
      child: const FileBrowserView(),
    );
  }
}

class FileBrowserView extends StatelessWidget {
  const FileBrowserView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<FileBrowserBloc, FileBrowserState>(
        listener: (context, state) {
          if (state is FileOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.successColor,
              ),
            );
          } else if (state is FileOperationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          } else if (state is FileOperationInProgress) {
            // Show progress dialog
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => FileOperationDialog(
                operation: state.operation,
                fileName: state.fileName,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is FileBrowserLoading) {
            return const Scaffold(
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(kToolbarHeight),
                child: FileBrowserAppBar(
                  currentPath: '',
                  canNavigateBack: false,
                ),
              ),
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (state is FileBrowserError) {
            return Scaffold(
              appBar: const PreferredSize(
                preferredSize: Size.fromHeight(kToolbarHeight),
                child: FileBrowserAppBar(
                  currentPath: '',
                  canNavigateBack: false,
                ),
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: AppTheme.errorColor,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'Error',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      state.message,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    ElevatedButton(
                      onPressed: () {
                        context.read<FileBrowserBloc>().add(
                          const RefreshDirectoryEvent(),
                        );
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (state is FileBrowserLoaded) {
            return Scaffold(
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(kToolbarHeight),
                child: FileBrowserAppBar(
                  currentPath: state.currentPath,
                  canNavigateBack: state.canNavigateBack,
                  showHiddenFiles: state.showHiddenFiles,
                  sortOrder: state.sortOrder,
                  viewMode: state.viewMode,
                ),
              ),
              body: RefreshIndicator(
                onRefresh: () async {
                  context.read<FileBrowserBloc>().add(
                    const RefreshDirectoryEvent(),
                  );
                },
                child: _buildFileView(context, state),
              ),
              floatingActionButton: FloatingActionButton(
                onPressed: () => _showCreateDialog(context, state.currentPath),
                child: const Icon(Icons.add),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildFileView(BuildContext context, FileBrowserLoaded state) {
    final files = state.filteredFiles;

    if (files.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: AppConstants.defaultPadding),
            Text(
              'This folder is empty',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    switch (state.viewMode) {
      case ViewMode.list:
        return FileListView(files: files);
      case ViewMode.grid:
        return FileGridView(files: files);
      case ViewMode.details:
        return FileDetailsView(files: files);
    }
  }

  void _showCreateDialog(BuildContext context, String currentPath) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Create New'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.folder),
              title: const Text('Folder'),
              onTap: () {
                Navigator.of(dialogContext).pop();
                _showCreateFolderDialog(context, currentPath);
              },
            ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file),
              title: const Text('File'),
              onTap: () {
                Navigator.of(dialogContext).pop();
                _showCreateFileDialog(context, currentPath);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateFolderDialog(BuildContext context, String currentPath) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Create Folder'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Folder name',
            hintText: 'Enter folder name',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                context.read<FileBrowserBloc>().add(
                  CreateDirectoryEvent(currentPath, controller.text.trim()),
                );
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showCreateFileDialog(BuildContext context, String currentPath) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Create File'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'File name',
            hintText: 'Enter file name',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                // Create file logic would go here
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}
