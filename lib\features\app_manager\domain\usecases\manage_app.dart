import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/app_info.dart';
import '../repositories/app_manager_repository.dart';

@lazySingleton
class ManageApp {
  final AppManagerRepository repository;

  const ManageApp(this.repository);

  ResultVoid uninstall(String packageName) async {
    return await repository.uninstallApp(packageName);
  }

  ResultVoid disable(String packageName) async {
    return await repository.disableApp(packageName);
  }

  ResultVoid enable(String packageName) async {
    return await repository.enableApp(packageName);
  }

  ResultVoid clearData(String packageName) async {
    return await repository.clearAppData(packageName);
  }

  ResultVoid clearCache(String packageName) async {
    return await repository.clearAppCache(packageName);
  }

  ResultVoid forceStop(String packageName) async {
    return await repository.forceStopApp(packageName);
  }

  ResultVoid launch(String packageName) async {
    return await repository.launchApp(packageName);
  }
}
