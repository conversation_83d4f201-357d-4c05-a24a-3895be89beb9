import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/app_manager_bloc.dart';
import '../bloc/app_manager_event.dart';
import '../bloc/app_manager_state.dart';
import '../../domain/entities/app_info.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class AppListWidget extends StatelessWidget {
  const AppListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppManagerBloc, AppManagerState>(
      builder: (context, state) {
        if (state is AppManagerLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                <PERSON><PERSON><PERSON><PERSON>(height: AppConstants.defaultPadding),
                Text('Loading apps...'),
              ],
            ),
          );
        }
        
        if (state is AppsLoaded) {
          return _buildAppsList(context, state);
        }
        
        if (state is AppManagerError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Error Loading Apps',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () {
                    context.read<AppManagerBloc>().add(const RefreshAppsEvent());
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        
        return const EmptyAppsState();
      },
    );
  }

  Widget _buildAppsList(BuildContext context, AppsLoaded state) {
    if (state.filteredApps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Apps Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              state.searchQuery.isNotEmpty
                  ? 'No apps match your search criteria'
                  : 'No apps found in this category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (state.searchQuery.isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              ElevatedButton(
                onPressed: () {
                  context.read<AppManagerBloc>().add(const SearchAppsEvent(''));
                },
                child: const Text('Clear Search'),
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      children: [
        // Header with selection controls
        if (state.selectedApps.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${state.selectedApps.length} apps selected',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    context.read<AppManagerBloc>().add(const SelectAllAppsEvent(false));
                  },
                  child: const Text('Clear Selection'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showBatchActionsDialog(context, state.selectedApps),
                  icon: const Icon(Icons.more_horiz),
                  label: const Text('Actions'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        
        // Apps list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: state.filteredApps.length,
            itemBuilder: (context, index) {
              final app = state.filteredApps[index];
              final isSelected = state.selectedApps.contains(app.packageName);
              
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: AppItemCard(
                  app: app,
                  isSelected: isSelected,
                  onSelectionChanged: (selected) {
                    context.read<AppManagerBloc>().add(
                      SelectAppEvent(
                        packageName: app.packageName,
                        isSelected: selected,
                      ),
                    );
                  },
                  onAppAction: (action) => _handleAppAction(context, app, action),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showBatchActionsDialog(BuildContext context, Set<String> selectedApps) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Batch Actions (${selectedApps.length} apps)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Uninstall'),
              onTap: () {
                Navigator.of(context).pop();
                _confirmBatchAction(
                  context,
                  AppBatchOperationType.uninstall,
                  selectedApps.toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Disable'),
              onTap: () {
                Navigator.of(context).pop();
                _confirmBatchAction(
                  context,
                  AppBatchOperationType.disable,
                  selectedApps.toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('Enable'),
              onTap: () {
                Navigator.of(context).pop();
                _confirmBatchAction(
                  context,
                  AppBatchOperationType.enable,
                  selectedApps.toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear Data'),
              onTap: () {
                Navigator.of(context).pop();
                _confirmBatchAction(
                  context,
                  AppBatchOperationType.clearData,
                  selectedApps.toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.cleaning_services),
              title: const Text('Clear Cache'),
              onTap: () {
                Navigator.of(context).pop();
                _confirmBatchAction(
                  context,
                  AppBatchOperationType.clearCache,
                  selectedApps.toList(),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _confirmBatchAction(
    BuildContext context,
    AppBatchOperationType type,
    List<String> packageNames,
  ) {
    final actionName = _getBatchActionName(type);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm $actionName'),
        content: Text(
          'Are you sure you want to $actionName ${packageNames.length} apps?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AppManagerBloc>().add(
                StartBatchOperationEvent(
                  type: type,
                  packageNames: packageNames,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: Text(actionName),
          ),
        ],
      ),
    );
  }

  String _getBatchActionName(AppBatchOperationType type) {
    switch (type) {
      case AppBatchOperationType.uninstall:
        return 'Uninstall';
      case AppBatchOperationType.disable:
        return 'Disable';
      case AppBatchOperationType.enable:
        return 'Enable';
      case AppBatchOperationType.clearData:
        return 'Clear Data';
      case AppBatchOperationType.clearCache:
        return 'Clear Cache';
      case AppBatchOperationType.backup:
        return 'Backup';
    }
  }

  void _handleAppAction(BuildContext context, AppInfo app, AppAction action) {
    switch (action) {
      case AppAction.launch:
        context.read<AppManagerBloc>().add(LaunchAppEvent(app.packageName));
        break;
      case AppAction.uninstall:
        _confirmSingleAction(context, app, 'Uninstall', () {
          context.read<AppManagerBloc>().add(UninstallAppEvent(app.packageName));
        });
        break;
      case AppAction.disable:
        context.read<AppManagerBloc>().add(DisableAppEvent(app.packageName));
        break;
      case AppAction.enable:
        context.read<AppManagerBloc>().add(EnableAppEvent(app.packageName));
        break;
      case AppAction.clearData:
        _confirmSingleAction(context, app, 'Clear Data', () {
          context.read<AppManagerBloc>().add(ClearAppDataEvent(app.packageName));
        });
        break;
      case AppAction.clearCache:
        context.read<AppManagerBloc>().add(ClearAppCacheEvent(app.packageName));
        break;
      case AppAction.forceStop:
        context.read<AppManagerBloc>().add(ForceStopAppEvent(app.packageName));
        break;
      case AppAction.details:
        _showAppDetails(context, app);
        break;
    }
  }

  void _confirmSingleAction(
    BuildContext context,
    AppInfo app,
    String actionName,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm $actionName'),
        content: Text(
          'Are you sure you want to $actionName "${app.appName}"?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: Text(actionName),
          ),
        ],
      ),
    );
  }

  void _showAppDetails(BuildContext context, AppInfo app) {
    showDialog(
      context: context,
      builder: (context) => AppDetailsDialog(app: app),
    );
  }
}

class AppItemCard extends StatelessWidget {
  final AppInfo app;
  final bool isSelected;
  final Function(bool) onSelectionChanged;
  final Function(AppAction) onAppAction;

  const AppItemCard({
    super.key,
    required this.app,
    required this.isSelected,
    required this.onSelectionChanged,
    required this.onAppAction,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : null,
      child: InkWell(
        onTap: () => onSelectionChanged(!isSelected),
        onLongPress: () => onAppAction(AppAction.details),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              // Selection checkbox
              Checkbox(
                value: isSelected,
                onChanged: (value) => onSelectionChanged(value ?? false),
              ),
              
              const SizedBox(width: AppConstants.smallPadding),
              
              // App icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getAppTypeColor(app.type).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Icon(
                  _getAppTypeIcon(app.type),
                  color: _getAppTypeColor(app.type),
                  size: 24,
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // App info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      app.appName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      app.packageName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(app.status),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            _getStatusText(app.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          FileUtils.formatFileSize(app.totalSize),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        if (!app.isEnabled)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppTheme.warningColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Disabled',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Action button
              PopupMenuButton<AppAction>(
                onSelected: onAppAction,
                itemBuilder: (context) => [
                  if (app.isEnabled)
                    const PopupMenuItem(
                      value: AppAction.launch,
                      child: ListTile(
                        leading: Icon(Icons.launch),
                        title: Text('Launch'),
                      ),
                    ),
                  if (app.canUninstall)
                    const PopupMenuItem(
                      value: AppAction.uninstall,
                      child: ListTile(
                        leading: Icon(Icons.delete),
                        title: Text('Uninstall'),
                      ),
                    ),
                  if (app.isEnabled && app.canDisable)
                    const PopupMenuItem(
                      value: AppAction.disable,
                      child: ListTile(
                        leading: Icon(Icons.block),
                        title: Text('Disable'),
                      ),
                    ),
                  if (!app.isEnabled)
                    const PopupMenuItem(
                      value: AppAction.enable,
                      child: ListTile(
                        leading: Icon(Icons.check_circle),
                        title: Text('Enable'),
                      ),
                    ),
                  if (app.canClearData)
                    const PopupMenuItem(
                      value: AppAction.clearData,
                      child: ListTile(
                        leading: Icon(Icons.clear_all),
                        title: Text('Clear Data'),
                      ),
                    ),
                  if (app.canClearCache)
                    const PopupMenuItem(
                      value: AppAction.clearCache,
                      child: ListTile(
                        leading: Icon(Icons.cleaning_services),
                        title: Text('Clear Cache'),
                      ),
                    ),
                  if (app.status == AppStatus.running)
                    const PopupMenuItem(
                      value: AppAction.forceStop,
                      child: ListTile(
                        leading: Icon(Icons.stop),
                        title: Text('Force Stop'),
                      ),
                    ),
                  const PopupMenuItem(
                    value: AppAction.details,
                    child: ListTile(
                      leading: Icon(Icons.info),
                      title: Text('Details'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getAppTypeIcon(AppType type) {
    switch (type) {
      case AppType.user:
        return Icons.person;
      case AppType.system:
        return Icons.settings;
      case AppType.updated:
        return Icons.update;
      case AppType.disabled:
        return Icons.block;
    }
  }

  Color _getAppTypeColor(AppType type) {
    switch (type) {
      case AppType.user:
        return AppTheme.primaryColor;
      case AppType.system:
        return AppTheme.warningColor;
      case AppType.updated:
        return AppTheme.successColor;
      case AppType.disabled:
        return AppTheme.errorColor;
    }
  }

  Color _getStatusColor(AppStatus status) {
    switch (status) {
      case AppStatus.running:
        return AppTheme.successColor;
      case AppStatus.stopped:
        return AppTheme.errorColor;
      case AppStatus.cached:
        return AppTheme.warningColor;
      case AppStatus.background:
        return AppTheme.primaryColor;
    }
  }

  String _getStatusText(AppStatus status) {
    switch (status) {
      case AppStatus.running:
        return 'Running';
      case AppStatus.stopped:
        return 'Stopped';
      case AppStatus.cached:
        return 'Cached';
      case AppStatus.background:
        return 'Background';
    }
  }
}

class AppDetailsDialog extends StatelessWidget {
  final AppInfo app;

  const AppDetailsDialog({super.key, required this.app});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(app.appName),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('Package Name', app.packageName),
            _buildDetailRow('Version', '${app.version} (${app.versionCode})'),
            _buildDetailRow('Type', app.appType),
            _buildDetailRow('Status', _getStatusText(app.status)),
            _buildDetailRow('Total Size', FileUtils.formatFileSize(app.totalSize)),
            _buildDetailRow('App Size', FileUtils.formatFileSize(app.installedSize)),
            _buildDetailRow('Data Size', FileUtils.formatFileSize(app.dataSize)),
            _buildDetailRow('Cache Size', FileUtils.formatFileSize(app.cacheSize)),
            _buildDetailRow('Install Date', _formatDate(app.installDate)),
            _buildDetailRow('Last Update', _formatDate(app.lastUpdateDate)),
            _buildDetailRow('Last Used', _formatDate(app.lastUsedDate)),
            _buildDetailRow('Target SDK', app.targetSdkVersion.toString()),
            _buildDetailRow('Min SDK', app.minSdkVersion.toString()),
            _buildDetailRow('Enabled', app.isEnabled ? 'Yes' : 'No'),
            _buildDetailRow('Can Uninstall', app.canUninstall ? 'Yes' : 'No'),
            _buildDetailRow('Permissions', '${app.permissions.length} permissions'),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusText(AppStatus status) {
    switch (status) {
      case AppStatus.running:
        return 'Running';
      case AppStatus.stopped:
        return 'Stopped';
      case AppStatus.cached:
        return 'Cached';
      case AppStatus.background:
        return 'Background';
    }
  }
}

class EmptyAppsState extends StatelessWidget {
  const EmptyAppsState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.apps,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Apps Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start by loading apps from the menu',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

enum AppAction {
  launch,
  uninstall,
  disable,
  enable,
  clearData,
  clearCache,
  forceStop,
  details,
}
