name: hd_file_explore
description: "Professional File Manager - HD File Explorer with advanced features like root access, network support, and app management."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  flutter_bloc: ^8.1.6
  provider: ^6.1.2
  equatable: ^2.0.5

  # Storage
  shared_preferences: ^2.2.3

  # File & Storage Management
  path_provider: ^2.1.4
  file_picker: ^8.1.2
  permission_handler: ^11.3.1
  path: ^1.9.0
  mime: ^1.0.6

  # Archive & Compression
  archive: ^3.6.1

  # File Operations
  open_file: ^3.5.3
  share_plus: ^10.0.2

  # Security
  flutter_secure_storage: ^9.2.2
  crypto: ^3.0.5
  local_auth: ^2.3.0

  # Network & FTP
  ftpconnect: ^2.0.7
  network_info_plus: ^6.0.0
  lan_scanner: ^1.0.2
  http: ^1.2.2

  # Device & App Management
  installed_apps: ^1.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # System & Root Access
  process_run: ^1.2.0

  # Charts & Analytics
  fl_chart: ^0.69.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  rxdart: ^0.28.0
  dartz: ^0.10.1

  # Dependency Injection
  get_it: ^8.0.0
  injectable: ^2.4.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Quality
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  injectable_generator: ^2.6.2

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/translations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Fonts - Using system fonts for now
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Cairo-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Cairo-Light.ttf
  #         weight: 300
  #   - family: Tajawal
  #     fonts:
  #       - asset: assets/fonts/Tajawal-Regular.ttf
  #       - asset: assets/fonts/Tajawal-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Tajawal-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Tajawal-Light.ttf
  #         weight: 300
