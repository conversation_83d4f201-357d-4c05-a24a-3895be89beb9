class ServerException implements Exception {
  final String message;
  final int? code;
  
  const ServerException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'ServerException: $message${code != null ? ' (Code: $code)' : ''}';
}

class CacheException implements Exception {
  final String message;
  final int? code;
  
  const CacheException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'CacheException: $message${code != null ? ' (Code: $code)' : ''}';
}

class NetworkException implements Exception {
  final String message;
  final int? code;
  
  const NetworkException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'NetworkException: $message${code != null ? ' (Code: $code)' : ''}';
}

class FileSystemException implements Exception {
  final String message;
  final int? code;
  
  const FileSystemException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'FileSystemException: $message${code != null ? ' (Code: $code)' : ''}';
}

class PermissionException implements Exception {
  final String message;
  final int? code;
  
  const PermissionException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'PermissionException: $message${code != null ? ' (Code: $code)' : ''}';
}

class AuthenticationException implements Exception {
  final String message;
  final int? code;
  
  const AuthenticationException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'AuthenticationException: $message${code != null ? ' (Code: $code)' : ''}';
}

class RootAccessException implements Exception {
  final String message;
  final int? code;
  
  const RootAccessException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'RootAccessException: $message${code != null ? ' (Code: $code)' : ''}';
}

class FtpException implements Exception {
  final String message;
  final int? code;
  
  const FtpException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'FtpException: $message${code != null ? ' (Code: $code)' : ''}';
}

class CompressionException implements Exception {
  final String message;
  final int? code;
  
  const CompressionException({
    required this.message,
    this.code,
  });
  
  @override
  String toString() => 'CompressionException: $message${code != null ? ' (Code: $code)' : ''}';
}
