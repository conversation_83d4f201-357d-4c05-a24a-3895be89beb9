# App Manager Feature

A comprehensive application management system for Android devices that provides advanced app control, analysis, and batch operations.

## Features

### Core Functionality
- **App Discovery**: List all installed apps (user and system)
- **App Information**: Detailed app metadata including size, permissions, and usage stats
- **App Operations**: Install, uninstall, enable, disable, clear data/cache, force stop
- **Batch Operations**: Perform operations on multiple apps simultaneously
- **App Analysis**: Storage usage analysis, largest apps, unused apps detection
- **Search & Filter**: Advanced filtering by type, status, and custom criteria
- **Sorting**: Multiple sorting options (name, size, install date, last used)

### Advanced Features
- **Usage Statistics**: Track app usage patterns and screen time
- **Backup & Restore**: Create and restore app backups
- **Permission Management**: View and manage app permissions
- **Installation Progress**: Real-time APK installation tracking
- **App Updates**: Detect and manage app updates
- **Default Apps**: Manage system default applications

## Architecture

This feature follows Clean Architecture principles with clear separation of concerns:

```
lib/features/app_manager/
├── domain/                 # Business logic layer
│   ├── entities/          # Core business objects
│   ├── repositories/      # Abstract repository interfaces
│   └── usecases/         # Business use cases
├── data/                  # Data access layer
│   ├── datasources/      # Data source implementations
│   ├── models/           # Data models
│   └── repositories/     # Repository implementations
├── presentation/          # UI layer
│   ├── bloc/             # State management
│   ├── pages/            # Screen widgets
│   └── widgets/          # Reusable UI components
└── app_manager_injection.dart  # Dependency injection setup
```

## Key Components

### Domain Layer

#### Entities
- `AppInfo`: Core app information entity
- `AppUsageStats`: App usage statistics
- `AppBatchOperation`: Batch operation tracking

#### Use Cases
- `GetAllApps`: Retrieve all installed applications
- `ManageApp`: Individual app operations
- `BatchOperations`: Bulk app operations

### Data Layer

#### Data Sources
- `AppManagerDataSource`: Abstract data source interface
- `AppManagerDataSourceImpl`: Mock implementation with realistic data

#### Repositories
- `AppManagerRepositoryImpl`: Repository implementation with error handling

### Presentation Layer

#### BLoC (State Management)
- `AppManagerBloc`: Main business logic controller
- `AppManagerEvent`: User actions and system events
- `AppManagerState`: UI state representations

#### Pages
- `AppManagerPage`: Main app manager interface with tabs

#### Widgets
- `AppListWidget`: Displays app list with selection and actions
- `AppFiltersWidget`: Advanced filtering interface
- `AppAnalysisWidget`: Storage and usage analysis charts
- `BatchOperationsWidget`: Batch operation management

## Usage

### Basic Setup

1. Add the dependency injection setup to your main injection container:

```dart
import 'package:your_app/features/app_manager/app_manager_injection.dart';

void setupDependencies() {
  initAppManagerInjection();
}
```

2. Navigate to the App Manager page:

```dart
import 'package:your_app/features/app_manager/app_manager.dart';

Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const AppManagerPage()),
);
```

### Advanced Usage

#### Custom App Operations

```dart
// Get the bloc instance
final appManagerBloc = sl<AppManagerBloc>();

// Perform app operations
appManagerBloc.add(UninstallAppEvent('com.example.app'));
appManagerBloc.add(ClearAppDataEvent('com.example.app'));

// Batch operations
appManagerBloc.add(StartBatchOperationEvent(
  type: AppBatchOperationType.uninstall,
  packageNames: ['com.app1', 'com.app2'],
));
```

#### Listen to State Changes

```dart
BlocListener<AppManagerBloc, AppManagerState>(
  listener: (context, state) {
    if (state is AppOperationSuccess) {
      // Handle successful operation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(state.message)),
      );
    } else if (state is AppOperationError) {
      // Handle operation error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
        ),
      );
    }
  },
  child: YourWidget(),
)
```

## State Management

The feature uses BLoC pattern for state management with the following key states:

- `AppManagerInitial`: Initial state
- `AppManagerLoading`: Loading operations
- `AppsLoaded`: Apps successfully loaded with filtering/sorting
- `AppManagerError`: Error states
- `AppOperationLoading/Success/Error`: Individual app operations
- `BatchOperationStarted/Progress/Completed`: Batch operations
- `AppAnalysisLoaded`: Analysis data loaded

## Error Handling

The feature implements comprehensive error handling:

- Network/API errors are caught and converted to user-friendly messages
- Invalid operations are prevented at the UI level
- Batch operations continue even if individual operations fail
- All errors are logged and reported to the user appropriately

## Testing

The architecture supports easy testing with:

- Mock data sources for unit testing
- BLoC testing utilities for state management testing
- Widget testing for UI components
- Integration testing for complete user flows

## Dependencies

Required packages:
- `flutter_bloc`: State management
- `injectable`: Dependency injection
- `get_it`: Service locator
- `dartz`: Functional programming utilities
- `equatable`: Value equality
- `uuid`: Unique identifier generation
- `fl_chart`: Charts and analytics visualization

## Performance Considerations

- Lazy loading of app lists for better performance
- Efficient filtering and sorting algorithms
- Memory-conscious image loading for app icons
- Background processing for batch operations
- Proper stream management to prevent memory leaks

## Security

- Permission checks before performing sensitive operations
- Validation of app package names and paths
- Secure handling of app data and backups
- Protection against malicious APK installations

## Future Enhancements

- Real Android system integration
- Cloud backup synchronization
- Advanced analytics and reporting
- App recommendation system
- Automated cleanup suggestions
- Integration with package managers
- Multi-device app management
