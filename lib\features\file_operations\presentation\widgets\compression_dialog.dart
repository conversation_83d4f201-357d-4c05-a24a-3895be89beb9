import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';

import '../bloc/file_operations_bloc.dart';
import '../bloc/file_operations_event.dart';
import '../../domain/entities/file_operation.dart';
import '../../../../core/constants/app_constants.dart';

class CompressionDialog extends StatefulWidget {
  const CompressionDialog({super.key});

  @override
  State<CompressionDialog> createState() => _CompressionDialogState();
}

class _CompressionDialogState extends State<CompressionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _outputNameController = TextEditingController();
  final _passwordController = TextEditingController();
  
  List<String> _selectedFiles = [];
  String _outputDirectory = '';
  CompressionFormat _format = CompressionFormat.zip;
  CompressionLevel _level = CompressionLevel.normal;
  bool _includeSubfolders = true;
  bool _usePassword = false;

  @override
  void dispose() {
    _outputNameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Compress Files'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // File Selection
                Text(
                  'Files to Compress',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_selectedFiles.isEmpty)
                        const Text(
                          'No files selected',
                          style: TextStyle(color: Colors.grey),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: _selectedFiles.map((file) => Text(
                            file,
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis,
                          )).toList(),
                        ),
                      
                      const SizedBox(height: AppConstants.smallPadding),
                      
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: _selectFiles,
                            icon: const Icon(Icons.add),
                            label: const Text('Add Files'),
                          ),
                          const SizedBox(width: AppConstants.smallPadding),
                          OutlinedButton.icon(
                            onPressed: _selectFolder,
                            icon: const Icon(Icons.folder),
                            label: const Text('Add Folder'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Output Settings
                Text(
                  'Output Settings',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                
                TextFormField(
                  controller: _outputNameController,
                  decoration: const InputDecoration(
                    labelText: 'Archive Name',
                    hintText: 'Enter archive name',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter archive name';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Format Selection
                Text(
                  'Compression Format',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                
                Wrap(
                  spacing: AppConstants.smallPadding,
                  children: CompressionFormat.values.map((format) {
                    return ChoiceChip(
                      label: Text(_getFormatName(format)),
                      selected: _format == format,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _format = format;
                          });
                        }
                      },
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Compression Level
                Text(
                  'Compression Level',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                
                DropdownButtonFormField<CompressionLevel>(
                  value: _level,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: CompressionLevel.values.map((level) {
                    return DropdownMenuItem(
                      value: level,
                      child: Text(_getLevelName(level)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _level = value;
                      });
                    }
                  },
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Options
                CheckboxListTile(
                  title: const Text('Include Subfolders'),
                  value: _includeSubfolders,
                  onChanged: (value) {
                    setState(() {
                      _includeSubfolders = value ?? true;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
                
                CheckboxListTile(
                  title: const Text('Password Protection'),
                  value: _usePassword,
                  onChanged: (value) {
                    setState(() {
                      _usePassword = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (_usePassword) ...[
                  const SizedBox(height: AppConstants.smallPadding),
                  TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: 'Password',
                      hintText: 'Enter password',
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (_usePassword && (value == null || value.trim().isEmpty)) {
                        return 'Please enter password';
                      }
                      return null;
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _compress,
          child: const Text('Compress'),
        ),
      ],
    );
  }

  Future<void> _selectFiles() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      setState(() {
        _selectedFiles.addAll(result.paths.where((path) => path != null).cast<String>());
      });
    }
  }

  Future<void> _selectFolder() async {
    final result = await FilePicker.platform.getDirectoryPath();

    if (result != null) {
      setState(() {
        _selectedFiles.add(result);
      });
    }
  }

  void _compress() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select files to compress'),
        ),
      );
      return;
    }

    final options = CompressionOptions(
      format: _format,
      level: _level,
      password: _usePassword ? _passwordController.text.trim() : null,
      includeSubfolders: _includeSubfolders,
    );

    // For demo purposes, use Downloads folder as destination
    final destinationPath = '/storage/emulated/0/Download/${_outputNameController.text.trim()}.${_getFormatExtension(_format)}';

    context.read<FileOperationsBloc>().add(
      StartCompressionEvent(
        sourcePaths: _selectedFiles,
        destinationPath: destinationPath,
        options: options,
      ),
    );

    Navigator.of(context).pop();
  }

  String _getFormatName(CompressionFormat format) {
    switch (format) {
      case CompressionFormat.zip:
        return 'ZIP';
      case CompressionFormat.tar:
        return 'TAR';
      case CompressionFormat.gzip:
        return 'GZIP';
      case CompressionFormat.sevenZip:
        return '7Z';
    }
  }

  String _getFormatExtension(CompressionFormat format) {
    switch (format) {
      case CompressionFormat.zip:
        return 'zip';
      case CompressionFormat.tar:
        return 'tar';
      case CompressionFormat.gzip:
        return 'gz';
      case CompressionFormat.sevenZip:
        return '7z';
    }
  }

  String _getLevelName(CompressionLevel level) {
    switch (level) {
      case CompressionLevel.store:
        return 'Store (No Compression)';
      case CompressionLevel.fastest:
        return 'Fastest';
      case CompressionLevel.fast:
        return 'Fast';
      case CompressionLevel.normal:
        return 'Normal';
      case CompressionLevel.maximum:
        return 'Maximum';
      case CompressionLevel.ultra:
        return 'Ultra';
    }
  }
}
