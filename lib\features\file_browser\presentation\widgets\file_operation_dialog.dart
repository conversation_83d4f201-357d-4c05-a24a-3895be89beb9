import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

class FileOperationDialog extends StatelessWidget {
  final String operation;
  final String fileName;

  const FileOperationDialog({
    super.key,
    required this.operation,
    required this.fileName,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            '$operation...',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            fileName,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
