import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/search_result.dart';
import '../entities/search_filter.dart';
import '../repositories/search_repository.dart';

@lazySingleton
class SearchFiles {
  final SearchRepository repository;

  const SearchFiles(this.repository);

  ResultFuture<SearchResult> call(SearchFilter filter) async {
    return await repository.searchFiles(filter);
  }

  Stream<SearchResult> stream(SearchFilter filter) {
    return repository.searchFilesStream(filter);
  }
}
