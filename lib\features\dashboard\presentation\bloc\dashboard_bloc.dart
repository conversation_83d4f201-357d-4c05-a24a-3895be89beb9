import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../domain/usecases/get_storage_info.dart';
import 'dashboard_event.dart';
import 'dashboard_state.dart';

@injectable
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetStorageInfo _getStorageInfo;

  DashboardBloc(this._getStorageInfo) : super(const DashboardInitial()) {
    on<LoadDashboardEvent>(_onLoadDashboard);
    on<RefreshStorageInfoEvent>(_onRefreshStorageInfo);
    on<LoadQuickAccessEvent>(_onLoadQuickAccess);
    on<LoadRecentFilesEvent>(_onLoadRecentFiles);
    on<NavigateToQuickAccessEvent>(_onNavigateToQuickAccess);
  }

  Future<void> _onLoadDashboard(
    LoadDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      // Load storage info
      final storageResult = await _getStorageInfo();
      
      await storageResult.fold(
        (failure) async => emit(DashboardError(failure.message)),
        (storageInfo) async {
          // Mock data for other components
          const quickAccess = QuickAccessData(
            imageCount: 1250,
            videoCount: 45,
            audioCount: 320,
            documentCount: 89,
            downloadCount: 156,
            appCount: 127,
          );

          const categorizedUsage = <String, int>{
            'Images': 2147483648, // 2GB
            'Videos': 5368709120, // 5GB
            'Audio': 1073741824, // 1GB
            'Documents': 536870912, // 512MB
            'Apps': 8589934592, // 8GB
            'Other': 3221225472, // 3GB
          };

          emit(DashboardLoaded(
            storageInfo: storageInfo,
            recentFiles: const [], // Will be loaded separately
            categorizedUsage: categorizedUsage,
            quickAccess: quickAccess,
          ));
        },
      );
    } catch (e) {
      emit(DashboardError('Failed to load dashboard: $e'));
    }
  }

  Future<void> _onRefreshStorageInfo(
    RefreshStorageInfoEvent event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      final result = await _getStorageInfo();
      
      result.fold(
        (failure) => emit(DashboardError(failure.message)),
        (storageInfo) => emit(currentState.copyWith(storageInfo: storageInfo)),
      );
    }
  }

  Future<void> _onLoadQuickAccess(
    LoadQuickAccessEvent event,
    Emitter<DashboardState> emit,
  ) async {
    // This would typically scan the file system for different file types
    // For now, we'll use mock data
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      // Mock updated counts
      const updatedQuickAccess = QuickAccessData(
        imageCount: 1275,
        videoCount: 47,
        audioCount: 325,
        documentCount: 92,
        downloadCount: 160,
        appCount: 130,
      );

      emit(currentState.copyWith(quickAccess: updatedQuickAccess));
    }
  }

  Future<void> _onLoadRecentFiles(
    LoadRecentFilesEvent event,
    Emitter<DashboardState> emit,
  ) async {
    // This would load recently accessed files
    // For now, we'll use empty list
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(recentFiles: const []));
    }
  }

  void _onNavigateToQuickAccess(
    NavigateToQuickAccessEvent event,
    Emitter<DashboardState> emit,
  ) {
    // This would trigger navigation to the specific file type view
    // The actual navigation would be handled by the UI layer
    // For now, we'll just emit the current state
    if (state is DashboardLoaded) {
      // Navigation logic would be handled by the UI
      // This event is mainly for tracking user interactions
    }
  }
}
