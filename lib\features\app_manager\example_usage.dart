// Example usage of App Manager feature
// This file demonstrates how to integrate and use the App Manager feature

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'app_manager.dart';
import '../../core/di/injection_container.dart';

/// Example 1: Basic App Manager Page Integration
class ExampleAppManagerIntegration extends StatelessWidget {
  const ExampleAppManagerIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Manager Example'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AppManagerPage(),
              ),
            );
          },
          child: const Text('Open App Manager'),
        ),
      ),
    );
  }
}

/// Example 2: Custom App Manager with BLoC Integration
class CustomAppManagerExample extends StatelessWidget {
  const CustomAppManagerExample({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppManagerBloc>()..add(const LoadAllAppsEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Custom App Manager'),
          actions: [
            BlocBuilder<AppManagerBloc, AppManagerState>(
              builder: (context, state) {
                return IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: state is AppManagerLoading
                      ? null
                      : () {
                          context.read<AppManagerBloc>().add(const RefreshAppsEvent());
                        },
                );
              },
            ),
          ],
        ),
        body: BlocConsumer<AppManagerBloc, AppManagerState>(
          listener: (context, state) {
            if (state is AppOperationSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
            } else if (state is AppOperationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is AppManagerLoading) {
              return const Center(child: CircularProgressIndicator());
            }
            
            if (state is AppsLoaded) {
              return Column(
                children: [
                  // Search bar
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      decoration: const InputDecoration(
                        hintText: 'Search apps...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (query) {
                        context.read<AppManagerBloc>().add(SearchAppsEvent(query));
                      },
                    ),
                  ),
                  
                  // Apps list
                  Expanded(
                    child: ListView.builder(
                      itemCount: state.filteredApps.length,
                      itemBuilder: (context, index) {
                        final app = state.filteredApps[index];
                        return ListTile(
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(Icons.apps, color: Colors.blue),
                          ),
                          title: Text(app.appName),
                          subtitle: Text(app.packageName),
                          trailing: PopupMenuButton<String>(
                            onSelected: (action) {
                              _handleAppAction(context, app, action);
                            },
                            itemBuilder: (context) => [
                              if (app.isEnabled)
                                const PopupMenuItem(
                                  value: 'launch',
                                  child: Text('Launch'),
                                ),
                              if (app.canUninstall)
                                const PopupMenuItem(
                                  value: 'uninstall',
                                  child: Text('Uninstall'),
                                ),
                              if (app.canDisable)
                                PopupMenuItem(
                                  value: app.isEnabled ? 'disable' : 'enable',
                                  child: Text(app.isEnabled ? 'Disable' : 'Enable'),
                                ),
                              if (app.canClearCache)
                                const PopupMenuItem(
                                  value: 'clear_cache',
                                  child: Text('Clear Cache'),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
            }
            
            if (state is AppManagerError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(state.message),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        context.read<AppManagerBloc>().add(const LoadAllAppsEvent());
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }
            
            return const Center(child: Text('No data'));
          },
        ),
      ),
    );
  }

  void _handleAppAction(BuildContext context, AppInfo app, String action) {
    final bloc = context.read<AppManagerBloc>();
    
    switch (action) {
      case 'launch':
        bloc.add(LaunchAppEvent(app.packageName));
        break;
      case 'uninstall':
        _showConfirmDialog(
          context,
          'Uninstall App',
          'Are you sure you want to uninstall ${app.appName}?',
          () => bloc.add(UninstallAppEvent(app.packageName)),
        );
        break;
      case 'disable':
        bloc.add(DisableAppEvent(app.packageName));
        break;
      case 'enable':
        bloc.add(EnableAppEvent(app.packageName));
        break;
      case 'clear_cache':
        bloc.add(ClearAppCacheEvent(app.packageName));
        break;
    }
  }

  void _showConfirmDialog(
    BuildContext context,
    String title,
    String message,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}

/// Example 3: Batch Operations Example
class BatchOperationsExample extends StatelessWidget {
  const BatchOperationsExample({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppManagerBloc>()..add(const LoadAllAppsEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Batch Operations Example'),
        ),
        body: BlocConsumer<AppManagerBloc, AppManagerState>(
          listener: (context, state) {
            if (state is BatchOperationCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Batch operation completed: ${state.operation.successfulApps} successful, ${state.operation.failedApps} failed',
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is AppsLoaded) {
              return Column(
                children: [
                  // Selection controls
                  if (state.selectedApps.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(16),
                      color: Colors.blue.withValues(alpha: 0.1),
                      child: Row(
                        children: [
                          Text('${state.selectedApps.length} apps selected'),
                          const Spacer(),
                          ElevatedButton(
                            onPressed: () {
                              _showBatchOperationsDialog(context, state.selectedApps);
                            },
                            child: const Text('Batch Actions'),
                          ),
                        ],
                      ),
                    ),
                  
                  // Apps list with selection
                  Expanded(
                    child: ListView.builder(
                      itemCount: state.filteredApps.length,
                      itemBuilder: (context, index) {
                        final app = state.filteredApps[index];
                        final isSelected = state.selectedApps.contains(app.packageName);
                        
                        return CheckboxListTile(
                          value: isSelected,
                          onChanged: (selected) {
                            context.read<AppManagerBloc>().add(
                              SelectAppEvent(
                                packageName: app.packageName,
                                isSelected: selected ?? false,
                              ),
                            );
                          },
                          secondary: const Icon(Icons.apps),
                          title: Text(app.appName),
                          subtitle: Text(app.packageName),
                        );
                      },
                    ),
                  ),
                ],
              );
            }
            
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ),
    );
  }

  void _showBatchOperationsDialog(BuildContext context, Set<String> selectedApps) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Batch Operations (${selectedApps.length} apps)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Uninstall'),
              onTap: () {
                Navigator.of(context).pop();
                context.read<AppManagerBloc>().add(
                  StartBatchOperationEvent(
                    type: AppBatchOperationType.uninstall,
                    packageNames: selectedApps.toList(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.cleaning_services),
              title: const Text('Clear Cache'),
              onTap: () {
                Navigator.of(context).pop();
                context.read<AppManagerBloc>().add(
                  StartBatchOperationEvent(
                    type: AppBatchOperationType.clearCache,
                    packageNames: selectedApps.toList(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Example 4: App Analysis Example
class AppAnalysisExample extends StatelessWidget {
  const AppAnalysisExample({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AppManagerBloc>()..add(const LoadAppAnalysisEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('App Analysis Example'),
        ),
        body: BlocBuilder<AppManagerBloc, AppManagerState>(
          builder: (context, state) {
            if (state is AppAnalysisLoaded) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Storage Analysis',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    
                    // Quick stats
                    Row(
                      children: [
                        Expanded(
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  const Icon(Icons.apps, size: 32),
                                  const SizedBox(height: 8),
                                  Text('${state.largestApps.length}'),
                                  const Text('Total Apps'),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  const Icon(Icons.schedule, size: 32),
                                  const SizedBox(height: 8),
                                  Text('${state.unusedApps.length}'),
                                  const Text('Unused Apps'),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Largest apps
                    Text(
                      'Largest Apps',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    ...state.largestApps.take(5).map((app) => ListTile(
                      leading: const Icon(Icons.apps),
                      title: Text(app.appName),
                      subtitle: Text(app.packageName),
                      trailing: Text(app.displaySize),
                    )),
                  ],
                ),
              );
            }
            
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ),
    );
  }
}
