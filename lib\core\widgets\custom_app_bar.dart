import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// شريط تطبيق مخصص
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool automaticallyImplyLeading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title:
          subtitle != null
              ? Column(
                crossAxisAlignment:
                    centerTitle
                        ? CrossAxisAlignment.center
                        : CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: foregroundColor ?? AppColors.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: (foregroundColor ?? AppColors.onPrimary)
                          .withValues(alpha: 0.8),
                    ),
                  ),
                ],
              )
              : Text(
                title,
                style: AppTextStyles.headlineSmall.copyWith(
                  color: foregroundColor ?? AppColors.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.onPrimary,
      elevation: elevation ?? 2,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق مع بحث
class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String? hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchSubmitted;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;

  const SearchAppBar({
    super.key,
    required this.title,
    this.hintText,
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.actions,
    this.automaticallyImplyLeading = true,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _SearchAppBarState extends State<SearchAppBar> {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title:
          _isSearching
              ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: widget.hintText ?? 'Search...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    color: AppColors.onPrimary.withValues(alpha: 0.7),
                  ),
                ),
                style: TextStyle(color: AppColors.onPrimary, fontSize: 16),
                onChanged: widget.onSearchChanged,
                onSubmitted: (value) {
                  widget.onSearchSubmitted?.call();
                },
              )
              : Text(
                widget.title,
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      automaticallyImplyLeading: widget.automaticallyImplyLeading,
      actions: [
        IconButton(
          icon: Icon(_isSearching ? Icons.close : Icons.search),
          onPressed: () {
            setState(() {
              _isSearching = !_isSearching;
              if (!_isSearching) {
                _searchController.clear();
                widget.onSearchChanged?.call('');
              }
            });
          },
        ),
        if (!_isSearching && widget.actions != null) ...widget.actions!,
      ],
    );
  }
}

/// شريط تطبيق مع تبويبات
class TabbedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Tab> tabs;
  final TabController? controller;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;

  const TabbedAppBar({
    super.key,
    required this.title,
    required this.tabs,
    this.controller,
    this.actions,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: AppTextStyles.headlineSmall.copyWith(
          color: AppColors.onPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
      bottom: TabBar(
        controller: controller,
        tabs: tabs,
        indicatorColor: AppColors.onPrimary,
        labelColor: AppColors.onPrimary,
        unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(kToolbarHeight + kTextTabBarHeight);
}

/// شريط تطبيق شفاف
class TransparentAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? foregroundColor;

  const TransparentAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title:
          title != null
              ? Text(
                title!,
                style: AppTextStyles.headlineSmall.copyWith(
                  color: foregroundColor ?? AppColors.onBackground,
                  fontWeight: FontWeight.bold,
                ),
              )
              : null,
      backgroundColor: Colors.transparent,
      foregroundColor: foregroundColor ?? AppColors.onBackground,
      elevation: 0,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
