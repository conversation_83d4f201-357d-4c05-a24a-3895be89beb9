import 'package:equatable/equatable.dart';

abstract class File<PERSON>rowserEvent extends Equatable {
  const FileBrowserEvent();

  @override
  List<Object?> get props => [];
}

class LoadDirectoryEvent extends FileBrowserEvent {
  final String path;

  const LoadDirectoryEvent(this.path);

  @override
  List<Object?> get props => [path];
}

class RefreshDirectoryEvent extends FileBrowserEvent {
  const RefreshDirectoryEvent();
}

class NavigateToDirectoryEvent extends FileBrowserEvent {
  final String path;

  const NavigateToDirectoryEvent(this.path);

  @override
  List<Object?> get props => [path];
}

class NavigateBackEvent extends FileBrowserEvent {
  const NavigateBackEvent();
}

class CreateDirectoryEvent extends FileBrowserEvent {
  final String path;
  final String name;

  const CreateDirectoryEvent(this.path, this.name);

  @override
  List<Object?> get props => [path, name];
}

class DeleteFileEvent extends FileBrowserEvent {
  final String path;

  const DeleteFileEvent(this.path);

  @override
  List<Object?> get props => [path];
}

class RenameFileEvent extends FileBrowserEvent {
  final String oldPath;
  final String newName;

  const RenameFileEvent(this.oldPath, this.newName);

  @override
  List<Object?> get props => [oldPath, newName];
}

class CopyFileEvent extends FileBrowserEvent {
  final String sourcePath;
  final String destinationPath;

  const CopyFileEvent(this.sourcePath, this.destinationPath);

  @override
  List<Object?> get props => [sourcePath, destinationPath];
}

class MoveFileEvent extends FileBrowserEvent {
  final String sourcePath;
  final String destinationPath;

  const MoveFileEvent(this.sourcePath, this.destinationPath);

  @override
  List<Object?> get props => [sourcePath, destinationPath];
}

class ToggleHiddenFilesEvent extends FileBrowserEvent {
  const ToggleHiddenFilesEvent();
}

class ChangeSortOrderEvent extends FileBrowserEvent {
  final SortOrder sortOrder;

  const ChangeSortOrderEvent(this.sortOrder);

  @override
  List<Object?> get props => [sortOrder];
}

class ChangeViewModeEvent extends FileBrowserEvent {
  final ViewMode viewMode;

  const ChangeViewModeEvent(this.viewMode);

  @override
  List<Object?> get props => [viewMode];
}

enum SortOrder {
  nameAsc,
  nameDesc,
  sizeAsc,
  sizeDesc,
  dateAsc,
  dateDesc,
  typeAsc,
  typeDesc,
}

enum ViewMode {
  list,
  grid,
  details,
}
