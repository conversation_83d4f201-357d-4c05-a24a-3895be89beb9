import 'package:equatable/equatable.dart';

class NetworkDevice extends Equatable {
  final String ipAddress;
  final String macAddress;
  final String hostname;
  final DeviceType deviceType;
  final bool isOnline;
  final DateTime lastSeen;
  final String? manufacturer;
  final List<int>? openPorts;
  final List<NetworkService>? services;

  const NetworkDevice({
    required this.ipAddress,
    required this.macAddress,
    required this.hostname,
    required this.deviceType,
    required this.isOnline,
    required this.lastSeen,
    this.manufacturer,
    this.openPorts,
    this.services,
  });

  String get displayName => hostname.isNotEmpty ? hostname : ipAddress;

  @override
  List<Object?> get props => [
        ipAddress,
        macAddress,
        hostname,
        deviceType,
        isOnline,
        lastSeen,
        manufacturer,
        openPorts,
        services,
      ];
}

enum DeviceType {
  computer,
  phone,
  tablet,
  router,
  printer,
  tv,
  camera,
  unknown,
}

class NetworkService extends Equatable {
  final String name;
  final int port;
  final String protocol;
  final bool isSecure;

  const NetworkService({
    required this.name,
    required this.port,
    required this.protocol,
    required this.isSecure,
  });

  factory NetworkService.fromJson(Map<String, dynamic> json) {
    return NetworkService(
      name: json['name'] as String,
      port: json['port'] as int,
      protocol: json['protocol'] as String,
      isSecure: json['isSecure'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'port': port,
      'protocol': protocol,
      'isSecure': isSecure,
    };
  }

  @override
  List<Object?> get props => [name, port, protocol, isSecure];
}
