import 'package:equatable/equatable.dart';
import '../models/language.dart';

/// أحداث إدارة اللغة
abstract class LanguageEvent extends Equatable {
  const LanguageEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل اللغة المحفوظة
class LoadSavedLanguageEvent extends LanguageEvent {
  const LoadSavedLanguageEvent();
}

/// تغيير اللغة
class ChangeLanguageEvent extends LanguageEvent {
  final Language language;

  const ChangeLanguageEvent(this.language);

  @override
  List<Object?> get props => [language];
}

/// تحميل الترجمات
class LoadTranslationsEvent extends LanguageEvent {
  final Language language;

  const LoadTranslationsEvent(this.language);

  @override
  List<Object?> get props => [language];
}

/// إعادة تحميل الترجمات
class ReloadTranslationsEvent extends LanguageEvent {
  const ReloadTranslationsEvent();
}

/// تحديث ترجمة معينة
class UpdateTranslationEvent extends LanguageEvent {
  final String key;
  final String value;

  const UpdateTranslationEvent(this.key, this.value);

  @override
  List<Object?> get props => [key, value];
}

/// مسح الترجمات
class ClearTranslationsEvent extends LanguageEvent {
  const ClearTranslationsEvent();
}

/// استيراد الترجمات
class ImportTranslationsEvent extends LanguageEvent {
  final String jsonString;

  const ImportTranslationsEvent(this.jsonString);

  @override
  List<Object?> get props => [jsonString];
}

/// دمج الترجمات
class MergeTranslationsEvent extends LanguageEvent {
  final Map<String, dynamic> translations;

  const MergeTranslationsEvent(this.translations);

  @override
  List<Object?> get props => [translations];
}

/// البحث في الترجمات
class SearchTranslationsEvent extends LanguageEvent {
  final String query;

  const SearchTranslationsEvent(this.query);

  @override
  List<Object?> get props => [query];
}

/// التحقق من الترجمات
class ValidateTranslationsEvent extends LanguageEvent {
  final Language referenceLanguage;

  const ValidateTranslationsEvent(this.referenceLanguage);

  @override
  List<Object?> get props => [referenceLanguage];
}

/// تصدير الترجمات
class ExportTranslationsEvent extends LanguageEvent {
  const ExportTranslationsEvent();
}

/// الحصول على معلومات الترجمة
class GetTranslationInfoEvent extends LanguageEvent {
  const GetTranslationInfoEvent();
}

/// تحديد اللغة تلقائياً من النظام
class DetectSystemLanguageEvent extends LanguageEvent {
  const DetectSystemLanguageEvent();
}

/// تبديل اتجاه النص
class ToggleTextDirectionEvent extends LanguageEvent {
  const ToggleTextDirectionEvent();
}

/// إعادة تعيين اللغة للافتراضية
class ResetToDefaultLanguageEvent extends LanguageEvent {
  const ResetToDefaultLanguageEvent();
}

/// حفظ تفضيلات اللغة
class SaveLanguagePreferencesEvent extends LanguageEvent {
  final Map<String, dynamic> preferences;

  const SaveLanguagePreferencesEvent(this.preferences);

  @override
  List<Object?> get props => [preferences];
}

/// تحميل تفضيلات اللغة
class LoadLanguagePreferencesEvent extends LanguageEvent {
  const LoadLanguagePreferencesEvent();
}

/// تحديث إعدادات الترجمة
class UpdateTranslationSettingsEvent extends LanguageEvent {
  final Map<String, dynamic> settings;

  const UpdateTranslationSettingsEvent(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// تفعيل/تعطيل الترجمة التلقائية
class ToggleAutoTranslationEvent extends LanguageEvent {
  final bool enabled;

  const ToggleAutoTranslationEvent(this.enabled);

  @override
  List<Object?> get props => [enabled];
}

/// تحديث ذاكرة التخزين المؤقت للترجمات
class UpdateTranslationCacheEvent extends LanguageEvent {
  const UpdateTranslationCacheEvent();
}

/// مسح ذاكرة التخزين المؤقت للترجمات
class ClearTranslationCacheEvent extends LanguageEvent {
  const ClearTranslationCacheEvent();
}

/// تحميل ترجمات إضافية
class LoadAdditionalTranslationsEvent extends LanguageEvent {
  final String module;
  final Language language;

  const LoadAdditionalTranslationsEvent(this.module, this.language);

  @override
  List<Object?> get props => [module, language];
}

/// إلغاء تحميل ترجمات إضافية
class UnloadAdditionalTranslationsEvent extends LanguageEvent {
  final String module;

  const UnloadAdditionalTranslationsEvent(this.module);

  @override
  List<Object?> get props => [module];
}

/// تحديث قاموس الترجمة
class UpdateTranslationDictionaryEvent extends LanguageEvent {
  final Map<String, String> dictionary;

  const UpdateTranslationDictionaryEvent(this.dictionary);

  @override
  List<Object?> get props => [dictionary];
}

/// إضافة ترجمة مخصصة
class AddCustomTranslationEvent extends LanguageEvent {
  final String key;
  final String value;
  final Language language;

  const AddCustomTranslationEvent(this.key, this.value, this.language);

  @override
  List<Object?> get props => [key, value, language];
}

/// إزالة ترجمة مخصصة
class RemoveCustomTranslationEvent extends LanguageEvent {
  final String key;
  final Language language;

  const RemoveCustomTranslationEvent(this.key, this.language);

  @override
  List<Object?> get props => [key, language];
}

/// تحديث إعدادات الخط
class UpdateFontSettingsEvent extends LanguageEvent {
  final String fontFamily;
  final double fontSize;
  final Language language;

  const UpdateFontSettingsEvent(this.fontFamily, this.fontSize, this.language);

  @override
  List<Object?> get props => [fontFamily, fontSize, language];
}

/// تحديث إعدادات التخطيط
class UpdateLayoutSettingsEvent extends LanguageEvent {
  final bool isRTL;
  final String textAlign;

  const UpdateLayoutSettingsEvent(this.isRTL, this.textAlign);

  @override
  List<Object?> get props => [isRTL, textAlign];
}

/// تحديث إعدادات التنسيق
class UpdateFormattingSettingsEvent extends LanguageEvent {
  final String dateFormat;
  final String timeFormat;
  final String numberFormat;

  const UpdateFormattingSettingsEvent(
    this.dateFormat,
    this.timeFormat,
    this.numberFormat,
  );

  @override
  List<Object?> get props => [dateFormat, timeFormat, numberFormat];
}

/// تحديث إعدادات الإقليم
class UpdateLocaleSettingsEvent extends LanguageEvent {
  final String currency;
  final String timezone;
  final String calendar;

  const UpdateLocaleSettingsEvent(
    this.currency,
    this.timezone,
    this.calendar,
  );

  @override
  List<Object?> get props => [currency, timezone, calendar];
}
