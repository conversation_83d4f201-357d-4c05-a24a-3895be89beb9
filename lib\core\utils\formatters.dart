import 'dart:math';

class Formatters {
  /// Format bytes to human readable format (KB, MB, GB, etc.)
  static String formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    final i = (log(bytes) / log(1024)).floor();
    
    if (i >= suffixes.length) {
      return '${(bytes / pow(1024, suffixes.length - 1)).toStringAsFixed(1)} ${suffixes.last}';
    }
    
    return '${(bytes / pow(1024, i)).toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }

  /// Format duration to human readable format
  static String formatDuration(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours.remainder(24);
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    final parts = <String>[];
    
    if (days > 0) parts.add('${days}د');
    if (hours > 0) parts.add('${hours}س');
    if (minutes > 0) parts.add('${minutes}ق');
    if (seconds > 0 || parts.isEmpty) parts.add('${seconds}ث');

    return parts.join(' ');
  }

  /// Format uptime seconds to human readable format
  static String formatUptime(int uptimeSeconds) {
    final duration = Duration(seconds: uptimeSeconds);
    return formatDuration(duration);
  }

  /// Format percentage with one decimal place
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Format number with thousand separators
  static String formatNumber(int number) {
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  /// Format file count
  static String formatFileCount(int count) {
    if (count == 0) return 'لا توجد ملفات';
    if (count == 1) return 'ملف واحد';
    if (count == 2) return 'ملفان';
    if (count <= 10) return '$count ملفات';
    return '$count ملف';
  }

  /// Format folder count
  static String formatFolderCount(int count) {
    if (count == 0) return 'لا توجد مجلدات';
    if (count == 1) return 'مجلد واحد';
    if (count == 2) return 'مجلدان';
    if (count <= 10) return '$count مجلدات';
    return '$count مجلد';
  }

  /// Format date and time
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// Format speed (bytes per second)
  static String formatSpeed(int bytesPerSecond) {
    return '${formatBytes(bytesPerSecond)}/ث';
  }

  /// Format temperature
  static String formatTemperature(double celsius) {
    return '${celsius.toStringAsFixed(1)}°م';
  }

  /// Format frequency (Hz)
  static String formatFrequency(int hertz) {
    if (hertz >= 1000000000) {
      return '${(hertz / 1000000000).toStringAsFixed(1)} جيجاهرتز';
    } else if (hertz >= 1000000) {
      return '${(hertz / 1000000).toStringAsFixed(0)} ميجاهرتز';
    } else if (hertz >= 1000) {
      return '${(hertz / 1000).toStringAsFixed(0)} كيلوهرتز';
    } else {
      return '$hertz هرتز';
    }
  }

  /// Format voltage
  static String formatVoltage(double voltage) {
    return '${voltage.toStringAsFixed(2)} فولت';
  }

  /// Format current (amperes)
  static String formatCurrent(double amperes) {
    if (amperes >= 1) {
      return '${amperes.toStringAsFixed(2)} أمبير';
    } else {
      return '${(amperes * 1000).toStringAsFixed(0)} ميلي أمبير';
    }
  }

  /// Format power (watts)
  static String formatPower(double watts) {
    if (watts >= 1000) {
      return '${(watts / 1000).toStringAsFixed(1)} كيلووات';
    } else {
      return '${watts.toStringAsFixed(1)} وات';
    }
  }
}
