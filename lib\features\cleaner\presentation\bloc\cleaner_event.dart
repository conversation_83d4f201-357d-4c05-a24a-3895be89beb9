import 'package:equatable/equatable.dart';
import '../../domain/entities/cleanup_item.dart';

abstract class CleanerEvent extends Equatable {
  const CleanerEvent();

  @override
  List<Object?> get props => [];
}

class StartSystemAnalysisEvent extends CleanerEvent {
  const StartSystemAnalysisEvent();
}

class CancelSystemAnalysisEvent extends CleanerEvent {
  const CancelSystemAnalysisEvent();
}

class StartCleanupEvent extends CleanerEvent {
  final List<CleanupItem> items;

  const StartCleanupEvent(this.items);

  @override
  List<Object?> get props => [items];
}

class CancelCleanupEvent extends CleanerEvent {
  const CancelCleanupEvent();
}

class ToggleCleanupItemEvent extends CleanerEvent {
  final String itemId;
  final bool isSelected;

  const ToggleCleanupItemEvent({
    required this.itemId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [itemId, isSelected];
}

class SelectAllCleanupItemsEvent extends CleanerEvent {
  final CleanupType? type;
  final bool isSelected;

  const SelectAllCleanupItemsEvent({
    this.type,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [type, isSelected];
}

class LoadCleanupHistoryEvent extends CleanerEvent {
  const LoadCleanupHistoryEvent();
}

class ClearCleanupHistoryEvent extends CleanerEvent {
  const ClearCleanupHistoryEvent();
}

class FindSpecificCleanupItemsEvent extends CleanerEvent {
  final CleanupType type;
  final Map<String, dynamic>? parameters;

  const FindSpecificCleanupItemsEvent({
    required this.type,
    this.parameters,
  });

  @override
  List<Object?> get props => [type, parameters];
}

class RefreshAnalysisEvent extends CleanerEvent {
  const RefreshAnalysisEvent();
}
