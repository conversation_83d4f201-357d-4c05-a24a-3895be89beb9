import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../domain/usecases/analyze_system.dart';
import '../../domain/usecases/perform_cleanup.dart';
import '../../domain/repositories/cleaner_repository.dart';
import '../../domain/entities/cleanup_item.dart';
import 'cleaner_event.dart';
import 'cleaner_state.dart';

@injectable
class CleanerBloc extends Bloc<CleanerEvent, CleanerState> {
  final AnalyzeSystem _analyzeSystem;
  final PerformCleanup _performCleanup;
  final CleanerRepository _repository;

  StreamSubscription? _analysisSubscription;
  StreamSubscription? _cleanupSubscription;
  List<CleanupItem> _currentCleanupItems = [];

  CleanerBloc(this._analyzeSystem, this._performCleanup, this._repository)
    : super(const CleanerInitial()) {
    on<StartSystemAnalysisEvent>(_onStartSystemAnalysis);
    on<CancelSystemAnalysisEvent>(_onCancelSystemAnalysis);
    on<StartCleanupEvent>(_onStartCleanup);
    on<CancelCleanupEvent>(_onCancelCleanup);
    on<ToggleCleanupItemEvent>(_onToggleCleanupItem);
    on<SelectAllCleanupItemsEvent>(_onSelectAllCleanupItems);
    on<LoadCleanupHistoryEvent>(_onLoadCleanupHistory);
    on<ClearCleanupHistoryEvent>(_onClearCleanupHistory);
    on<FindSpecificCleanupItemsEvent>(_onFindSpecificCleanupItems);
    on<RefreshAnalysisEvent>(_onRefreshAnalysis);
  }

  @override
  Future<void> close() {
    _analysisSubscription?.cancel();
    _cleanupSubscription?.cancel();
    return super.close();
  }

  Future<void> _onStartSystemAnalysis(
    StartSystemAnalysisEvent event,
    Emitter<CleanerState> emit,
  ) async {
    emit(const SystemAnalysisLoading());

    await _analysisSubscription?.cancel();

    _analysisSubscription = _analyzeSystem.stream().listen(
      (analysis) {
        if (!emit.isDone) {
          _currentCleanupItems = analysis.cleanupItems.cast<CleanupItem>();

          if (analysis.analysisDuration.inSeconds > 0) {
            emit(SystemAnalysisCompleted(analysis));
          } else {
            emit(SystemAnalysisInProgress(analysis));
          }
        }
      },
      onError: (error) {
        if (!emit.isDone) {
          emit(SystemAnalysisError('System analysis failed: $error'));
        }
      },
    );
  }

  Future<void> _onCancelSystemAnalysis(
    CancelSystemAnalysisEvent event,
    Emitter<CleanerState> emit,
  ) async {
    await _analysisSubscription?.cancel();
    _analysisSubscription = null;

    final result = await _repository.cancelAnalysis();
    result.fold(
      (failure) => emit(SystemAnalysisError(failure.message)),
      (_) => emit(const CleanerInitial()),
    );
  }

  Future<void> _onStartCleanup(
    StartCleanupEvent event,
    Emitter<CleanerState> emit,
  ) async {
    emit(const CleanupLoading());

    await _cleanupSubscription?.cancel();

    _cleanupSubscription = _performCleanup
        .stream(event.items)
        .listen(
          (result) {
            if (!emit.isDone) {
              if (result.isComplete) {
                emit(CleanupCompleted(result));
                // Save to history
                _repository.saveCleanupResult(result);
              } else {
                emit(CleanupInProgress(result));
              }
            }
          },
          onError: (error) {
            if (!emit.isDone) {
              emit(CleanupError('Cleanup failed: $error'));
            }
          },
        );
  }

  Future<void> _onCancelCleanup(
    CancelCleanupEvent event,
    Emitter<CleanerState> emit,
  ) async {
    await _cleanupSubscription?.cancel();
    _cleanupSubscription = null;

    final result = await _repository.cancelCleanup();
    result.fold(
      (failure) => emit(CleanupError(failure.message)),
      (_) => emit(const CleanerInitial()),
    );
  }

  void _onToggleCleanupItem(
    ToggleCleanupItemEvent event,
    Emitter<CleanerState> emit,
  ) {
    final updatedItems =
        _currentCleanupItems.map((item) {
          if (item.id == event.itemId) {
            return item.copyWith(isSelected: event.isSelected);
          }
          return item;
        }).toList();

    _currentCleanupItems = updatedItems;
    emit(CleanupItemsUpdated(updatedItems));
  }

  void _onSelectAllCleanupItems(
    SelectAllCleanupItemsEvent event,
    Emitter<CleanerState> emit,
  ) {
    final updatedItems =
        _currentCleanupItems.map((item) {
          if (event.type == null || item.type == event.type) {
            return item.copyWith(isSelected: event.isSelected);
          }
          return item;
        }).toList();

    _currentCleanupItems = updatedItems;
    emit(CleanupItemsUpdated(updatedItems));
  }

  Future<void> _onLoadCleanupHistory(
    LoadCleanupHistoryEvent event,
    Emitter<CleanerState> emit,
  ) async {
    final result = await _repository.getCleanupHistory();

    result.fold(
      (failure) => emit(SystemAnalysisError(failure.message)),
      (history) => emit(CleanupHistoryLoaded(history)),
    );
  }

  Future<void> _onClearCleanupHistory(
    ClearCleanupHistoryEvent event,
    Emitter<CleanerState> emit,
  ) async {
    final result = await _repository.clearCleanupHistory();

    result.fold(
      (failure) => emit(SystemAnalysisError(failure.message)),
      (_) => emit(const CleanupHistoryLoaded([])),
    );
  }

  Future<void> _onFindSpecificCleanupItems(
    FindSpecificCleanupItemsEvent event,
    Emitter<CleanerState> emit,
  ) async {
    try {
      List<CleanupItem> items = [];

      switch (event.type) {
        case CleanupType.cache:
          final result = await _repository.findCacheFiles();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.temporaryFiles:
          final result = await _repository.findTemporaryFiles();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.logs:
          final result = await _repository.findLogFiles();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.thumbnails:
          final result = await _repository.findThumbnails();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.emptyFolders:
          final result = await _repository.findEmptyFolders();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.largeFiles:
          final minSizeMB = event.parameters?['minSizeMB'] as int? ?? 100;
          final result = await _repository.findLargeFiles(minSizeMB);
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.oldFiles:
          final daysOld = event.parameters?['daysOld'] as int? ?? 30;
          final result = await _repository.findOldFiles(daysOld);
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.apkFiles:
          final result = await _repository.findApkFiles();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.residualFiles:
          final result = await _repository.findResidualFiles();
          result.fold((failure) => throw failure, (data) => items = data);
          break;
        case CleanupType.downloads:
          // TODO: Implement downloads cleanup
          break;
      }

      emit(SpecificCleanupItemsFound(type: event.type, items: items));
    } catch (e) {
      emit(SystemAnalysisError('Failed to find ${event.type} items: $e'));
    }
  }

  Future<void> _onRefreshAnalysis(
    RefreshAnalysisEvent event,
    Emitter<CleanerState> emit,
  ) async {
    add(const StartSystemAnalysisEvent());
  }
}
