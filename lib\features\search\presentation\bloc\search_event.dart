import 'package:equatable/equatable.dart';
import '../../domain/entities/search_filter.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

class StartSearchEvent extends SearchEvent {
  final SearchFilter filter;

  const StartSearchEvent(this.filter);

  @override
  List<Object?> get props => [filter];
}

class QuickSearchEvent extends SearchEvent {
  final String query;

  const QuickSearchEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class CancelSearchEvent extends SearchEvent {
  const CancelSearchEvent();
}

class StartDuplicateSearchEvent extends SearchEvent {
  final DuplicateSearchFilter filter;

  const StartDuplicateSearchEvent(this.filter);

  @override
  List<Object?> get props => [filter];
}

class CancelDuplicateSearchEvent extends SearchEvent {
  const CancelDuplicateSearchEvent();
}

class LoadSearchHistoryEvent extends SearchEvent {
  const LoadSearchHistoryEvent();
}

class SaveSearchToHistoryEvent extends SearchEvent {
  final SearchFilter filter;

  const SaveSearchToHistoryEvent(this.filter);

  @override
  List<Object?> get props => [filter];
}

class ClearSearchHistoryEvent extends SearchEvent {
  const ClearSearchHistoryEvent();
}

class GetSearchSuggestionsEvent extends SearchEvent {
  final String query;

  const GetSearchSuggestionsEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class UpdateSearchFilterEvent extends SearchEvent {
  final SearchFilter filter;

  const UpdateSearchFilterEvent(this.filter);

  @override
  List<Object?> get props => [filter];
}

class DeleteDuplicateFilesEvent extends SearchEvent {
  final String groupId;
  final List<String> filesToDelete;

  const DeleteDuplicateFilesEvent({
    required this.groupId,
    required this.filesToDelete,
  });

  @override
  List<Object?> get props => [groupId, filesToDelete];
}
