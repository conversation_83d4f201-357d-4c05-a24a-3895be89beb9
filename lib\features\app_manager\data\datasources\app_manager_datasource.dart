import 'dart:async';
import 'dart:math';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/app_info.dart';
import '../../../../core/error/exceptions.dart';

// Temporary models until we create separate files
class AppInfoModel extends AppInfo {
  const AppInfoModel({
    required super.packageName,
    required super.appName,
    required super.version,
    required super.versionCode,
    required super.installedSize,
    required super.dataSize,
    required super.cacheSize,
    required super.installDate,
    required super.lastUpdateDate,
    required super.lastUsedDate,
    super.iconPath,
    required super.type,
    required super.status,
    required super.permissions,
    required super.isSystemApp,
    required super.isEnabled,
    required super.canUninstall,
    required super.canDisable,
    required super.canClearData,
    required super.canClearCache,
    super.apkPath,
    super.dataPath,
    required super.targetSdkVersion,
    required super.minSdkVersion,
    super.signature,
  });

  factory AppInfoModel.fromEntity(AppInfo entity) {
    return AppInfoModel(
      packageName: entity.packageName,
      appName: entity.appName,
      version: entity.version,
      versionCode: entity.versionCode,
      installedSize: entity.installedSize,
      dataSize: entity.dataSize,
      cacheSize: entity.cacheSize,
      installDate: entity.installDate,
      lastUpdateDate: entity.lastUpdateDate,
      lastUsedDate: entity.lastUsedDate,
      iconPath: entity.iconPath,
      type: entity.type,
      status: entity.status,
      permissions: entity.permissions,
      isSystemApp: entity.isSystemApp,
      isEnabled: entity.isEnabled,
      canUninstall: entity.canUninstall,
      canDisable: entity.canDisable,
      canClearData: entity.canClearData,
      canClearCache: entity.canClearCache,
      apkPath: entity.apkPath,
      dataPath: entity.dataPath,
      targetSdkVersion: entity.targetSdkVersion,
      minSdkVersion: entity.minSdkVersion,
      signature: entity.signature,
    );
  }

  factory AppInfoModel.mock({
    String? packageName,
    String? appName,
    bool isSystemApp = false,
  }) {
    final now = DateTime.now();
    return AppInfoModel(
      packageName: packageName ?? 'com.example.app',
      appName: appName ?? 'Example App',
      version: '1.0.0',
      versionCode: '1',
      installedSize: 50 * 1024 * 1024, // 50MB
      dataSize: 10 * 1024 * 1024, // 10MB
      cacheSize: 5 * 1024 * 1024, // 5MB
      installDate: now.subtract(const Duration(days: 30)),
      lastUpdateDate: now.subtract(const Duration(days: 7)),
      lastUsedDate: now.subtract(const Duration(hours: 2)),
      type: isSystemApp ? AppType.system : AppType.user,
      status: AppStatus.stopped,
      permissions: ['android.permission.INTERNET', 'android.permission.CAMERA'],
      isSystemApp: isSystemApp,
      isEnabled: true,
      canUninstall: !isSystemApp,
      canDisable: true,
      canClearData: true,
      canClearCache: true,
      targetSdkVersion: 33,
      minSdkVersion: 21,
    );
  }
}

class AppUsageStatsModel extends AppUsageStats {
  const AppUsageStatsModel({
    required super.packageName,
    required super.totalTimeInForeground,
    required super.launchCount,
    required super.lastTimeUsed,
    required super.screenTime,
    required super.notificationCount,
    required super.dailyUsage,
  });
}

class AppBatchOperationModel extends AppBatchOperation {
  const AppBatchOperationModel({
    required super.id,
    required super.type,
    required super.packageNames,
    required super.status,
    required super.totalApps,
    required super.processedApps,
    required super.successfulApps,
    required super.failedApps,
    required super.failedPackages,
    required super.startTime,
    super.endTime,
    super.errorMessage,
  });
}

abstract class AppManagerDataSource {
  Future<List<AppInfoModel>> getAllApps();
  Future<List<AppInfoModel>> getUserApps();
  Future<List<AppInfoModel>> getSystemApps();
  Future<List<AppInfoModel>> getRunningApps();
  Future<AppInfoModel> getAppInfo(String packageName);

  Future<void> uninstallApp(String packageName);
  Future<void> disableApp(String packageName);
  Future<void> enableApp(String packageName);
  Future<void> clearAppData(String packageName);
  Future<void> clearAppCache(String packageName);
  Future<void> forceStopApp(String packageName);
  Future<void> launchApp(String packageName);

  Future<AppBatchOperationModel> startBatchOperation(
    AppBatchOperationType type,
    List<String> packageNames,
  );
  Stream<AppBatchOperationModel> getBatchOperationStream(String operationId);
  Future<void> cancelBatchOperation(String operationId);

  Future<AppUsageStatsModel> getAppUsageStats(String packageName);
  Future<List<AppUsageStatsModel>> getAllUsageStats();
  Future<Map<String, Duration>> getDailyUsageStats(DateTime date);

  Future<String> backupApp(String packageName, String backupPath);
  Future<void> restoreApp(String backupPath);
  Future<List<String>> getAvailableBackups();

  Future<List<String>> getAppPermissions(String packageName);
  Future<Map<String, bool>> getPermissionStatus(String packageName);
  Future<void> grantPermission(String packageName, String permission);
  Future<void> revokePermission(String packageName, String permission);

  Future<Map<String, int>> getAppSizeAnalysis();
  Future<List<AppInfoModel>> getLargestApps(int limit);
  Future<List<AppInfoModel>> getUnusedApps(int daysUnused);
  Future<List<AppInfoModel>> getAppsWithPermission(String permission);

  Future<void> installApk(String apkPath);
  Stream<double> getInstallationProgress(String apkPath);
  Future<bool> verifyApkSignature(String apkPath);

  Future<List<AppInfoModel>> getUpdatableApps();
  Future<void> updateApp(String packageName);
  Future<void> updateAllApps();

  Future<List<AppInfoModel>> getDefaultApps();
  Future<void> setDefaultApp(String packageName, String category);
  Future<void> clearDefaultApp(String category);
}

@LazySingleton(as: AppManagerDataSource)
class AppManagerDataSourceImpl implements AppManagerDataSource {
  final Uuid _uuid = const Uuid();
  final Map<String, StreamController<AppBatchOperationModel>> _batchOperations =
      {};
  final Map<String, StreamController<double>> _installationProgress = {};

  // Mock data for demonstration
  static final List<AppInfoModel> _mockApps = [
    AppInfoModel.mock(
      packageName: 'com.android.chrome',
      appName: 'Chrome',
      isSystemApp: false,
    ),
    AppInfoModel.mock(
      packageName: 'com.whatsapp',
      appName: 'WhatsApp',
      isSystemApp: false,
    ),
    AppInfoModel.mock(
      packageName: 'com.android.settings',
      appName: 'Settings',
      isSystemApp: true,
    ),
    AppInfoModel.mock(
      packageName: 'com.android.systemui',
      appName: 'System UI',
      isSystemApp: true,
    ),
    AppInfoModel.mock(
      packageName: 'com.spotify.music',
      appName: 'Spotify',
      isSystemApp: false,
    ),
    AppInfoModel.mock(
      packageName: 'com.instagram.android',
      appName: 'Instagram',
      isSystemApp: false,
    ),
    AppInfoModel.mock(
      packageName: 'com.google.android.gms',
      appName: 'Google Play Services',
      isSystemApp: true,
    ),
    AppInfoModel.mock(
      packageName: 'com.android.vending',
      appName: 'Google Play Store',
      isSystemApp: true,
    ),
  ];

  @override
  Future<List<AppInfoModel>> getAllApps() async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate API delay
    return List.from(_mockApps);
  }

  @override
  Future<List<AppInfoModel>> getUserApps() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockApps.where((app) => !app.isSystemApp).toList();
  }

  @override
  Future<List<AppInfoModel>> getSystemApps() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockApps.where((app) => app.isSystemApp).toList();
  }

  @override
  Future<List<AppInfoModel>> getRunningApps() async {
    await Future.delayed(const Duration(milliseconds: 200));
    final random = Random();
    return _mockApps
        .where((app) => random.nextBool())
        .map(
          (app) =>
              AppInfoModel.fromEntity(app.copyWith(status: AppStatus.running)),
        )
        .toList();
  }

  @override
  Future<AppInfoModel> getAppInfo(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final app = _mockApps.firstWhere(
      (app) => app.packageName == packageName,
      orElse: () => throw ServerException(message: 'App not found'),
    );
    return app;
  }

  @override
  Future<void> uninstallApp(String packageName) async {
    await Future.delayed(const Duration(seconds: 2));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    final app = _mockApps[appIndex];
    if (!app.canUninstall) {
      throw ServerException(message: 'Cannot uninstall system app');
    }

    _mockApps.removeAt(appIndex);
  }

  @override
  Future<void> disableApp(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    final app = _mockApps[appIndex];
    if (!app.canDisable) {
      throw ServerException(message: 'Cannot disable this app');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(
      app.copyWith(isEnabled: false),
    );
  }

  @override
  Future<void> enableApp(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(
      _mockApps[appIndex].copyWith(isEnabled: true),
    );
  }

  @override
  Future<void> clearAppData(String packageName) async {
    await Future.delayed(const Duration(seconds: 1));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    final app = _mockApps[appIndex];
    if (!app.canClearData) {
      throw ServerException(message: 'Cannot clear data for this app');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(app.copyWith(dataSize: 0));
  }

  @override
  Future<void> clearAppCache(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 800));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    final app = _mockApps[appIndex];
    if (!app.canClearCache) {
      throw ServerException(message: 'Cannot clear cache for this app');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(app.copyWith(cacheSize: 0));
  }

  @override
  Future<void> forceStopApp(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(
      _mockApps[appIndex].copyWith(status: AppStatus.stopped),
    );
  }

  @override
  Future<void> launchApp(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final appIndex = _mockApps.indexWhere(
      (app) => app.packageName == packageName,
    );
    if (appIndex == -1) {
      throw ServerException(message: 'App not found');
    }

    _mockApps[appIndex] = AppInfoModel.fromEntity(
      _mockApps[appIndex].copyWith(
        status: AppStatus.running,
        lastUsedDate: DateTime.now(),
      ),
    );
  }

  @override
  Future<AppBatchOperationModel> startBatchOperation(
    AppBatchOperationType type,
    List<String> packageNames,
  ) async {
    final operationId = _uuid.v4();
    final operation = AppBatchOperationModel(
      id: operationId,
      type: type,
      packageNames: packageNames,
      status: AppBatchOperationStatus.pending,
      totalApps: packageNames.length,
      processedApps: 0,
      successfulApps: 0,
      failedApps: 0,
      failedPackages: [],
      startTime: DateTime.now(),
    );

    final controller = StreamController<AppBatchOperationModel>();
    _batchOperations[operationId] = controller;

    // Start the batch operation in background
    _processBatchOperation(operation, controller);

    return operation;
  }

  Future<void> _processBatchOperation(
    AppBatchOperationModel operation,
    StreamController<AppBatchOperationModel> controller,
  ) async {
    var currentOperation = AppBatchOperationModel(
      id: operation.id,
      type: operation.type,
      packageNames: operation.packageNames,
      status: AppBatchOperationStatus.running,
      totalApps: operation.totalApps,
      processedApps: 0,
      successfulApps: 0,
      failedApps: 0,
      failedPackages: [],
      startTime: operation.startTime,
    );

    controller.add(currentOperation);

    for (int i = 0; i < operation.packageNames.length; i++) {
      if (!_batchOperations.containsKey(operation.id)) {
        // Operation was cancelled
        break;
      }

      final packageName = operation.packageNames[i];
      bool success = true;

      try {
        switch (operation.type) {
          case AppBatchOperationType.uninstall:
            await uninstallApp(packageName);
            break;
          case AppBatchOperationType.disable:
            await disableApp(packageName);
            break;
          case AppBatchOperationType.enable:
            await enableApp(packageName);
            break;
          case AppBatchOperationType.clearData:
            await clearAppData(packageName);
            break;
          case AppBatchOperationType.clearCache:
            await clearAppCache(packageName);
            break;
          case AppBatchOperationType.backup:
            await backupApp(packageName, '/backup/');
            break;
        }
      } catch (e) {
        success = false;
      }

      currentOperation = AppBatchOperationModel(
        id: operation.id,
        type: operation.type,
        packageNames: operation.packageNames,
        status: AppBatchOperationStatus.running,
        totalApps: operation.totalApps,
        processedApps: i + 1,
        successfulApps: currentOperation.successfulApps + (success ? 1 : 0),
        failedApps: currentOperation.failedApps + (success ? 0 : 1),
        failedPackages:
            success
                ? currentOperation.failedPackages
                : [...currentOperation.failedPackages, packageName],
        startTime: operation.startTime,
      );

      controller.add(currentOperation);
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate processing time
    }

    // Complete the operation
    final finalOperation = AppBatchOperationModel(
      id: operation.id,
      type: operation.type,
      packageNames: operation.packageNames,
      status: AppBatchOperationStatus.completed,
      totalApps: currentOperation.totalApps,
      processedApps: currentOperation.processedApps,
      successfulApps: currentOperation.successfulApps,
      failedApps: currentOperation.failedApps,
      failedPackages: currentOperation.failedPackages,
      startTime: operation.startTime,
      endTime: DateTime.now(),
    );

    controller.add(finalOperation);
    controller.close();
    _batchOperations.remove(operation.id);
  }

  @override
  Stream<AppBatchOperationModel> getBatchOperationStream(String operationId) {
    final controller = _batchOperations[operationId];
    if (controller == null) {
      throw ServerException(message: 'Batch operation not found');
    }
    return controller.stream;
  }

  @override
  Future<void> cancelBatchOperation(String operationId) async {
    final controller = _batchOperations.remove(operationId);
    controller?.close();
  }

  @override
  Future<AppUsageStatsModel> getAppUsageStats(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final random = Random();

    return AppUsageStatsModel(
      packageName: packageName,
      totalTimeInForeground: Duration(hours: random.nextInt(100)),
      launchCount: random.nextInt(500),
      lastTimeUsed: DateTime.now().subtract(
        Duration(hours: random.nextInt(24)),
      ),
      screenTime: Duration(hours: random.nextInt(50)),
      notificationCount: random.nextInt(100),
      dailyUsage: {},
    );
  }

  @override
  Future<List<AppUsageStatsModel>> getAllUsageStats() async {
    await Future.delayed(const Duration(milliseconds: 500));
    final stats = <AppUsageStatsModel>[];

    for (final app in _mockApps) {
      stats.add(await getAppUsageStats(app.packageName));
    }

    return stats;
  }

  @override
  Future<Map<String, Duration>> getDailyUsageStats(DateTime date) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final random = Random();
    final stats = <String, Duration>{};

    for (final app in _mockApps) {
      stats[app.packageName] = Duration(minutes: random.nextInt(300));
    }

    return stats;
  }

  @override
  Future<String> backupApp(String packageName, String backupPath) async {
    await Future.delayed(const Duration(seconds: 3));
    return '$backupPath$packageName.backup';
  }

  @override
  Future<void> restoreApp(String backupPath) async {
    await Future.delayed(const Duration(seconds: 5));
  }

  @override
  Future<List<String>> getAvailableBackups() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      '/backup/com.example.app1.backup',
      '/backup/com.example.app2.backup',
    ];
  }

  @override
  Future<List<String>> getAppPermissions(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final app = _mockApps.firstWhere(
      (app) => app.packageName == packageName,
      orElse: () => throw ServerException(message: 'App not found'),
    );
    return app.permissions;
  }

  @override
  Future<Map<String, bool>> getPermissionStatus(String packageName) async {
    await Future.delayed(const Duration(milliseconds: 150));
    final permissions = await getAppPermissions(packageName);
    final random = Random();

    return Map.fromEntries(
      permissions.map((permission) => MapEntry(permission, random.nextBool())),
    );
  }

  @override
  Future<void> grantPermission(String packageName, String permission) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<void> revokePermission(String packageName, String permission) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<Map<String, int>> getAppSizeAnalysis() async {
    await Future.delayed(const Duration(milliseconds: 400));
    final analysis = <String, int>{};

    for (final app in _mockApps) {
      analysis[app.appName] = app.totalSize;
    }

    return analysis;
  }

  @override
  Future<List<AppInfoModel>> getLargestApps(int limit) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final sortedApps = List<AppInfoModel>.from(_mockApps);
    sortedApps.sort((a, b) => b.totalSize.compareTo(a.totalSize));
    return sortedApps.take(limit).toList();
  }

  @override
  Future<List<AppInfoModel>> getUnusedApps(int daysUnused) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final cutoffDate = DateTime.now().subtract(Duration(days: daysUnused));
    return _mockApps
        .where((app) => app.lastUsedDate.isBefore(cutoffDate))
        .toList();
  }

  @override
  Future<List<AppInfoModel>> getAppsWithPermission(String permission) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockApps
        .where((app) => app.permissions.contains(permission))
        .toList();
  }

  @override
  Future<void> installApk(String apkPath) async {
    final controller = StreamController<double>();
    _installationProgress[apkPath] = controller;

    // Simulate installation progress
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      controller.add(i / 100.0);
    }

    controller.close();
    _installationProgress.remove(apkPath);
  }

  @override
  Stream<double> getInstallationProgress(String apkPath) {
    final controller = _installationProgress[apkPath];
    if (controller == null) {
      throw ServerException(message: 'Installation not found');
    }
    return controller.stream;
  }

  @override
  Future<bool> verifyApkSignature(String apkPath) async {
    await Future.delayed(const Duration(seconds: 1));
    return Random().nextBool();
  }

  @override
  Future<List<AppInfoModel>> getUpdatableApps() async {
    await Future.delayed(const Duration(milliseconds: 500));
    final random = Random();
    return _mockApps.where((app) => random.nextBool()).toList();
  }

  @override
  Future<void> updateApp(String packageName) async {
    await Future.delayed(const Duration(seconds: 3));
  }

  @override
  Future<void> updateAllApps() async {
    await Future.delayed(const Duration(seconds: 10));
  }

  @override
  Future<List<AppInfoModel>> getDefaultApps() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockApps.take(3).toList();
  }

  @override
  Future<void> setDefaultApp(String packageName, String category) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<void> clearDefaultApp(String category) async {
    await Future.delayed(const Duration(milliseconds: 200));
  }
}
