import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/cleaner_bloc.dart';
import '../bloc/cleaner_event.dart';
import '../bloc/cleaner_state.dart';
import '../../domain/entities/cleanup_item.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class CleanupHistoryWidget extends StatelessWidget {
  const CleanupHistoryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CleanerBloc, CleanerState>(
      builder: (context, state) {
        if (state is CleanupHistoryLoaded) {
          return _buildHistoryList(context, state.history);
        }

        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildHistoryList(BuildContext context, List<CleanupResult> history) {
    if (history.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Cleanup History',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Your cleanup history will appear here',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final totalCleaned = history.fold<int>(
      0,
      (sum, result) => sum + result.totalDeletedSize,
    );

    final totalFiles = history.fold<int>(
      0,
      (sum, result) => sum + result.totalDeletedCount,
    );

    return Column(
      children: [
        // Summary header
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Cleanup History',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        Text(
                          '${history.length} cleanup sessions',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _showClearHistoryDialog(context),
                    icon: const Icon(Icons.delete_outline),
                    tooltip: 'Clear History',
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Statistics
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Total Cleaned',
                      FileUtils.formatFileSize(totalCleaned),
                      Icons.cleaning_services,
                      AppTheme.successColor,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Files Deleted',
                      totalFiles.toString(),
                      Icons.delete,
                      AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // History list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: history.length,
            itemBuilder: (context, index) {
              final result = history[index];
              return Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.defaultPadding,
                ),
                child: CleanupHistoryCard(result: result),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(label, style: Theme.of(context).textTheme.bodySmall),
        ],
      ),
    );
  }

  void _showClearHistoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear History'),
            content: const Text(
              'Are you sure you want to clear all cleanup history? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<CleanerBloc>().add(
                    const ClearCleanupHistoryEvent(),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear'),
              ),
            ],
          ),
    );
  }
}

class CleanupHistoryCard extends StatelessWidget {
  final CleanupResult result;

  const CleanupHistoryCard({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  result.isComplete ? Icons.check_circle : Icons.warning,
                  color:
                      result.isComplete
                          ? AppTheme.successColor
                          : AppTheme.warningColor,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    result.isComplete
                        ? 'Cleanup Completed'
                        : 'Cleanup Incomplete',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color:
                          result.isComplete
                              ? AppTheme.successColor
                              : AppTheme.warningColor,
                    ),
                  ),
                ),
                Text(
                  _formatDuration(result.cleanupDuration),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Statistics
            Row(
              children: [
                Expanded(
                  child: _buildResultStat(
                    context,
                    'Files Deleted',
                    result.totalDeletedCount.toString(),
                    Icons.delete,
                  ),
                ),
                Expanded(
                  child: _buildResultStat(
                    context,
                    'Space Freed',
                    FileUtils.formatFileSize(result.totalDeletedSize),
                    Icons.storage,
                  ),
                ),
                if (result.failedDeletions.isNotEmpty)
                  Expanded(
                    child: _buildResultStat(
                      context,
                      'Failed',
                      result.failedDeletions.length.toString(),
                      Icons.error,
                      color: AppTheme.errorColor,
                    ),
                  ),
              ],
            ),

            // Error message if any
            if (result.errorMessage != null) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    AppConstants.smallBorderRadius,
                  ),
                  border: Border.all(
                    color: AppTheme.errorColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: AppTheme.errorColor,
                      size: 16,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        result.errorMessage!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultStat(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    final statColor = color ?? AppTheme.primaryColor;

    return Column(
      children: [
        Icon(icon, color: statColor, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: statColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds.remainder(60)}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}
