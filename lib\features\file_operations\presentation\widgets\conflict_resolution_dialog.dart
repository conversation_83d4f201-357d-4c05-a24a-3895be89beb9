import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class ConflictResolutionDialog extends StatefulWidget {
  final List<String> conflictingFiles;
  final List<String> sourcePaths;
  final String destinationPath;

  const ConflictResolutionDialog({
    super.key,
    required this.conflictingFiles,
    required this.sourcePaths,
    required this.destinationPath,
  });

  @override
  State<ConflictResolutionDialog> createState() =>
      _ConflictResolutionDialogState();
}

class _ConflictResolutionDialogState extends State<ConflictResolutionDialog> {
  ConflictResolution _selectedResolution = ConflictResolution.ask;
  final Map<String, ConflictResolution> _fileResolutions = {};

  @override
  void initState() {
    super.initState();
    // Initialize individual file resolutions
    for (final file in widget.conflictingFiles) {
      _fileResolutions[file] = ConflictResolution.ask;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('File Conflicts Detected'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'The following files already exist in the destination:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Global resolution
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(
                  AppConstants.smallBorderRadius,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Apply to all conflicts:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),

                  Wrap(
                    spacing: AppConstants.smallPadding,
                    children:
                        ConflictResolution.values.map((resolution) {
                          return ChoiceChip(
                            label: Text(_getResolutionName(resolution)),
                            selected: _selectedResolution == resolution,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedResolution = resolution;
                                  if (resolution != ConflictResolution.ask) {
                                    // Apply to all files
                                    for (final file
                                        in widget.conflictingFiles) {
                                      _fileResolutions[file] = resolution;
                                    }
                                  }
                                });
                              }
                            },
                          );
                        }).toList(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Individual file conflicts
            Expanded(
              child: ListView.builder(
                itemCount: widget.conflictingFiles.length,
                itemBuilder: (context, index) {
                  final file = widget.conflictingFiles[index];
                  return _buildConflictItem(file);
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _applyResolution, child: const Text('Apply')),
      ],
    );
  }

  Widget _buildConflictItem(String filePath) {
    final fileName = FileUtils.getFileName(filePath);
    final resolution = _fileResolutions[filePath] ?? ConflictResolution.ask;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.orange[600], size: 20),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    fileName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.smallPadding),

            Text(
              filePath,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Individual resolution options
            if (_selectedResolution == ConflictResolution.ask)
              Wrap(
                spacing: AppConstants.smallPadding,
                children:
                    [
                      ConflictResolution.replace,
                      ConflictResolution.skip,
                      ConflictResolution.rename,
                    ].map((res) {
                      return ChoiceChip(
                        label: Text(_getResolutionName(res)),
                        selected: resolution == res,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _fileResolutions[filePath] = res;
                            });
                          }
                        },
                      );
                    }).toList(),
              )
            else
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.smallPadding,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: _getResolutionColor(resolution).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    AppConstants.smallBorderRadius,
                  ),
                ),
                child: Text(
                  _getResolutionName(resolution),
                  style: TextStyle(
                    color: _getResolutionColor(resolution),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _applyResolution() {
    // Here you would typically pass the resolution back to the calling code
    // For now, we'll just close the dialog
    Navigator.of(context).pop({
      'globalResolution': _selectedResolution,
      'fileResolutions': _fileResolutions,
    });
  }

  String _getResolutionName(ConflictResolution resolution) {
    switch (resolution) {
      case ConflictResolution.ask:
        return 'Ask for each';
      case ConflictResolution.replace:
        return 'Replace';
      case ConflictResolution.skip:
        return 'Skip';
      case ConflictResolution.rename:
        return 'Rename';
      case ConflictResolution.replaceAll:
        return 'Replace All';
      case ConflictResolution.skipAll:
        return 'Skip All';
    }
  }

  Color _getResolutionColor(ConflictResolution resolution) {
    switch (resolution) {
      case ConflictResolution.replace:
      case ConflictResolution.replaceAll:
        return Colors.red;
      case ConflictResolution.skip:
      case ConflictResolution.skipAll:
        return Colors.orange;
      case ConflictResolution.rename:
        return Colors.blue;
      case ConflictResolution.ask:
        return Colors.grey;
    }
  }
}

enum ConflictResolution { ask, replace, skip, rename, replaceAll, skipAll }
