import 'dart:io';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import '../models/storage_info_model.dart';
import '../../../../core/error/exceptions.dart';

abstract class StorageDataSource {
  Future<List<StorageInfoModel>> getStorageInfo();
  Future<StorageInfoModel> getInternalStorageInfo();
  Future<List<StorageInfoModel>> getExternalStorageInfo();
  Future<int> getDirectorySize(String path);
  Future<Map<String, int>> getCategorizedStorageUsage();
}

@LazySingleton(as: StorageDataSource)
class StorageDataSourceImpl implements StorageDataSource {
  @override
  Future<List<StorageInfoModel>> getStorageInfo() async {
    try {
      final storageList = <StorageInfoModel>[];
      
      // Add internal storage
      final internalStorage = await getInternalStorageInfo();
      storageList.add(internalStorage);
      
      // Add external storage
      final externalStorages = await getExternalStorageInfo();
      storageList.addAll(externalStorages);
      
      return storageList;
    } catch (e) {
      throw ServerException(message: 'Failed to get storage info: $e');
    }
  }

  @override
  Future<StorageInfoModel> getInternalStorageInfo() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '/storage/emulated/0'; // Android internal storage path
      
      final stat = await Directory(path).stat();
      final totalSpace = _getAvailableSpace(path);
      final freeSpace = _getFreeSpace(path);
      final usedSpace = totalSpace - freeSpace;

      return StorageInfoModel(
        totalSpace: totalSpace,
        freeSpace: freeSpace,
        usedSpace: usedSpace,
        path: path,
        type: StorageType.internal,
        isRemovable: false,
        isEmulated: true,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to get internal storage info: $e');
    }
  }

  @override
  Future<List<StorageInfoModel>> getExternalStorageInfo() async {
    try {
      final externalStorages = <StorageInfoModel>[];
      
      // Check for SD card and other external storage
      final externalPaths = [
        '/storage/sdcard1',
        '/storage/extSdCard',
        '/storage/external_SD',
        '/mnt/sdcard/external_sd',
      ];

      for (final path in externalPaths) {
        if (await Directory(path).exists()) {
          try {
            final totalSpace = _getAvailableSpace(path);
            final freeSpace = _getFreeSpace(path);
            final usedSpace = totalSpace - freeSpace;

            final storage = StorageInfoModel(
              totalSpace: totalSpace,
              freeSpace: freeSpace,
              usedSpace: usedSpace,
              path: path,
              type: StorageType.external,
              isRemovable: true,
              isEmulated: false,
            );
            
            externalStorages.add(storage);
          } catch (e) {
            // Skip if can't access this storage
            continue;
          }
        }
      }

      return externalStorages;
    } catch (e) {
      throw ServerException(message: 'Failed to get external storage info: $e');
    }
  }

  @override
  Future<int> getDirectorySize(String path) async {
    try {
      final directory = Directory(path);
      if (!await directory.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            // Skip files that can't be accessed
            continue;
          }
        }
      }

      return totalSize;
    } catch (e) {
      throw ServerException(message: 'Failed to calculate directory size: $e');
    }
  }

  @override
  Future<Map<String, int>> getCategorizedStorageUsage() async {
    try {
      final basePath = '/storage/emulated/0';
      final categories = <String, int>{};

      // Common Android directories
      final categoryPaths = {
        'Images': ['$basePath/DCIM', '$basePath/Pictures'],
        'Videos': ['$basePath/Movies', '$basePath/DCIM'],
        'Audio': ['$basePath/Music', '$basePath/Podcasts'],
        'Documents': ['$basePath/Documents', '$basePath/Download'],
        'Apps': ['/data/app'],
        'System': ['/system'],
        'Other': ['$basePath'],
      };

      for (final category in categoryPaths.keys) {
        int categorySize = 0;
        for (final path in categoryPaths[category]!) {
          if (await Directory(path).exists()) {
            categorySize += await getDirectorySize(path);
          }
        }
        categories[category] = categorySize;
      }

      return categories;
    } catch (e) {
      throw ServerException(message: 'Failed to get categorized storage usage: $e');
    }
  }

  // Helper methods to get storage space (simplified implementation)
  int _getAvailableSpace(String path) {
    // This is a simplified implementation
    // In a real app, you would use platform-specific code to get actual storage info
    return 64 * 1024 * 1024 * 1024; // 64GB as example
  }

  int _getFreeSpace(String path) {
    // This is a simplified implementation
    // In a real app, you would use platform-specific code to get actual free space
    return 32 * 1024 * 1024 * 1024; // 32GB as example
  }
}
