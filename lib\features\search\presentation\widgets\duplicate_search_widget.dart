import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/search_bloc.dart';
import '../bloc/search_event.dart';
import '../bloc/search_state.dart';
import '../../domain/entities/search_filter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class DuplicateSearchWidget extends StatefulWidget {
  const DuplicateSearchWidget({super.key});

  @override
  State<DuplicateSearchWidget> createState() => _DuplicateSearchWidgetState();
}

class _DuplicateSearchWidgetState extends State<DuplicateSearchWidget> {
  DuplicateSearchMethod _method = DuplicateSearchMethod.nameAndSize;
  final List<String> _searchPaths = ['/storage/emulated/0'];
  int? _minFileSize;
  bool _includeHidden = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        if (state is DuplicateSearchLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: AppConstants.defaultPadding),
                Text('Searching for duplicates...'),
                SizedBox(height: AppConstants.smallPadding),
                Text(
                  'This may take a while depending on the number of files',
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        if (state is DuplicateSearchInProgress) {
          return _buildDuplicateResults(context, state.partialResult, isInProgress: true);
        }

        if (state is DuplicateSearchCompleted) {
          return _buildDuplicateResults(context, state.result);
        }

        if (state is DuplicateSearchError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Search Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: _startDuplicateSearch,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return _buildSearchForm();
      },
    );
  }

  Widget _buildSearchForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Find Duplicate Files',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Scan your device for duplicate files to free up storage space',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Detection Method
          Text(
            'Detection Method',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          Column(
            children: DuplicateSearchMethod.values.map((method) {
              return RadioListTile<DuplicateSearchMethod>(
                title: Text(_getMethodName(method)),
                subtitle: Text(_getMethodDescription(method)),
                value: method,
                groupValue: _method,
                onChanged: (value) {
                  setState(() {
                    _method = value!;
                  });
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Minimum File Size
          Text(
            'Minimum File Size',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          TextFormField(
            decoration: const InputDecoration(
              labelText: 'Minimum size (MB)',
              hintText: 'Leave empty to include all files',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final size = double.tryParse(value);
              setState(() {
                _minFileSize = size != null ? (size * 1024 * 1024).round() : null;
              });
            },
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Options
          CheckboxListTile(
            title: const Text('Include Hidden Files'),
            value: _includeHidden,
            onChanged: (value) {
              setState(() {
                _includeHidden = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Start Search Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _startDuplicateSearch,
              icon: const Icon(Icons.search),
              label: const Text('Start Duplicate Search'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Warning
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.orange[700],
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Text(
                    'Duplicate search may take several minutes depending on the number of files on your device.',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDuplicateResults(
    BuildContext context,
    dynamic result,
    {bool isInProgress = false}
  ) {
    if (result.duplicateGroups.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: AppTheme.successColor,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Duplicates Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.successColor,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Your device is clean! No duplicate files were found.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  // Reset to search form
                });
              },
              child: const Text('Search Again'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Results Header
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${result.duplicateGroups.length} duplicate groups found',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (isInProgress)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                ],
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Row(
                children: [
                  Icon(
                    Icons.storage,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Wasted space: ${FileUtils.formatFileSize(result.totalWastedSpace)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.errorColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (!isInProgress)
                    Text(
                      'Scan completed in ${result.searchDuration.inSeconds}s',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        
        // Results List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: result.duplicateGroups.length,
            itemBuilder: (context, index) {
              final group = result.duplicateGroups[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: DuplicateGroupCard(
                  group: group,
                  onDeleteFiles: (filesToDelete) {
                    context.read<SearchBloc>().add(
                      DeleteDuplicateFilesEvent(
                        groupId: group.id,
                        filesToDelete: filesToDelete,
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _startDuplicateSearch() {
    final filter = DuplicateSearchFilter(
      searchPaths: _searchPaths,
      method: _method,
      minFileSize: _minFileSize,
      includeHidden: _includeHidden,
    );

    context.read<SearchBloc>().add(StartDuplicateSearchEvent(filter));
  }

  String _getMethodName(DuplicateSearchMethod method) {
    switch (method) {
      case DuplicateSearchMethod.nameOnly:
        return 'Name Only';
      case DuplicateSearchMethod.nameAndSize:
        return 'Name and Size';
      case DuplicateSearchMethod.contentHash:
        return 'Content Hash (Most Accurate)';
      case DuplicateSearchMethod.fuzzyName:
        return 'Fuzzy Name Matching';
    }
  }

  String _getMethodDescription(DuplicateSearchMethod method) {
    switch (method) {
      case DuplicateSearchMethod.nameOnly:
        return 'Find files with identical names';
      case DuplicateSearchMethod.nameAndSize:
        return 'Find files with same name and size (Recommended)';
      case DuplicateSearchMethod.contentHash:
        return 'Compare file contents (Slower but most accurate)';
      case DuplicateSearchMethod.fuzzyName:
        return 'Find files with similar names (e.g., copy, duplicate)';
    }
  }
}

class DuplicateGroupCard extends StatefulWidget {
  final dynamic group;
  final Function(List<String>) onDeleteFiles;

  const DuplicateGroupCard({
    super.key,
    required this.group,
    required this.onDeleteFiles,
  });

  @override
  State<DuplicateGroupCard> createState() => _DuplicateGroupCardState();
}

class _DuplicateGroupCardState extends State<DuplicateGroupCard> {
  bool _isExpanded = false;
  final Set<String> _selectedFiles = {};

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          // Header
          ListTile(
            leading: Icon(
              Icons.content_copy,
              color: AppTheme.warningColor,
            ),
            title: Text(
              widget.group.originalFile.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${widget.group.duplicateCount} duplicates • ${FileUtils.formatFileSize(widget.group.wastedSpace)} wasted',
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  FileUtils.formatFileSize(widget.group.totalSize),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                IconButton(
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
              ],
            ),
          ),
          
          // Expandable content
          if (_isExpanded) ...[
            const Divider(),
            
            // File list
            ...widget.group.duplicates.map<Widget>((file) {
              final isSelected = _selectedFiles.contains(file.path);
              return CheckboxListTile(
                value: isSelected,
                onChanged: (selected) {
                  setState(() {
                    if (selected == true) {
                      _selectedFiles.add(file.path);
                    } else {
                      _selectedFiles.remove(file.path);
                    }
                  });
                },
                title: Text(
                  file.name,
                  style: TextStyle(
                    fontWeight: file == widget.group.originalFile 
                        ? FontWeight.bold 
                        : FontWeight.normal,
                  ),
                ),
                subtitle: Text(
                  file.path,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                secondary: file == widget.group.originalFile
                    ? Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Original',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      )
                    : null,
              );
            }).toList(),
            
            // Action buttons
            if (_selectedFiles.isNotEmpty) ...[
              const Divider(),
              Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    Text(
                      '${_selectedFiles.length} files selected',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const Spacer(),
                    OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _selectedFiles.clear();
                        });
                      },
                      child: const Text('Clear'),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    ElevatedButton.icon(
                      onPressed: () => _confirmDelete(),
                      icon: const Icon(Icons.delete),
                      label: const Text('Delete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.errorColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Files'),
        content: Text(
          'Are you sure you want to delete ${_selectedFiles.length} duplicate files?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onDeleteFiles(_selectedFiles.toList());
              setState(() {
                _selectedFiles.clear();
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
