import 'package:equatable/equatable.dart';
import '../../domain/entities/search_result.dart';
import '../../domain/entities/search_filter.dart';

abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {
  const SearchInitial();
}

class SearchLoading extends SearchState {
  const SearchLoading();
}

class SearchInProgress extends SearchState {
  final SearchResult partialResult;

  const SearchInProgress(this.partialResult);

  @override
  List<Object?> get props => [partialResult];
}

class SearchCompleted extends SearchState {
  final SearchResult result;

  const SearchCompleted(this.result);

  @override
  List<Object?> get props => [result];
}

class SearchError extends SearchState {
  final String message;

  const SearchError(this.message);

  @override
  List<Object?> get props => [message];
}

class DuplicateSearchLoading extends SearchState {
  const DuplicateSearchLoading();
}

class DuplicateSearchInProgress extends SearchState {
  final DuplicateSearchResult partialResult;

  const DuplicateSearchInProgress(this.partialResult);

  @override
  List<Object?> get props => [partialResult];
}

class DuplicateSearchCompleted extends SearchState {
  final DuplicateSearchResult result;

  const DuplicateSearchCompleted(this.result);

  @override
  List<Object?> get props => [result];
}

class DuplicateSearchError extends SearchState {
  final String message;

  const DuplicateSearchError(this.message);

  @override
  List<Object?> get props => [message];
}

class SearchHistoryLoaded extends SearchState {
  final List<SearchFilter> history;

  const SearchHistoryLoaded(this.history);

  @override
  List<Object?> get props => [history];
}

class SearchSuggestionsLoaded extends SearchState {
  final List<String> suggestions;

  const SearchSuggestionsLoaded(this.suggestions);

  @override
  List<Object?> get props => [suggestions];
}

class SearchFilterUpdated extends SearchState {
  final SearchFilter filter;

  const SearchFilterUpdated(this.filter);

  @override
  List<Object?> get props => [filter];
}

class DuplicateFilesDeleted extends SearchState {
  final String groupId;
  final List<String> deletedFiles;

  const DuplicateFilesDeleted({
    required this.groupId,
    required this.deletedFiles,
  });

  @override
  List<Object?> get props => [groupId, deletedFiles];
}
