import '../../../../core/utils/typedef.dart';
import '../entities/root_access.dart';

abstract class RootToolsRepository {
  // Root Access Management
  ResultFuture<RootAccess> checkRootAccess();
  ResultFuture<bool> requestRootPermission();
  ResultVoid revokeRootPermission();
  ResultFuture<List<String>> getAvailableCommands();
  
  // Command Execution
  ResultFuture<RootCommandResult> executeCommand(String command);
  ResultFuture<RootCommandResult> executeCommandWithTimeout(
    String command,
    Duration timeout,
  );
  ResultFuture<List<RootCommandResult>> executeBatchCommands(
    List<String> commands,
  );
  Stream<String> executeCommandStream(String command);
  
  // System Information
  ResultFuture<List<SystemPartition>> getSystemPartitions();
  ResultFuture<List<SystemProcess>> getRunningProcesses();
  ResultFuture<List<SystemService>> getSystemServices();
  ResultFuture<Map<String, String>> getSystemProperties();
  ResultFuture<Map<String, dynamic>> getSystemInfo();
  
  // Process Management
  ResultVoid killProcess(int pid);
  ResultVoid killProcessByName(String processName);
  ResultFuture<SystemProcess> getProcessInfo(int pid);
  ResultVoid changeProcessPriority(int pid, int priority);
  
  // Service Management
  ResultVoid startService(String serviceName);
  ResultVoid stopService(String serviceName);
  ResultVoid restartService(String serviceName);
  ResultVoid enableService(String serviceName);
  ResultVoid disableService(String serviceName);
  ResultFuture<ServiceState> getServiceState(String serviceName);
  
  // File System Operations
  ResultVoid mountPartition(String partition, String mountPoint);
  ResultVoid unmountPartition(String partition);
  ResultVoid remountPartition(String partition, bool readOnly);
  ResultVoid changeFilePermissions(String path, String permissions);
  ResultVoid changeFileOwnership(String path, String owner, String group);
  ResultFuture<String> getFilePermissions(String path);
  ResultFuture<Map<String, String>> getFileOwnership(String path);
  
  // System Modifications
  ResultVoid modifySystemProperty(String property, String value);
  ResultVoid modifyBuildProp(Map<String, String> properties);
  ResultVoid installSystemApp(String apkPath);
  ResultVoid uninstallSystemApp(String packageName);
  ResultVoid freezeApp(String packageName);
  ResultVoid unfreezeApp(String packageName);
  
  // Backup and Restore
  ResultFuture<SystemBackup> createSystemBackup(
    String name,
    BackupType type,
    List<String> paths,
  );
  ResultVoid restoreSystemBackup(String backupId);
  ResultFuture<List<SystemBackup>> getAvailableBackups();
  ResultVoid deleteBackup(String backupId);
  
  // Boot Management
  ResultFuture<Map<String, dynamic>> getBootInfo();
  ResultVoid flashRecovery(String recoveryPath);
  ResultVoid flashKernel(String kernelPath);
  ResultVoid rebootToRecovery();
  ResultVoid rebootToBootloader();
  ResultVoid rebootToDownloadMode();
  
  // Advanced Tools
  ResultFuture<List<String>> getInstalledModules();
  ResultVoid installModule(String modulePath);
  ResultVoid uninstallModule(String moduleId);
  ResultVoid enableModule(String moduleId);
  ResultVoid disableModule(String moduleId);
  
  // System Tweaks
  ResultVoid enableDeveloperOptions();
  ResultVoid enableAdbDebugging();
  ResultVoid setAnimationScale(double scale);
  ResultVoid setDpi(int dpi);
  ResultVoid setGovernor(String governor);
  ResultVoid setCpuFrequency(int minFreq, int maxFreq);
  
  // Security and Privacy
  ResultVoid removeSystemApps(List<String> packageNames);
  ResultVoid disableSystemApps(List<String> packageNames);
  ResultVoid blockAds();
  ResultVoid unblockAds();
  ResultVoid enableFirewall();
  ResultVoid disableFirewall();
  ResultFuture<List<String>> getFirewallRules();
  ResultVoid addFirewallRule(String rule);
  ResultVoid removeFirewallRule(String rule);
  
  // Performance Optimization
  ResultVoid clearSystemCache();
  ResultVoid optimizeDatabase();
  ResultVoid defragmentStorage();
  ResultVoid trimFilesystem();
  ResultVoid optimizeMemory();
  
  // Monitoring
  Stream<Map<String, dynamic>> getSystemStats();
  Stream<List<SystemProcess>> getProcessUpdates();
  Stream<Map<String, double>> getCpuUsage();
  Stream<Map<String, int>> getMemoryUsage();
  Stream<Map<String, int>> getNetworkUsage();
  
  // Logs and Diagnostics
  ResultFuture<List<String>> getSystemLogs();
  ResultFuture<List<String>> getKernelLogs();
  ResultFuture<List<String>> getApplicationLogs();
  ResultVoid clearLogs();
  ResultFuture<Map<String, dynamic>> runSystemDiagnostics();
  
  // Recovery and Repair
  ResultVoid fixPermissions();
  ResultVoid rebuildDalvikCache();
  ResultVoid clearDalvikCache();
  ResultVoid fixBootloop();
  ResultVoid repairFilesystem(String partition);
  
  // Custom Scripts
  ResultFuture<List<String>> getAvailableScripts();
  ResultFuture<RootCommandResult> executeScript(String scriptPath);
  ResultVoid installScript(String scriptPath, String name);
  ResultVoid uninstallScript(String scriptName);
  
  // System Information Export
  ResultFuture<String> exportSystemInfo();
  ResultFuture<String> exportInstalledApps();
  ResultFuture<String> exportSystemLogs();
  ResultFuture<String> generateSystemReport();
}
