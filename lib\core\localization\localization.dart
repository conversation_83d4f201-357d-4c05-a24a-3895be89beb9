// Localization System Export File
// This file exports all the public APIs of the localization system

import 'package:flutter/material.dart';

// Models
export 'models/language.dart';

// Services
export 'services/translation_service.dart';

// BLoC
export 'bloc/language_bloc.dart';
export 'bloc/language_event.dart';
export 'bloc/language_state.dart';

// Extensions
export 'extensions/translation_extensions.dart';

// Widgets
export 'widgets/language_selector.dart';

// Pages
export 'pages/language_settings_page.dart';

// Import the models for use in this file
import 'models/language.dart';

/// Localization System Configuration
class LocalizationConfig {
  static const String name = 'localization';
  static const String displayName = 'نظام الترجمة';
  static const String description = 'نظام شامل لدعم اللغات المتعددة والترجمة';
  static const String version = '1.0.0';

  // Supported features
  static const List<String> features = [
    'multi_language_support',
    'rtl_support',
    'dynamic_language_switching',
    'translation_caching',
    'custom_translations',
    'language_detection',
    'font_optimization',
    'layout_mirroring',
    'number_formatting',
    'date_formatting',
    'pluralization',
    'parameter_substitution',
    'translation_validation',
    'export_import',
  ];

  // Default settings
  static const Map<String, dynamic> defaultSettings = {
    'autoDetectLanguage': false,
    'showNativeNames': true,
    'enableRTLSupport': true,
    'cacheTranslations': true,
    'validateTranslations': true,
    'enableFontOptimization': true,
    'enableLayoutMirroring': true,
    'enableNumberFormatting': true,
    'enableDateFormatting': true,
    'enablePluralization': true,
    'maxCacheSize': 1000,
    'cacheExpirationHours': 24,
  };

  // Performance settings
  static const Map<String, dynamic> performanceSettings = {
    'lazyLoadTranslations': true,
    'preloadPrimaryLanguages': true,
    'enableTranslationCompression': false,
    'maxMemoryUsageMB': 50,
    'translationBatchSize': 100,
    'cacheCleanupIntervalMinutes': 30,
  };

  // Accessibility settings
  static const Map<String, dynamic> accessibilitySettings = {
    'enableScreenReaderSupport': true,
    'enableHighContrastMode': false,
    'enableLargeTextMode': false,
    'enableVoiceNavigation': false,
    'enableGestureNavigation': true,
  };

  // Security settings
  static const Map<String, dynamic> securitySettings = {
    'validateTranslationSources': true,
    'enableTranslationEncryption': false,
    'allowCustomTranslations': true,
    'enableTranslationAudit': false,
    'maxTranslationLength': 10000,
  };

  // Debug settings
  static const Map<String, dynamic> debugSettings = {
    'showTranslationKeys': false,
    'showMissingTranslations': true,
    'logTranslationUsage': false,
    'enableTranslationMetrics': false,
    'showLanguageInfo': false,
  };

  // File paths
  static const Map<String, String> paths = {
    'translations': 'assets/translations',
    'fonts': 'assets/fonts',
    'cache': 'cache/translations',
    'logs': 'logs/translations',
    'exports': 'exports/translations',
  };

  // File extensions
  static const Map<String, String> extensions = {
    'translation': '.json',
    'font': '.ttf',
    'cache': '.cache',
    'log': '.log',
    'export': '.json',
  };

  // Validation rules
  static const Map<String, dynamic> validationRules = {
    'maxKeyLength': 100,
    'maxValueLength': 1000,
    'allowedKeyCharacters': r'^[a-zA-Z0-9._-]+$',
    'requiredKeys': ['app.name', 'common.ok', 'common.cancel'],
    'forbiddenKeys': ['password', 'secret', 'token'],
  };

  // Font configurations - Arabic and English only
  static const Map<String, Map<String, dynamic>> fontConfigs = {
    'ar': {
      'family': 'system',
      'size': 14.0,
      'weight': 'normal',
      'height': 1.5,
    }, // Using system font for Arabic
    'en': {
      'family': 'system',
      'size': 14.0,
      'weight': 'normal',
      'height': 1.4,
    }, // Using system font for English
  };

  // Layout configurations
  static const Map<String, Map<String, dynamic>> layoutConfigs = {
    'rtl': {
      'textDirection': 'rtl',
      'textAlign': 'right',
      'iconDirection': 'reversed',
      'paddingDirection': 'reversed',
    },
    'ltr': {
      'textDirection': 'ltr',
      'textAlign': 'left',
      'iconDirection': 'normal',
      'paddingDirection': 'normal',
    },
  };

  // Number formatting configurations
  static const Map<String, Map<String, dynamic>> numberConfigs = {
    'ar': {
      'useArabicDigits': false,
      'decimalSeparator': '.',
      'thousandsSeparator': ',',
      'currencySymbol': 'ر.س',
      'currencyPosition': 'after',
    },
    'en': {
      'useArabicDigits': false,
      'decimalSeparator': '.',
      'thousandsSeparator': ',',
      'currencySymbol': '\$',
      'currencyPosition': 'before',
    },
  };

  // Date formatting configurations
  static const Map<String, Map<String, dynamic>> dateConfigs = {
    'ar': {
      'dateFormat': 'dd/MM/yyyy',
      'timeFormat': 'HH:mm',
      'dateTimeFormat': 'dd/MM/yyyy HH:mm',
      'use24Hour': true,
      'firstDayOfWeek': 6, // Saturday
    },
    'en': {
      'dateFormat': 'MM/dd/yyyy',
      'timeFormat': 'hh:mm a',
      'dateTimeFormat': 'MM/dd/yyyy hh:mm a',
      'use24Hour': false,
      'firstDayOfWeek': 0, // Sunday
    },
  };

  // Pluralization rules
  static const Map<String, List<String>> pluralizationRules = {
    'ar': ['zero', 'one', 'two', 'few', 'many', 'other'],
    'en': ['one', 'other'],
    'ru': ['one', 'few', 'many', 'other'],
    'pl': ['one', 'few', 'many', 'other'],
  };

  // System initialization
  static Future<bool> initialize() async {
    try {
      // Initialize translation service
      // Load default language
      // Set up caching
      // Configure fonts
      return true;
    } catch (e) {
      return false;
    }
  }

  // System cleanup
  static Future<void> cleanup() async {
    try {
      // Clear caches
      // Save settings
      // Close resources
    } catch (e) {
      // Handle cleanup errors
    }
  }

  // Check system health
  static Future<Map<String, dynamic>> checkHealth() async {
    return {
      'status': 'healthy',
      'translationsLoaded': true,
      'cacheSize': 0,
      'memoryUsage': 0,
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }

  // Get system statistics
  static Future<Map<String, dynamic>> getStatistics() async {
    return {
      'supportedLanguages': SupportedLanguages.all.length,
      'loadedTranslations': 0,
      'cacheHitRate': 0.0,
      'averageTranslationTime': 0.0,
      'totalTranslations': 0,
      'missingTranslations': 0,
    };
  }
}

/// Localization utilities
class LocalizationUtils {
  /// Get system locale
  static String getSystemLocale() {
    return SupportedLanguages.getSystemLanguage().locale.toString();
  }

  /// Check if locale is supported
  static bool isLocaleSupported(String locale) {
    return SupportedLanguages.supportedLocales.any(
      (l) => l.toString() == locale,
    );
  }

  /// Get best matching language
  static Language getBestMatchingLanguage(String locale) {
    // Try exact match first
    Language? language = SupportedLanguages.findByLocale(
      Locale(
        locale.split('_')[0],
        locale.split('_').length > 1 ? locale.split('_')[1] : null,
      ),
    );

    // Try language code only
    language ??= SupportedLanguages.findByCode(locale.split('_')[0]);

    // Return default if no match
    return language ?? SupportedLanguages.defaultLanguage;
  }

  /// Validate translation key
  static bool isValidTranslationKey(String key) {
    if (key.isEmpty ||
        key.length > LocalizationConfig.validationRules['maxKeyLength']) {
      return false;
    }

    final regex = RegExp(
      LocalizationConfig.validationRules['allowedKeyCharacters'],
    );
    return regex.hasMatch(key);
  }

  /// Validate translation value
  static bool isValidTranslationValue(String value) {
    return value.length <= LocalizationConfig.validationRules['maxValueLength'];
  }

  /// Generate translation report
  static Map<String, dynamic> generateReport(
    Map<String, dynamic> translations,
  ) {
    final keys = _getAllKeys(translations);

    return {
      'totalKeys': keys.length,
      'totalCharacters': keys.fold<int>(0, (sum, key) => sum + key.length),
      'averageKeyLength':
          keys.isEmpty
              ? 0
              : keys.fold<int>(0, (sum, key) => sum + key.length) / keys.length,
      'longestKey':
          keys.isEmpty
              ? ''
              : keys.reduce((a, b) => a.length > b.length ? a : b),
      'shortestKey':
          keys.isEmpty
              ? ''
              : keys.reduce((a, b) => a.length < b.length ? a : b),
      'topLevelGroups': translations.keys.toList(),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// Get all translation keys recursively
  static List<String> _getAllKeys(
    Map<String, dynamic> map, [
    String prefix = '',
  ]) {
    final List<String> keys = [];

    map.forEach((key, value) {
      final fullKey = prefix.isEmpty ? key : '$prefix.$key';

      if (value is Map<String, dynamic>) {
        keys.addAll(_getAllKeys(value, fullKey));
      } else if (value is String) {
        keys.add(fullKey);
      }
    });

    return keys;
  }
}
