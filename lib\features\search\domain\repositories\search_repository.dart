import '../../../../core/utils/typedef.dart';
import '../entities/search_result.dart';
import '../entities/search_filter.dart';

abstract class SearchRepository {
  // File Search
  ResultFuture<SearchResult> searchFiles(SearchFilter filter);
  Stream<SearchResult> searchFilesStream(SearchFilter filter);
  ResultVoid cancelSearch();
  
  // Duplicate Search
  ResultFuture<DuplicateSearchResult> findDuplicates(DuplicateSearchFilter filter);
  Stream<DuplicateSearchResult> findDuplicatesStream(DuplicateSearchFilter filter);
  ResultVoid cancelDuplicateSearch();
  
  // Search History
  ResultFuture<List<SearchFilter>> getSearchHistory();
  ResultVoid saveSearchToHistory(SearchFilter filter);
  ResultVoid clearSearchHistory();
  
  // Quick Search
  ResultFuture<List<String>> getSearchSuggestions(String query);
  ResultFuture<SearchResult> quickSearch(String query);
}
