import '../../../../core/utils/typedef.dart';
import '../entities/file_operation.dart';

abstract class FileOperationsRepository {
  // Basic Operations
  ResultFuture<String> copyFiles(List<String> sourcePaths, String destinationPath);
  ResultFuture<String> moveFiles(List<String> sourcePaths, String destinationPath);
  ResultFuture<String> deleteFiles(List<String> filePaths);
  
  // Compression Operations
  ResultFuture<String> compressFiles(
    List<String> sourcePaths,
    String destinationPath,
    CompressionOptions options,
  );
  ResultFuture<String> extractArchive(
    String archivePath,
    String destinationPath,
    String? password,
  );
  
  // Operation Management
  ResultFuture<List<FileOperation>> getActiveOperations();
  ResultFuture<FileOperation> getOperation(String operationId);
  ResultVoid pauseOperation(String operationId);
  ResultVoid resumeOperation(String operationId);
  ResultVoid cancelOperation(String operationId);
  
  // Progress Monitoring
  Stream<FileOperation> watchOperation(String operationId);
  Stream<List<FileOperation>> watchAllOperations();
  
  // Utilities
  ResultFuture<int> calculateTotalSize(List<String> paths);
  ResultFuture<bool> hasEnoughSpace(String destinationPath, int requiredBytes);
  ResultFuture<List<String>> getConflictingFiles(
    List<String> sourcePaths,
    String destinationPath,
  );
}
