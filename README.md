# hd_file_explore

A new Flutter project.

## Getting Started

# HD File Explorer 📁

مدير ملفات احترافي مطور بـ Flutter مشابه لتطبيق ES File Explorer مع ميزات متقدمة.

## ✨ الميزات الرئيسية

### 🏠 واجهة التحكم الرئيسية (Dashboard)
- عرض حالة التخزين مع رسوم بيانية تفاعلية
- اختصارات سريعة للوصول للصور، الفيديوهات، المستندات
- إحصائيات استخدام التخزين المصنفة
- عرض الملفات الحديثة

### 📂 مستعرض الملفات المتقدم
- تصفح ملفات النظام مع دعم الروت
- عرض متعدد الأنماط (قائمة، شبكة، تفاصيل)
- ترتيب متقدم حسب الاسم، الحجم، التاريخ، النوع
- دعم الملفات المخفية
- فتح الملفات مباشرة

### 🔧 عمليات الملفات
- نسخ، قص، لصق الملفات والمجلدات
- إعادة تسمية وحذف
- إنشاء مجلدات جديدة
- مشاركة الملفات

## 🏗️ البنية المعمارية

يستخدم المشروع **Clean Architecture** مع تقسيم واضح للطبقات:

```
lib/
├── core/                    # الأدوات والثوابت المشتركة
│   ├── constants/          # الثوابت
│   ├── error/              # معالجة الأخطاء
│   ├── theme/              # تصميم التطبيق
│   ├── utils/              # الأدوات المساعدة
│   └── di/                 # حقن التبعيات
├── features/               # الميزات الرئيسية
│   ├── dashboard/          # واجهة التحكم
│   ├── file_browser/       # مستعرض الملفات
│   ├── search/             # البحث
│   ├── network/            # الشبكة
│   ├── security/           # الأمان
│   ├── cleaner/            # التنظيف
│   ├── app_manager/        # مدير التطبيقات
│   └── root_tools/         # أدوات الروت
└── app.dart               # التطبيق الرئيسي
```

كل ميزة تحتوي على:
- **Data Layer**: النماذج، مصادر البيانات، المستودعات
- **Domain Layer**: الكيانات، حالات الاستخدام، مستودعات المجال
- **Presentation Layer**: BLoC، الصفحات، الويدجت

## 📦 المكتبات المستخدمة

### إدارة الحالة والبنية
- `flutter_bloc` - إدارة الحالة
- `injectable` & `get_it` - حقن التبعيات
- `dartz` - البرمجة الوظيفية
- `equatable` - مقارنة الكائنات

### إدارة الملفات والتخزين
- `path_provider` - مسارات النظام
- `file_picker` - اختيار الملفات
- `permission_handler` - إدارة الصلاحيات
- `open_file` - فتح الملفات
- `share_plus` - مشاركة الملفات

### الأمان والتشفير
- `flutter_secure_storage` - التخزين الآمن
- `local_auth` - المصادقة البيومترية
- `crypto` - التشفير

### الشبكة والاتصال
- `ftpconnect` - اتصال FTP
- `network_info_plus` - معلومات الشبكة
- `lan_scanner` - مسح الشبكة المحلية
- `http` - طلبات HTTP

### الرسوم البيانية والواجهة
- `fl_chart` - الرسوم البيانية
- `material_color_utilities` - ألوان Material 3

### أدوات النظام
- `device_apps` - إدارة التطبيقات
- `device_info_plus` - معلومات الجهاز
- `process_run` - تنفيذ أوامر النظام

## 🚀 التشغيل

### المتطلبات
- Flutter SDK 3.7.2 أو أحدث
- Dart SDK
- Android Studio أو VS Code

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd hd_file_explore
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **إنشاء ملفات التكوين**
```bash
dart run build_runner build
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## 🔧 التطوير

### إضافة ميزة جديدة

1. إنشاء مجلد الميزة في `lib/features/`
2. إنشاء الطبقات الثلاث (data, domain, presentation)
3. تسجيل التبعيات في `injection_container.dart`
4. تشغيل `build_runner` لإنشاء ملفات التكوين

### اختبار التطبيق

```bash
flutter test
```

## 📱 الميزات المستقبلية

- [ ] دعم كامل للروت
- [ ] كشف الملفات المكررة
- [ ] أدوات التنظيف المتقدمة
- [ ] مدير التطبيقات الكامل
- [ ] دعم الشبكات (LAN/FTP)
- [ ] أدوات الأمان والتشفير
- [ ] ضغط وفك الضغط
- [ ] البحث المتقدم

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

للأسئلة والاستفسارات، يرجى فتح issue في المشروع.

---

**تم التطوير بـ ❤️ باستخدام Flutter**
