import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

// Import the localization system
import '../lib/core/localization/localization.dart';

/// مثال شامل لاستخدام نظام الترجمة
/// Complete example of using the localization system
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize the localization system
  await LocalizationConfig.initialize();
  
  runApp(const LocalizationExampleApp());
}

class LocalizationExampleApp extends StatelessWidget {
  const LocalizationExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LanguageBloc>(
      create: (context) => GetIt.instance<LanguageBloc>()
        ..add(const LoadSavedLanguageEvent()),
      child: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, state) {
          Language currentLanguage = SupportedLanguages.defaultLanguage;
          
          if (state is LanguageLoaded) {
            currentLanguage = state.language;
          } else if (state is LanguageChanged) {
            currentLanguage = state.currentLanguage;
          }

          return MaterialApp(
            title: 'Localization Example',
            locale: currentLanguage.locale,
            supportedLocales: SupportedLanguages.supportedLocales,
            builder: (context, child) {
              return Directionality(
                textDirection: currentLanguage.isRTL 
                    ? TextDirection.rtl 
                    : TextDirection.ltr,
                child: child!,
              );
            },
            home: const ExampleHomePage(),
          );
        },
      ),
    );
  }
}

class ExampleHomePage extends StatefulWidget {
  const ExampleHomePage({super.key});

  @override
  State<ExampleHomePage> createState() => _ExampleHomePageState();
}

class _ExampleHomePageState extends State<ExampleHomePage> {
  int _fileCount = 5;
  String _userName = 'أحمد';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('app.name')),
        actions: [
          // Language selector in app bar
          const LanguageSelectorAppBar(),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _navigateToLanguageSettings(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic translation example
            _buildSection(
              title: 'Basic Translation',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(context.tr('common.hello')),
                  Text(context.tr('common.welcome')),
                  Text(context.tr('dashboard.title')),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Translation with parameters
            _buildSection(
              title: 'Translation with Parameters',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(context.tr('common.welcome_user', params: {'name': _userName})),
                  Text(context.tr('dashboard.storage_usage', params: {'used': '50', 'total': '100'})),
                  TextField(
                    decoration: InputDecoration(
                      labelText: context.tr('common.name'),
                      hintText: context.tr('common.enter_name'),
                    ),
                    onChanged: (value) => setState(() => _userName = value),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Plural translations
            _buildSection(
              title: 'Plural Translations',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(context.trPlural('common.files', _fileCount)),
                  Text(context.trPlural('common.folders', _fileCount)),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () => setState(() => _fileCount--),
                        child: const Text('-'),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text('$_fileCount'),
                      ),
                      ElevatedButton(
                        onPressed: () => setState(() => _fileCount++),
                        child: const Text('+'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Language selector
            _buildSection(
              title: 'Language Selector',
              child: const LanguageSelector(
                showFlags: true,
                showNativeNames: true,
                isCompact: false,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // RTL/LTR demonstration
            _buildSection(
              title: 'RTL/LTR Layout',
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.arrow_forward).forRTL(context),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          context.tr('common.direction_demo'),
                          textAlign: TextAlign.start.forRTL(context),
                        ),
                      ),
                      const Icon(Icons.arrow_back).forRTL(context),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16).forRTL(context),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      context.tr('common.rtl_demo'),
                      textAlign: TextAlign.start.forRTL(context),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Number and date formatting
            _buildSection(
              title: 'Formatting Examples',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Number: ${TranslationHelpers.formatNumber(context, 1234567)}'),
                  Text('Date: ${TranslationHelpers.formatDate(context, DateTime.now())}'),
                  Text('Time: ${TranslationHelpers.formatTime(context, DateTime.now())}'),
                  Text('File Size: ${TranslationHelpers.formatFileSize(context, 1024 * 1024)}'),
                  Text('Day: ${TranslationHelpers.getDayName(context, DateTime.now().weekday)}'),
                  Text('Month: ${TranslationHelpers.getMonthName(context, DateTime.now().month)}'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Language information
            _buildSection(
              title: 'Current Language Info',
              child: const LanguageInfo(),
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            _buildSection(
              title: 'Actions',
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _reloadTranslations(),
                      icon: const Icon(Icons.refresh),
                      label: Text(context.tr('settings.reload_translations')),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _exportTranslations(),
                      icon: const Icon(Icons.download),
                      label: Text(context.tr('settings.export_translations')),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _resetToDefault(),
                      icon: const Icon(Icons.restore),
                      label: Text(context.tr('settings.reset_to_default')),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showLanguageDialog(),
        child: const Icon(Icons.language),
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  void _navigateToLanguageSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LanguageSettingsPage(),
      ),
    );
  }

  void _reloadTranslations() {
    context.read<LanguageBloc>().add(const ReloadTranslationsEvent());
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('settings.translations_reloaded')),
      ),
    );
  }

  void _exportTranslations() {
    context.read<LanguageBloc>().add(const ExportTranslationsEvent());
  }

  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('settings.reset_to_default')),
        content: Text(context.tr('settings.reset_confirmation')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<LanguageBloc>().add(const ResetToDefaultLanguageEvent());
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.reset')),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('settings.language')),
        content: SizedBox(
          width: double.maxFinite,
          child: const LanguageSelector(
            showFlags: true,
            showNativeNames: true,
            isCompact: false,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('common.close')),
          ),
        ],
      ),
    );
  }
}
