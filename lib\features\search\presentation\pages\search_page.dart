import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/search_bloc.dart';
import '../bloc/search_event.dart';
import '../bloc/search_state.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/search_filters_widget.dart';
import '../widgets/search_results_widget.dart';
import '../widgets/duplicate_search_widget.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/injection_container.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => sl<SearchBloc>()..add(const LoadSearchHistoryEvent()),
      child: const SearchView(),
    );
  }
}

class SearchView extends StatelessWidget {
  const SearchView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Search'),
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(Icons.search), text: 'File Search'),
              Tab(icon: Icon(Icons.content_copy), text: 'Find Duplicates'),
            ],
          ),
        ),
        body: BlocConsumer<SearchBloc, SearchState>(
          listener: (context, state) {
            if (state is SearchError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is DuplicateSearchError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is SearchCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Found ${state.result.totalResults} files in ${state.result.searchDuration.inSeconds}s',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is DuplicateSearchCompleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Found ${state.result.duplicateGroups.length} duplicate groups',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is DuplicateFilesDeleted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Deleted ${state.deletedFiles.length} duplicate files',
                  ),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            }
          },
          builder: (context, state) {
            return const TabBarView(
              children: [FileSearchTab(), DuplicateSearchTab()],
            );
          },
        ),
      ),
    );
  }
}

class FileSearchTab extends StatelessWidget {
  const FileSearchTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Bar
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const SearchBarWidget(),
        ),

        // Search Filters (Expandable)
        const SearchFiltersWidget(),

        // Search Results
        const Expanded(child: SearchResultsWidget()),
      ],
    );
  }
}

class DuplicateSearchTab extends StatelessWidget {
  const DuplicateSearchTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const DuplicateSearchWidget();
  }
}

class EmptySearchState extends StatelessWidget {
  const EmptySearchState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Search for Files',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Enter a search term to find files and folders',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Quick search suggestions
          Wrap(
            spacing: AppConstants.smallPadding,
            runSpacing: AppConstants.smallPadding,
            children: [
              _buildQuickSearchChip(context, 'Images', Icons.image),
              _buildQuickSearchChip(context, 'Videos', Icons.video_library),
              _buildQuickSearchChip(context, 'Documents', Icons.description),
              _buildQuickSearchChip(context, 'Music', Icons.music_note),
              _buildQuickSearchChip(context, 'Downloads', Icons.download),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSearchChip(
    BuildContext context,
    String label,
    IconData icon,
  ) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(label),
      onPressed: () {
        // TODO: Implement quick search
        context.read<SearchBloc>().add(QuickSearchEvent(label.toLowerCase()));
      },
    );
  }
}
