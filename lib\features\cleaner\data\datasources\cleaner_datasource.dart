import 'dart:io';
import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;

import '../models/cleanup_item_model.dart';
import '../../domain/entities/cleanup_item.dart';
import '../../../../core/error/exceptions.dart';

abstract class CleanerDataSource {
  Future<SystemAnalysisModel> analyzeSystem();
  Stream<SystemAnalysisModel> analyzeSystemStream();
  Future<void> cancelAnalysis();

  Future<CleanupResultModel> performCleanup(List<CleanupItemModel> items);
  Stream<CleanupResultModel> performCleanupStream(List<CleanupItemModel> items);
  Future<void> cancelCleanup();

  Future<List<CleanupItemModel>> findCacheFiles();
  Future<List<CleanupItemModel>> findTemporaryFiles();
  Future<List<CleanupItemModel>> findLogFiles();
  Future<List<CleanupItemModel>> findThumbnails();
  Future<List<CleanupItemModel>> findEmptyFolders();
  Future<List<CleanupItemModel>> findLargeFiles(int minSizeMB);
  Future<List<CleanupItemModel>> findOldFiles(int daysOld);
  Future<List<CleanupItemModel>> findApkFiles();
  Future<List<CleanupItemModel>> findResidualFiles();

  Future<Map<String, int>> analyzeLargestDirectories();
  Future<Map<String, int>> analyzeFileTypeDistribution();
  Future<int> calculateDirectorySize(String path);

  Future<List<CleanupResultModel>> getCleanupHistory();
  Future<void> saveCleanupResult(CleanupResultModel result);
  Future<void> clearCleanupHistory();
}

@LazySingleton(as: CleanerDataSource)
class CleanerDataSourceImpl implements CleanerDataSource {
  final Uuid _uuid = const Uuid();
  StreamController<SystemAnalysisModel>? _analysisController;
  StreamController<CleanupResultModel>? _cleanupController;
  bool _isAnalysisCancelled = false;
  bool _isCleanupCancelled = false;

  @override
  Future<SystemAnalysisModel> analyzeSystem() async {
    try {
      _isAnalysisCancelled = false;
      final startTime = DateTime.now();

      // Analyze storage
      final storageInfo = await _analyzeStorage();

      // Find cleanup opportunities
      final cleanupItems = <CleanupItemModel>[];

      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findCacheFiles());
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findTemporaryFiles());
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findLogFiles());
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findThumbnails());
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findEmptyFolders());
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findLargeFiles(100)); // Files > 100MB
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findOldFiles(30)); // Files > 30 days old
      }
      if (!_isAnalysisCancelled) {
        cleanupItems.addAll(await findApkFiles());
      }

      // Analyze directories and file types
      final largestDirectories = await analyzeLargestDirectories();
      final fileTypeDistribution = await analyzeFileTypeDistribution();

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // Calculate cleanup opportunities
      final cleanupOpportunities = <CleanupType, int>{};
      for (final item in cleanupItems) {
        cleanupOpportunities[item.type] =
            (cleanupOpportunities[item.type] ?? 0) + item.totalSize;
      }

      return SystemAnalysisModel(
        totalFiles: storageInfo['totalFiles'] ?? 0,
        totalFolders: storageInfo['totalFolders'] ?? 0,
        totalSize: storageInfo['totalSize'] ?? 0,
        availableSpace: storageInfo['availableSpace'] ?? 0,
        usedSpace: storageInfo['usedSpace'] ?? 0,
        cleanupOpportunities: cleanupOpportunities,
        cleanupItems: cleanupItems,
        largestDirectories: largestDirectories,
        fileTypeDistribution: fileTypeDistribution,
        analysisTime: endTime,
        analysisDuration: duration,
      );
    } catch (e) {
      throw ServerException(message: 'System analysis failed: $e');
    }
  }

  @override
  Stream<SystemAnalysisModel> analyzeSystemStream() async* {
    _analysisController = StreamController<SystemAnalysisModel>();
    _isAnalysisCancelled = false;

    try {
      final startTime = DateTime.now();
      final cleanupItems = <CleanupItemModel>[];

      // Emit initial state
      yield SystemAnalysisModel(
        totalFiles: 0,
        totalFolders: 0,
        totalSize: 0,
        availableSpace: 0,
        usedSpace: 0,
        cleanupOpportunities: const {},
        cleanupItems: const [],
        largestDirectories: const {},
        fileTypeDistribution: const {},
        analysisTime: startTime,
        analysisDuration: Duration.zero,
      );

      // Analyze storage
      final storageInfo = await _analyzeStorage();

      // Progressive analysis with updates
      final analysisSteps = [
        () => findCacheFiles(),
        () => findTemporaryFiles(),
        () => findLogFiles(),
        () => findThumbnails(),
        () => findEmptyFolders(),
        () => findLargeFiles(100),
        () => findOldFiles(30),
        () => findApkFiles(),
      ];

      for (int i = 0; i < analysisSteps.length; i++) {
        if (_isAnalysisCancelled) break;

        final stepItems = await analysisSteps[i]();
        cleanupItems.addAll(stepItems);

        final currentTime = DateTime.now();
        final duration = currentTime.difference(startTime);

        // Calculate cleanup opportunities
        final cleanupOpportunities = <CleanupType, int>{};
        for (final item in cleanupItems) {
          cleanupOpportunities[item.type] =
              (cleanupOpportunities[item.type] ?? 0) + item.totalSize;
        }

        yield SystemAnalysisModel(
          totalFiles: storageInfo['totalFiles'] ?? 0,
          totalFolders: storageInfo['totalFolders'] ?? 0,
          totalSize: storageInfo['totalSize'] ?? 0,
          availableSpace: storageInfo['availableSpace'] ?? 0,
          usedSpace: storageInfo['usedSpace'] ?? 0,
          cleanupOpportunities: cleanupOpportunities,
          cleanupItems: List.from(cleanupItems),
          largestDirectories: const {},
          fileTypeDistribution: const {},
          analysisTime: currentTime,
          analysisDuration: duration,
        );
      }

      // Final analysis with directory and file type data
      if (!_isAnalysisCancelled) {
        final largestDirectories = await analyzeLargestDirectories();
        final fileTypeDistribution = await analyzeFileTypeDistribution();

        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);

        final cleanupOpportunities = <CleanupType, int>{};
        for (final item in cleanupItems) {
          cleanupOpportunities[item.type] =
              (cleanupOpportunities[item.type] ?? 0) + item.totalSize;
        }

        yield SystemAnalysisModel(
          totalFiles: storageInfo['totalFiles'] ?? 0,
          totalFolders: storageInfo['totalFolders'] ?? 0,
          totalSize: storageInfo['totalSize'] ?? 0,
          availableSpace: storageInfo['availableSpace'] ?? 0,
          usedSpace: storageInfo['usedSpace'] ?? 0,
          cleanupOpportunities: cleanupOpportunities,
          cleanupItems: cleanupItems,
          largestDirectories: largestDirectories,
          fileTypeDistribution: fileTypeDistribution,
          analysisTime: endTime,
          analysisDuration: duration,
        );
      }
    } catch (e) {
      yield SystemAnalysisModel(
        totalFiles: 0,
        totalFolders: 0,
        totalSize: 0,
        availableSpace: 0,
        usedSpace: 0,
        cleanupOpportunities: const {},
        cleanupItems: const [],
        largestDirectories: const {},
        fileTypeDistribution: const {},
        analysisTime: DateTime.now(),
        analysisDuration: Duration.zero,
      );
    } finally {
      _analysisController?.close();
      _analysisController = null;
    }
  }

  Future<Map<String, int>> _analyzeStorage() async {
    try {
      // Mock storage analysis - in real implementation, use platform-specific APIs
      return {
        'totalFiles': 15000,
        'totalFolders': 2500,
        'totalSize': 32 * 1024 * 1024 * 1024, // 32GB
        'availableSpace': 8 * 1024 * 1024 * 1024, // 8GB
        'usedSpace': 24 * 1024 * 1024 * 1024, // 24GB
      };
    } catch (e) {
      return {
        'totalFiles': 0,
        'totalFolders': 0,
        'totalSize': 0,
        'availableSpace': 0,
        'usedSpace': 0,
      };
    }
  }

  @override
  Future<void> cancelAnalysis() async {
    _isAnalysisCancelled = true;
    _analysisController?.close();
    _analysisController = null;
  }

  @override
  Future<CleanupResultModel> performCleanup(
    List<CleanupItemModel> items,
  ) async {
    try {
      _isCleanupCancelled = false;
      final startTime = DateTime.now();

      final deletedFiles = <String>[];
      final failedDeletions = <String>[];
      int totalDeletedSize = 0;

      for (final item in items) {
        if (_isCleanupCancelled) break;

        for (final filePath in item.filePaths) {
          try {
            final file = File(filePath);
            if (await file.exists()) {
              final size = await file.length();
              await file.delete();
              deletedFiles.add(filePath);
              totalDeletedSize += size;
            }
          } catch (e) {
            failedDeletions.add(filePath);
          }
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return CleanupResultModel(
        deletedFiles: deletedFiles,
        totalDeletedSize: totalDeletedSize,
        totalDeletedCount: deletedFiles.length,
        failedDeletions: failedDeletions,
        cleanupDuration: duration,
        isComplete: !_isCleanupCancelled,
      );
    } catch (e) {
      throw ServerException(message: 'Cleanup failed: $e');
    }
  }

  @override
  Stream<CleanupResultModel> performCleanupStream(
    List<CleanupItemModel> items,
  ) async* {
    _cleanupController = StreamController<CleanupResultModel>();
    _isCleanupCancelled = false;

    try {
      final startTime = DateTime.now();
      final deletedFiles = <String>[];
      final failedDeletions = <String>[];
      int totalDeletedSize = 0;

      for (int i = 0; i < items.length; i++) {
        if (_isCleanupCancelled) break;

        final item = items[i];

        for (final filePath in item.filePaths) {
          if (_isCleanupCancelled) break;

          try {
            final file = File(filePath);
            if (await file.exists()) {
              final size = await file.length();
              await file.delete();
              deletedFiles.add(filePath);
              totalDeletedSize += size;
            }
          } catch (e) {
            failedDeletions.add(filePath);
          }
        }

        // Emit progress every item
        final currentTime = DateTime.now();
        final duration = currentTime.difference(startTime);

        yield CleanupResultModel(
          deletedFiles: List.from(deletedFiles),
          totalDeletedSize: totalDeletedSize,
          totalDeletedCount: deletedFiles.length,
          failedDeletions: List.from(failedDeletions),
          cleanupDuration: duration,
          isComplete: false,
        );
      }

      // Final result
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      yield CleanupResultModel(
        deletedFiles: deletedFiles,
        totalDeletedSize: totalDeletedSize,
        totalDeletedCount: deletedFiles.length,
        failedDeletions: failedDeletions,
        cleanupDuration: duration,
        isComplete: !_isCleanupCancelled,
      );
    } catch (e) {
      yield CleanupResultModel(
        deletedFiles: const [],
        totalDeletedSize: 0,
        totalDeletedCount: 0,
        failedDeletions: const [],
        cleanupDuration: Duration.zero,
        isComplete: false,
        errorMessage: 'Cleanup failed: $e',
      );
    } finally {
      _cleanupController?.close();
      _cleanupController = null;
    }
  }

  @override
  Future<void> cancelCleanup() async {
    _isCleanupCancelled = true;
    _cleanupController?.close();
    _cleanupController = null;
  }

  @override
  Future<List<CleanupItemModel>> findCacheFiles() async {
    try {
      final items = <CleanupItemModel>[];
      final cachePaths = [
        '/storage/emulated/0/Android/data',
        '/data/data',
        '/cache',
      ];

      for (final cachePath in cachePaths) {
        final directory = Directory(cachePath);
        if (await directory.exists()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File && entity.path.contains('cache')) {
              try {
                final stat = await entity.stat();
                items.add(
                  CleanupItemModel(
                    id: _uuid.v4(),
                    type: CleanupType.cache,
                    name: 'Cache Files',
                    description:
                        'Temporary cache files that can be safely deleted',
                    filePaths: [entity.path],
                    totalSize: stat.size,
                    fileCount: 1,
                    priority: CleanupPriority.high,
                    isSafeToDelete: true,
                    lastModified: stat.modified,
                  ),
                );
              } catch (e) {
                // Skip files that can't be accessed
              }
            }
          }
        }
      }

      return _groupSimilarItems(items, CleanupType.cache);
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<CleanupItemModel>> findTemporaryFiles() async {
    try {
      final items = <CleanupItemModel>[];
      final tempPaths = [
        '/tmp',
        '/storage/emulated/0/temp',
        '/storage/emulated/0/.temp',
      ];

      for (final tempPath in tempPaths) {
        final directory = Directory(tempPath);
        if (await directory.exists()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File) {
              try {
                final stat = await entity.stat();
                items.add(
                  CleanupItemModel(
                    id: _uuid.v4(),
                    type: CleanupType.temporaryFiles,
                    name: 'Temporary Files',
                    description: 'Temporary files that are no longer needed',
                    filePaths: [entity.path],
                    totalSize: stat.size,
                    fileCount: 1,
                    priority: CleanupPriority.high,
                    isSafeToDelete: true,
                    lastModified: stat.modified,
                  ),
                );
              } catch (e) {
                // Skip files that can't be accessed
              }
            }
          }
        }
      }

      return _groupSimilarItems(items, CleanupType.temporaryFiles);
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<CleanupItemModel>> findLogFiles() async {
    try {
      final items = <CleanupItemModel>[];
      final logPaths = ['/storage/emulated/0', '/var/log'];

      for (final logPath in logPaths) {
        final directory = Directory(logPath);
        if (await directory.exists()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File &&
                (entity.path.endsWith('.log') ||
                    entity.path.endsWith('.txt') &&
                        entity.path.contains('log'))) {
              try {
                final stat = await entity.stat();
                items.add(
                  CleanupItemModel(
                    id: _uuid.v4(),
                    type: CleanupType.logs,
                    name: 'Log Files',
                    description: 'Application log files',
                    filePaths: [entity.path],
                    totalSize: stat.size,
                    fileCount: 1,
                    priority: CleanupPriority.medium,
                    isSafeToDelete: true,
                    lastModified: stat.modified,
                  ),
                );
              } catch (e) {
                // Skip files that can't be accessed
              }
            }
          }
        }
      }

      return _groupSimilarItems(items, CleanupType.logs);
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<CleanupItemModel>> findThumbnails() async {
    try {
      final items = <CleanupItemModel>[];
      final thumbnailPaths = [
        '/storage/emulated/0/DCIM/.thumbnails',
        '/storage/emulated/0/Android/data/com.android.providers.media/cache',
      ];

      for (final thumbnailPath in thumbnailPaths) {
        final directory = Directory(thumbnailPath);
        if (await directory.exists()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File) {
              try {
                final stat = await entity.stat();
                items.add(
                  CleanupItemModel(
                    id: _uuid.v4(),
                    type: CleanupType.thumbnails,
                    name: 'Thumbnails',
                    description: 'Image and video thumbnails',
                    filePaths: [entity.path],
                    totalSize: stat.size,
                    fileCount: 1,
                    priority: CleanupPriority.medium,
                    isSafeToDelete: true,
                    lastModified: stat.modified,
                  ),
                );
              } catch (e) {
                // Skip files that can't be accessed
              }
            }
          }
        }
      }

      return _groupSimilarItems(items, CleanupType.thumbnails);
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<CleanupItemModel>> findEmptyFolders() async {
    try {
      final items = <CleanupItemModel>[];
      final searchPaths = ['/storage/emulated/0'];

      for (final searchPath in searchPaths) {
        await _findEmptyFoldersRecursive(searchPath, items);
      }

      return items;
    } catch (e) {
      return [];
    }
  }

  Future<void> _findEmptyFoldersRecursive(
    String dirPath,
    List<CleanupItemModel> items,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      final entities = await directory.list().toList();

      for (final entity in entities) {
        if (entity is Directory) {
          final subEntities = await entity.list().toList();
          if (subEntities.isEmpty) {
            final stat = await entity.stat();
            items.add(
              CleanupItemModel(
                id: _uuid.v4(),
                type: CleanupType.emptyFolders,
                name: 'Empty Folders',
                description: 'Empty directories that can be removed',
                filePaths: [entity.path],
                totalSize: 0,
                fileCount: 0,
                priority: CleanupPriority.low,
                isSafeToDelete: true,
                lastModified: stat.modified,
              ),
            );
          } else {
            await _findEmptyFoldersRecursive(entity.path, items);
          }
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  @override
  Future<List<CleanupItemModel>> findLargeFiles(int minSizeMB) async {
    try {
      final items = <CleanupItemModel>[];
      final minSizeBytes = minSizeMB * 1024 * 1024;
      final searchPaths = ['/storage/emulated/0'];

      for (final searchPath in searchPaths) {
        await _findLargeFilesRecursive(searchPath, minSizeBytes, items);
      }

      return items;
    } catch (e) {
      return [];
    }
  }

  Future<void> _findLargeFilesRecursive(
    String dirPath,
    int minSize,
    List<CleanupItemModel> items,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      await for (final entity in directory.list()) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            if (stat.size >= minSize) {
              items.add(
                CleanupItemModel(
                  id: _uuid.v4(),
                  type: CleanupType.largeFiles,
                  name: 'Large Files',
                  description:
                      'Files larger than ${minSize ~/ (1024 * 1024)}MB',
                  filePaths: [entity.path],
                  totalSize: stat.size,
                  fileCount: 1,
                  priority: CleanupPriority.low,
                  isSafeToDelete: false,
                  lastModified: stat.modified,
                ),
              );
            }
          } catch (e) {
            // Skip files that can't be accessed
          }
        } else if (entity is Directory) {
          await _findLargeFilesRecursive(entity.path, minSize, items);
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  @override
  Future<List<CleanupItemModel>> findOldFiles(int daysOld) async {
    try {
      final items = <CleanupItemModel>[];
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      final searchPaths = ['/storage/emulated/0/Download'];

      for (final searchPath in searchPaths) {
        await _findOldFilesRecursive(searchPath, cutoffDate, items);
      }

      return _groupSimilarItems(items, CleanupType.oldFiles);
    } catch (e) {
      return [];
    }
  }

  Future<void> _findOldFilesRecursive(
    String dirPath,
    DateTime cutoffDate,
    List<CleanupItemModel> items,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      await for (final entity in directory.list()) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              items.add(
                CleanupItemModel(
                  id: _uuid.v4(),
                  type: CleanupType.oldFiles,
                  name: 'Old Files',
                  description:
                      'Files older than ${cutoffDate.difference(stat.modified).inDays} days',
                  filePaths: [entity.path],
                  totalSize: stat.size,
                  fileCount: 1,
                  priority: CleanupPriority.medium,
                  isSafeToDelete: false,
                  lastModified: stat.modified,
                ),
              );
            }
          } catch (e) {
            // Skip files that can't be accessed
          }
        } else if (entity is Directory) {
          await _findOldFilesRecursive(entity.path, cutoffDate, items);
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  @override
  Future<List<CleanupItemModel>> findApkFiles() async {
    try {
      final items = <CleanupItemModel>[];
      final searchPaths = ['/storage/emulated/0'];

      for (final searchPath in searchPaths) {
        await _findApkFilesRecursive(searchPath, items);
      }

      return _groupSimilarItems(items, CleanupType.apkFiles);
    } catch (e) {
      return [];
    }
  }

  Future<void> _findApkFilesRecursive(
    String dirPath,
    List<CleanupItemModel> items,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      await for (final entity in directory.list()) {
        if (entity is File && entity.path.endsWith('.apk')) {
          try {
            final stat = await entity.stat();
            items.add(
              CleanupItemModel(
                id: _uuid.v4(),
                type: CleanupType.apkFiles,
                name: 'APK Files',
                description: 'Android application packages',
                filePaths: [entity.path],
                totalSize: stat.size,
                fileCount: 1,
                priority: CleanupPriority.low,
                isSafeToDelete: false,
                lastModified: stat.modified,
              ),
            );
          } catch (e) {
            // Skip files that can't be accessed
          }
        } else if (entity is Directory) {
          await _findApkFilesRecursive(entity.path, items);
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  @override
  Future<List<CleanupItemModel>> findResidualFiles() async {
    try {
      final items = <CleanupItemModel>[];
      final residualPaths = [
        '/storage/emulated/0/Android/data',
        '/storage/emulated/0/.android_secure',
      ];

      for (final residualPath in residualPaths) {
        final directory = Directory(residualPath);
        if (await directory.exists()) {
          await for (final entity in directory.list()) {
            if (entity is Directory) {
              // Check if this is a residual app folder
              final appName = path.basename(entity.path);
              if (appName.startsWith('com.') || appName.startsWith('org.')) {
                try {
                  final size = await calculateDirectorySize(entity.path);
                  final stat = await entity.stat();

                  items.add(
                    CleanupItemModel(
                      id: _uuid.v4(),
                      type: CleanupType.residualFiles,
                      name: 'Residual Files',
                      description: 'Leftover files from uninstalled apps',
                      filePaths: [entity.path],
                      totalSize: size,
                      fileCount: 1,
                      priority: CleanupPriority.medium,
                      isSafeToDelete: true,
                      lastModified: stat.modified,
                    ),
                  );
                } catch (e) {
                  // Skip directories that can't be accessed
                }
              }
            }
          }
        }
      }

      return items;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<Map<String, int>> analyzeLargestDirectories() async {
    try {
      final directories = <String, int>{};
      final searchPaths = [
        '/storage/emulated/0/DCIM',
        '/storage/emulated/0/Download',
        '/storage/emulated/0/Pictures',
        '/storage/emulated/0/Movies',
        '/storage/emulated/0/Music',
        '/storage/emulated/0/Documents',
        '/storage/emulated/0/Android/data',
      ];

      for (final searchPath in searchPaths) {
        try {
          final size = await calculateDirectorySize(searchPath);
          if (size > 0) {
            directories[searchPath] = size;
          }
        } catch (e) {
          // Skip directories that can't be accessed
        }
      }

      // Sort by size and return top 10
      final sortedEntries =
          directories.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      return Map.fromEntries(sortedEntries.take(10));
    } catch (e) {
      return {};
    }
  }

  @override
  Future<Map<String, int>> analyzeFileTypeDistribution() async {
    try {
      final fileTypes = <String, int>{};
      final searchPath = '/storage/emulated/0';

      await _analyzeFileTypesRecursive(searchPath, fileTypes);

      // Sort by count and return top 10
      final sortedEntries =
          fileTypes.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      return Map.fromEntries(sortedEntries.take(10));
    } catch (e) {
      return {};
    }
  }

  Future<void> _analyzeFileTypesRecursive(
    String dirPath,
    Map<String, int> fileTypes,
  ) async {
    try {
      final directory = Directory(dirPath);
      if (!await directory.exists()) return;

      await for (final entity in directory.list()) {
        if (entity is File) {
          final extension = path.extension(entity.path).toLowerCase();
          final fileType = extension.isNotEmpty ? extension : 'No extension';
          fileTypes[fileType] = (fileTypes[fileType] ?? 0) + 1;
        } else if (entity is Directory && !entity.path.contains('/.')) {
          await _analyzeFileTypesRecursive(entity.path, fileTypes);
        }
      }
    } catch (e) {
      // Skip directories that can't be accessed
    }
  }

  @override
  Future<int> calculateDirectorySize(String dirPath) async {
    try {
      int totalSize = 0;
      final directory = Directory(dirPath);

      if (!await directory.exists()) return 0;

      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            // Skip files that can't be accessed
          }
        }
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  @override
  Future<List<CleanupResultModel>> getCleanupHistory() async {
    // TODO: Implement persistent storage
    return [];
  }

  @override
  Future<void> saveCleanupResult(CleanupResultModel result) async {
    // TODO: Implement persistent storage
  }

  @override
  Future<void> clearCleanupHistory() async {
    // TODO: Implement persistent storage
  }

  List<CleanupItemModel> _groupSimilarItems(
    List<CleanupItemModel> items,
    CleanupType type,
  ) {
    if (items.isEmpty) return items;

    // Group items by type and combine similar ones
    final grouped = <String, List<CleanupItemModel>>{};

    for (final item in items) {
      final key = '${item.type}_${item.name}';
      grouped.putIfAbsent(key, () => []).add(item);
    }

    final result = <CleanupItemModel>[];

    for (final group in grouped.values) {
      if (group.length == 1) {
        result.add(group.first);
      } else {
        // Combine multiple items into one
        final allPaths = <String>[];
        int totalSize = 0;
        int totalCount = 0;
        DateTime latestModified = group.first.lastModified;

        for (final item in group) {
          allPaths.addAll(item.filePaths);
          totalSize += item.totalSize;
          totalCount += item.fileCount;
          if (item.lastModified.isAfter(latestModified)) {
            latestModified = item.lastModified;
          }
        }

        result.add(
          CleanupItemModel(
            id: _uuid.v4(),
            type: type,
            name: group.first.name,
            description: '${group.first.description} ($totalCount files)',
            filePaths: allPaths,
            totalSize: totalSize,
            fileCount: totalCount,
            priority: group.first.priority,
            isSafeToDelete: group.first.isSafeToDelete,
            lastModified: latestModified,
          ),
        );
      }
    }

    return result;
  }
}
