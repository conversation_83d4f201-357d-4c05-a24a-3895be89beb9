import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/language_bloc.dart';
import '../bloc/language_event.dart';
import '../bloc/language_state.dart';
import '../services/translation_service.dart';
import '../models/language.dart';

/// Extension لـ BuildContext لتسهيل الوصول للترجمات
extension TranslationContext on BuildContext {
  /// الحصول على خدمة الترجمة
  TranslationService get translationService {
    try {
      return read<TranslationService>();
    } catch (e) {
      throw Exception(
        'TranslationService not found in context. Make sure it\'s provided.',
      );
    }
  }

  /// الحصول على BLoC اللغة
  LanguageBloc get languageBloc {
    try {
      return read<LanguageBloc>();
    } catch (e) {
      throw Exception(
        'LanguageBloc not found in context. Make sure it\'s provided.',
      );
    }
  }

  /// الحصول على اللغة الحالية
  Language get currentLanguage {
    final state = languageBloc.state;
    if (state is LanguageLoaded) {
      return state.language;
    } else if (state is LanguageChanged) {
      return state.currentLanguage;
    }
    return SupportedLanguages.defaultLanguage;
  }

  /// التحقق من كون اللغة الحالية RTL
  bool get isRTL {
    return currentLanguage.isRTL;
  }

  /// الحصول على اتجاه النص
  TextDirection get textDirection {
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }

  /// الحصول على Locale الحالي
  Locale get currentLocale {
    return currentLanguage.locale;
  }

  /// ترجمة نص
  String tr(String key, {Map<String, dynamic>? params}) {
    return translationService.translate(key, params: params);
  }

  /// ترجمة مع صيغة الجمع
  String trPlural(String key, int count, {Map<String, dynamic>? params}) {
    return translationService.translatePlural(key, count, params: params);
  }

  /// التحقق من وجود ترجمة
  bool hasTranslation(String key) {
    return translationService.hasTranslation(key);
  }

  /// تغيير اللغة
  void changeLanguage(Language language) {
    read<LanguageBloc>().add(ChangeLanguageEvent(language));
  }

  /// إعادة تحميل الترجمات
  void reloadTranslations() {
    read<LanguageBloc>().add(const ReloadTranslationsEvent());
  }
}

/// Extension لـ String لتسهيل الترجمة
extension StringTranslation on String {
  /// ترجمة النص
  String tr(BuildContext context, {Map<String, dynamic>? params}) {
    return context.tr(this, params: params);
  }

  /// ترجمة مع صيغة الجمع
  String trPlural(
    BuildContext context,
    int count, {
    Map<String, dynamic>? params,
  }) {
    return context.trPlural(this, count, params: params);
  }

  /// التحقق من وجود ترجمة
  bool hasTranslation(BuildContext context) {
    return context.hasTranslation(this);
  }

  /// تحويل النص لاتجاه RTL إذا لزم الأمر
  String toRTL(BuildContext context) {
    if (context.isRTL) {
      return '\u202B$this\u202C'; // Right-to-Left Override
    }
    return this;
  }

  /// تحويل النص لاتجاه LTR إذا لزم الأمر
  String toLTR(BuildContext context) {
    if (!context.isRTL) {
      return '\u202A$this\u202C'; // Left-to-Right Override
    }
    return this;
  }

  /// إضافة علامات اتجاه النص التلقائية
  String withAutoDirection(BuildContext context) {
    if (context.isRTL) {
      return '\u202E$this\u202C'; // Right-to-Left Override
    } else {
      return '\u202D$this\u202C'; // Left-to-Right Override
    }
  }
}

/// Extension لـ Widget لدعم الترجمة
extension WidgetTranslation on Widget {
  /// تطبيق اتجاه النص على الويدجت
  Widget withDirection(BuildContext context) {
    return Directionality(textDirection: context.textDirection, child: this);
  }

  /// تطبيق Locale على الويدجت
  Widget withLocale(BuildContext context) {
    return Localizations(
      locale: context.currentLocale,
      delegates: const [
        // يمكن إضافة delegates هنا
      ],
      child: this,
    );
  }
}

/// Extension لـ TextStyle لدعم الخطوط العربية
extension TextStyleArabic on TextStyle {
  /// تطبيق خط عربي مناسب
  TextStyle withArabicFont(BuildContext context) {
    if (context.currentLanguage.code == 'ar') {
      return copyWith(
        fontFamily: 'Cairo',
        height: 1.5, // ارتفاع أكبر للنص العربي
      );
    }
    return this;
  }

  /// تطبيق خط مناسب للغة الحالية
  TextStyle withLanguageFont(BuildContext context) {
    switch (context.currentLanguage.code) {
      case 'ar':
        return copyWith(fontFamily: 'Cairo', height: 1.5);
      case 'fa':
        return copyWith(fontFamily: 'Vazir', height: 1.4);
      case 'ur':
        return copyWith(fontFamily: 'Noto Nastaliq Urdu', height: 1.6);
      case 'zh':
        return copyWith(fontFamily: 'Noto Sans CJK SC', height: 1.3);
      case 'ja':
        return copyWith(fontFamily: 'Noto Sans CJK JP', height: 1.3);
      case 'ko':
        return copyWith(fontFamily: 'Noto Sans CJK KR', height: 1.3);
      default:
        return this;
    }
  }

  /// تطبيق حجم خط مناسب للغة
  TextStyle withLanguageFontSize(BuildContext context) {
    switch (context.currentLanguage.code) {
      case 'ar':
      case 'fa':
      case 'ur':
        // الخطوط العربية تحتاج حجم أكبر قليلاً
        return copyWith(fontSize: (fontSize ?? 14) * 1.1);
      case 'zh':
      case 'ja':
      case 'ko':
        // الخطوط الآسيوية تحتاج حجم أصغر قليلاً
        return copyWith(fontSize: (fontSize ?? 14) * 0.95);
      default:
        return this;
    }
  }
}

/// Extension لـ EdgeInsets لدعم RTL
extension EdgeInsetsRTL on EdgeInsets {
  /// عكس الحواف للغات RTL
  EdgeInsets forRTL(BuildContext context) {
    if (context.isRTL) {
      return EdgeInsets.fromLTRB(right, top, left, bottom);
    }
    return this;
  }

  /// تطبيق حواف مناسبة للغة
  EdgeInsets forLanguage(BuildContext context) {
    if (context.isRTL) {
      return EdgeInsets.fromLTRB(right, top, left, bottom);
    }
    return this;
  }
}

/// Extension لـ Alignment لدعم RTL
extension AlignmentRTL on Alignment {
  /// عكس المحاذاة للغات RTL
  Alignment forRTL(BuildContext context) {
    if (context.isRTL) {
      return Alignment(-x, y);
    }
    return this;
  }
}

/// Extension لـ MainAxisAlignment لدعم RTL
extension MainAxisAlignmentRTL on MainAxisAlignment {
  /// عكس المحاذاة للغات RTL
  MainAxisAlignment forRTL(BuildContext context) {
    if (!context.isRTL) return this;

    switch (this) {
      case MainAxisAlignment.start:
        return MainAxisAlignment.end;
      case MainAxisAlignment.end:
        return MainAxisAlignment.start;
      default:
        return this;
    }
  }
}

/// Extension لـ CrossAxisAlignment لدعم RTL
extension CrossAxisAlignmentRTL on CrossAxisAlignment {
  /// عكس المحاذاة للغات RTL
  CrossAxisAlignment forRTL(BuildContext context) {
    if (!context.isRTL) return this;

    switch (this) {
      case CrossAxisAlignment.start:
        return CrossAxisAlignment.end;
      case CrossAxisAlignment.end:
        return CrossAxisAlignment.start;
      default:
        return this;
    }
  }
}

/// Extension لـ TextAlign لدعم RTL
extension TextAlignRTL on TextAlign {
  /// تطبيق محاذاة مناسبة للغة
  TextAlign forRTL(BuildContext context) {
    if (!context.isRTL) return this;

    switch (this) {
      case TextAlign.left:
        return TextAlign.right;
      case TextAlign.right:
        return TextAlign.left;
      case TextAlign.start:
        return TextAlign.end;
      case TextAlign.end:
        return TextAlign.start;
      default:
        return this;
    }
  }
}

/// Extension لـ IconData لدعم RTL
extension IconDataRTL on IconData {
  /// عكس الأيقونة للغات RTL إذا لزم الأمر
  IconData forRTL(BuildContext context) {
    if (!context.isRTL) return this;

    // قائمة الأيقونات التي تحتاج عكس
    final rtlIcons = <IconData, IconData>{
      Icons.arrow_forward: Icons.arrow_back,
      Icons.arrow_back: Icons.arrow_forward,
      Icons.arrow_forward_ios: Icons.arrow_back_ios,
      Icons.arrow_back_ios: Icons.arrow_forward_ios,
      Icons.chevron_right: Icons.chevron_left,
      Icons.chevron_left: Icons.chevron_right,
      Icons.navigate_next: Icons.navigate_before,
      Icons.navigate_before: Icons.navigate_next,
      Icons.first_page: Icons.last_page,
      Icons.last_page: Icons.first_page,
      Icons.keyboard_arrow_right: Icons.keyboard_arrow_left,
      Icons.keyboard_arrow_left: Icons.keyboard_arrow_right,
    };

    return rtlIcons[this] ?? this;
  }
}

/// مساعدات للترجمة
class TranslationHelpers {
  /// تنسيق الأرقام حسب اللغة
  static String formatNumber(BuildContext context, num number) {
    // يمكن استخدام NumberFormat هنا
    if (context.currentLanguage.code == 'ar') {
      // تحويل الأرقام للعربية إذا لزم الأمر
      return number.toString().replaceAllMapped(RegExp(r'[0-9]'), (match) {
        const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicDigits[int.parse(match.group(0)!)];
      });
    }

    return number.toString();
  }

  /// تنسيق التاريخ حسب اللغة
  static String formatDate(BuildContext context, DateTime date) {
    // يمكن استخدام DateFormat هنا
    if (context.currentLanguage.code == 'ar') {
      // تنسيق التاريخ بالعربية
      return '${date.day}/${date.month}/${date.year}';
    }

    return '${date.month}/${date.day}/${date.year}';
  }

  /// تنسيق الوقت حسب اللغة
  static String formatTime(BuildContext context, DateTime time) {
    if (context.currentLanguage.code == 'ar') {
      // تنسيق الوقت بالعربية (24 ساعة)
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }

    // تنسيق 12 ساعة للإنجليزية
    final hour = time.hour > 12 ? time.hour - 12 : time.hour;
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
  }

  /// الحصول على اسم اليوم بالترجمة
  static String getDayName(BuildContext context, int weekday) {
    final dayKeys = [
      'common.monday',
      'common.tuesday',
      'common.wednesday',
      'common.thursday',
      'common.friday',
      'common.saturday',
      'common.sunday',
    ];

    return context.tr(dayKeys[weekday - 1]);
  }

  /// الحصول على اسم الشهر بالترجمة
  static String getMonthName(BuildContext context, int month) {
    final monthKeys = [
      'common.january',
      'common.february',
      'common.march',
      'common.april',
      'common.may',
      'common.june',
      'common.july',
      'common.august',
      'common.september',
      'common.october',
      'common.november',
      'common.december',
    ];

    return context.tr(monthKeys[month - 1]);
  }

  /// تحويل حجم الملف للترجمة
  static String formatFileSize(BuildContext context, int bytes) {
    const suffixes = ['bytes', 'kb', 'mb', 'gb', 'tb'];
    int index = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && index < suffixes.length - 1) {
      size /= 1024;
      index++;
    }

    final sizeStr = size.toStringAsFixed(index == 0 ? 0 : 1);
    final suffix = context.tr('common.${suffixes[index]}');

    return '$sizeStr $suffix';
  }
}
