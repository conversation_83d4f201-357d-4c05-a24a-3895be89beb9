import 'dart:io';
import 'package:injectable/injectable.dart';
import '../models/file_item_model.dart';
import '../../../../core/error/exceptions.dart';

abstract class FileSystemDataSource {
  Future<List<FileItemModel>> getDirectoryContents(String path);
  Future<FileItemModel> getFileInfo(String path);
  Future<bool> createDirectory(String path);
  Future<bool> deleteFile(String path);
  Future<bool> deleteDirectory(String path);
  Future<bool> copyFile(String sourcePath, String destinationPath);
  Future<bool> moveFile(String sourcePath, String destinationPath);
  Future<bool> renameFile(String oldPath, String newPath);
  Future<List<String>> getRootDirectories();
  Future<bool> fileExists(String path);
  Future<bool> directoryExists(String path);
}

@LazySingleton(as: FileSystemDataSource)
class FileSystemDataSourceImpl implements FileSystemDataSource {
  @override
  Future<List<FileItemModel>> getDirectoryContents(String path) async {
    try {
      final directory = Directory(path);
      
      if (!await directory.exists()) {
        throw const FileSystemException(message: 'Directory does not exist');
      }

      final entities = await directory.list().toList();
      final fileItems = <FileItemModel>[];

      for (final entity in entities) {
        try {
          final fileItem = FileItemModel.fromFileSystemEntity(entity);
          fileItems.add(fileItem);
        } catch (e) {
          // Skip files that can't be accessed
          continue;
        }
      }

      // Sort: directories first, then files, both alphabetically
      fileItems.sort((a, b) {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.toLowerCase().compareTo(b.name.toLowerCase());
      });

      return fileItems;
    } catch (e) {
      throw FileSystemException(message: 'Failed to read directory: $e');
    }
  }

  @override
  Future<FileItemModel> getFileInfo(String path) async {
    try {
      final entity = await FileSystemEntity.type(path) == FileSystemEntityType.directory
          ? Directory(path)
          : File(path);

      if (!await entity.exists()) {
        throw const FileSystemException(message: 'File or directory does not exist');
      }

      return FileItemModel.fromFileSystemEntity(entity);
    } catch (e) {
      throw FileSystemException(message: 'Failed to get file info: $e');
    }
  }

  @override
  Future<bool> createDirectory(String path) async {
    try {
      final directory = Directory(path);
      await directory.create(recursive: true);
      return true;
    } catch (e) {
      throw FileSystemException(message: 'Failed to create directory: $e');
    }
  }

  @override
  Future<bool> deleteFile(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      throw FileSystemException(message: 'Failed to delete file: $e');
    }
  }

  @override
  Future<bool> deleteDirectory(String path) async {
    try {
      final directory = Directory(path);
      if (await directory.exists()) {
        await directory.delete(recursive: true);
        return true;
      }
      return false;
    } catch (e) {
      throw FileSystemException(message: 'Failed to delete directory: $e');
    }
  }

  @override
  Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw const FileSystemException(message: 'Source file does not exist');
      }

      await sourceFile.copy(destinationPath);
      return true;
    } catch (e) {
      throw FileSystemException(message: 'Failed to copy file: $e');
    }
  }

  @override
  Future<bool> moveFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw const FileSystemException(message: 'Source file does not exist');
      }

      await sourceFile.rename(destinationPath);
      return true;
    } catch (e) {
      throw FileSystemException(message: 'Failed to move file: $e');
    }
  }

  @override
  Future<bool> renameFile(String oldPath, String newPath) async {
    try {
      final entity = await FileSystemEntity.type(oldPath) == FileSystemEntityType.directory
          ? Directory(oldPath)
          : File(oldPath);

      if (!await entity.exists()) {
        throw const FileSystemException(message: 'File or directory does not exist');
      }

      await entity.rename(newPath);
      return true;
    } catch (e) {
      throw FileSystemException(message: 'Failed to rename file: $e');
    }
  }

  @override
  Future<List<String>> getRootDirectories() async {
    try {
      // Common Android root directories
      final rootDirs = <String>[
        '/storage/emulated/0', // Internal storage
        '/storage', // All storage
        '/sdcard', // SD card symlink
        '/system', // System directory (requires root)
        '/data', // Data directory (requires root)
      ];

      final existingDirs = <String>[];
      for (final dir in rootDirs) {
        if (await Directory(dir).exists()) {
          existingDirs.add(dir);
        }
      }

      return existingDirs;
    } catch (e) {
      throw FileSystemException(message: 'Failed to get root directories: $e');
    }
  }

  @override
  Future<bool> fileExists(String path) async {
    try {
      return await File(path).exists();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> directoryExists(String path) async {
    try {
      return await Directory(path).exists();
    } catch (e) {
      return false;
    }
  }
}
