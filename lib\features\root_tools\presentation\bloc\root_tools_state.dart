import 'package:equatable/equatable.dart';
import '../../domain/entities/root_access.dart';

abstract class RootToolsState extends Equatable {
  const RootToolsState();

  @override
  List<Object?> get props => [];
}

// Initial State
class RootToolsInitial extends RootToolsState {
  const RootToolsInitial();
}

// Loading States
class RootToolsLoading extends RootToolsState {
  final String? message;

  const RootToolsLoading({this.message});

  @override
  List<Object?> get props => [message];
}

class RootAccessLoading extends RootToolsState {
  const RootAccessLoading();
}

class CommandExecutionLoading extends RootToolsState {
  final String command;

  const CommandExecutionLoading(this.command);

  @override
  List<Object?> get props => [command];
}

class SystemOperationLoading extends RootToolsState {
  final String operation;

  const SystemOperationLoading(this.operation);

  @override
  List<Object?> get props => [operation];
}

// Success States
class RootAccessLoaded extends RootToolsState {
  final RootAccess rootAccess;

  const RootAccessLoaded(this.rootAccess);

  @override
  List<Object?> get props => [rootAccess];
}

class RootPermissionGranted extends RootToolsState {
  const RootPermissionGranted();
}

class RootPermissionRevoked extends RootToolsState {
  const RootPermissionRevoked();
}

class CommandExecutionSuccess extends RootToolsState {
  final RootCommandResult result;

  const CommandExecutionSuccess(this.result);

  @override
  List<Object?> get props => [result];
}

class BatchCommandsExecutionSuccess extends RootToolsState {
  final List<RootCommandResult> results;

  const BatchCommandsExecutionSuccess(this.results);

  @override
  List<Object?> get props => [results];
}

class CommandStreamStarted extends RootToolsState {
  final String command;

  const CommandStreamStarted(this.command);

  @override
  List<Object?> get props => [command];
}

class CommandStreamOutput extends RootToolsState {
  final String output;

  const CommandStreamOutput(this.output);

  @override
  List<Object?> get props => [output];
}

class CommandStreamStopped extends RootToolsState {
  const CommandStreamStopped();
}

// System Information States
class SystemPartitionsLoaded extends RootToolsState {
  final List<SystemPartition> partitions;

  const SystemPartitionsLoaded(this.partitions);

  @override
  List<Object?> get props => [partitions];
}

class RunningProcessesLoaded extends RootToolsState {
  final List<SystemProcess> processes;

  const RunningProcessesLoaded(this.processes);

  @override
  List<Object?> get props => [processes];
}

class SystemServicesLoaded extends RootToolsState {
  final List<SystemService> services;

  const SystemServicesLoaded(this.services);

  @override
  List<Object?> get props => [services];
}

class SystemPropertiesLoaded extends RootToolsState {
  final Map<String, String> properties;

  const SystemPropertiesLoaded(this.properties);

  @override
  List<Object?> get props => [properties];
}

class SystemInfoLoaded extends RootToolsState {
  final Map<String, dynamic> systemInfo;

  const SystemInfoLoaded(this.systemInfo);

  @override
  List<Object?> get props => [systemInfo];
}

// Process Management States
class ProcessKilled extends RootToolsState {
  final int pid;

  const ProcessKilled(this.pid);

  @override
  List<Object?> get props => [pid];
}

class ProcessKilledByName extends RootToolsState {
  final String processName;

  const ProcessKilledByName(this.processName);

  @override
  List<Object?> get props => [processName];
}

class ProcessPriorityChanged extends RootToolsState {
  final int pid;
  final int priority;

  const ProcessPriorityChanged(this.pid, this.priority);

  @override
  List<Object?> get props => [pid, priority];
}

// Service Management States
class ServiceStarted extends RootToolsState {
  final String serviceName;

  const ServiceStarted(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class ServiceStopped extends RootToolsState {
  final String serviceName;

  const ServiceStopped(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class ServiceRestarted extends RootToolsState {
  final String serviceName;

  const ServiceRestarted(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class ServiceEnabled extends RootToolsState {
  final String serviceName;

  const ServiceEnabled(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class ServiceDisabled extends RootToolsState {
  final String serviceName;

  const ServiceDisabled(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

// File System States
class PartitionMounted extends RootToolsState {
  final String partition;
  final String mountPoint;

  const PartitionMounted(this.partition, this.mountPoint);

  @override
  List<Object?> get props => [partition, mountPoint];
}

class PartitionUnmounted extends RootToolsState {
  final String partition;

  const PartitionUnmounted(this.partition);

  @override
  List<Object?> get props => [partition];
}

class PartitionRemounted extends RootToolsState {
  final String partition;
  final bool readOnly;

  const PartitionRemounted(this.partition, this.readOnly);

  @override
  List<Object?> get props => [partition, readOnly];
}

class FilePermissionsChanged extends RootToolsState {
  final String path;
  final String permissions;

  const FilePermissionsChanged(this.path, this.permissions);

  @override
  List<Object?> get props => [path, permissions];
}

class FileOwnershipChanged extends RootToolsState {
  final String path;
  final String owner;
  final String group;

  const FileOwnershipChanged(this.path, this.owner, this.group);

  @override
  List<Object?> get props => [path, owner, group];
}

// System Modifications States
class SystemPropertyModified extends RootToolsState {
  final String property;
  final String value;

  const SystemPropertyModified(this.property, this.value);

  @override
  List<Object?> get props => [property, value];
}

class BuildPropModified extends RootToolsState {
  final Map<String, String> properties;

  const BuildPropModified(this.properties);

  @override
  List<Object?> get props => [properties];
}

class SystemAppInstalled extends RootToolsState {
  final String apkPath;

  const SystemAppInstalled(this.apkPath);

  @override
  List<Object?> get props => [apkPath];
}

class SystemAppUninstalled extends RootToolsState {
  final String packageName;

  const SystemAppUninstalled(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class AppFrozen extends RootToolsState {
  final String packageName;

  const AppFrozen(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class AppUnfrozen extends RootToolsState {
  final String packageName;

  const AppUnfrozen(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

// Backup and Restore States
class SystemBackupCreated extends RootToolsState {
  final SystemBackup backup;

  const SystemBackupCreated(this.backup);

  @override
  List<Object?> get props => [backup];
}

class SystemBackupRestored extends RootToolsState {
  final String backupId;

  const SystemBackupRestored(this.backupId);

  @override
  List<Object?> get props => [backupId];
}

class AvailableBackupsLoaded extends RootToolsState {
  final List<SystemBackup> backups;

  const AvailableBackupsLoaded(this.backups);

  @override
  List<Object?> get props => [backups];
}

class BackupDeleted extends RootToolsState {
  final String backupId;

  const BackupDeleted(this.backupId);

  @override
  List<Object?> get props => [backupId];
}

// Boot Management States
class BootInfoLoaded extends RootToolsState {
  final Map<String, dynamic> bootInfo;

  const BootInfoLoaded(this.bootInfo);

  @override
  List<Object?> get props => [bootInfo];
}

class RecoveryFlashed extends RootToolsState {
  final String recoveryPath;

  const RecoveryFlashed(this.recoveryPath);

  @override
  List<Object?> get props => [recoveryPath];
}

class KernelFlashed extends RootToolsState {
  final String kernelPath;

  const KernelFlashed(this.kernelPath);

  @override
  List<Object?> get props => [kernelPath];
}

class RebootingToRecovery extends RootToolsState {
  const RebootingToRecovery();
}

class RebootingToBootloader extends RootToolsState {
  const RebootingToBootloader();
}

class RebootingToDownloadMode extends RootToolsState {
  const RebootingToDownloadMode();
}

// Advanced Tools States
class InstalledModulesLoaded extends RootToolsState {
  final List<String> modules;

  const InstalledModulesLoaded(this.modules);

  @override
  List<Object?> get props => [modules];
}

class ModuleInstalled extends RootToolsState {
  final String modulePath;

  const ModuleInstalled(this.modulePath);

  @override
  List<Object?> get props => [modulePath];
}

class ModuleUninstalled extends RootToolsState {
  final String moduleId;

  const ModuleUninstalled(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

class ModuleEnabled extends RootToolsState {
  final String moduleId;

  const ModuleEnabled(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

class ModuleDisabled extends RootToolsState {
  final String moduleId;

  const ModuleDisabled(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

// System Tweaks States
class DeveloperOptionsEnabled extends RootToolsState {
  const DeveloperOptionsEnabled();
}

class AdbDebuggingEnabled extends RootToolsState {
  const AdbDebuggingEnabled();
}

class AnimationScaleSet extends RootToolsState {
  final double scale;

  const AnimationScaleSet(this.scale);

  @override
  List<Object?> get props => [scale];
}

class DpiSet extends RootToolsState {
  final int dpi;

  const DpiSet(this.dpi);

  @override
  List<Object?> get props => [dpi];
}

class GovernorSet extends RootToolsState {
  final String governor;

  const GovernorSet(this.governor);

  @override
  List<Object?> get props => [governor];
}

class CpuFrequencySet extends RootToolsState {
  final int minFreq;
  final int maxFreq;

  const CpuFrequencySet(this.minFreq, this.maxFreq);

  @override
  List<Object?> get props => [minFreq, maxFreq];
}

// Security and Privacy States
class SystemAppsRemoved extends RootToolsState {
  final List<String> packageNames;

  const SystemAppsRemoved(this.packageNames);

  @override
  List<Object?> get props => [packageNames];
}

class SystemAppsDisabled extends RootToolsState {
  final List<String> packageNames;

  const SystemAppsDisabled(this.packageNames);

  @override
  List<Object?> get props => [packageNames];
}

class AdsBlocked extends RootToolsState {
  const AdsBlocked();
}

class AdsUnblocked extends RootToolsState {
  const AdsUnblocked();
}

class FirewallEnabled extends RootToolsState {
  const FirewallEnabled();
}

class FirewallDisabled extends RootToolsState {
  const FirewallDisabled();
}

class FirewallRulesLoaded extends RootToolsState {
  final List<String> rules;

  const FirewallRulesLoaded(this.rules);

  @override
  List<Object?> get props => [rules];
}

class FirewallRuleAdded extends RootToolsState {
  final String rule;

  const FirewallRuleAdded(this.rule);

  @override
  List<Object?> get props => [rule];
}

class FirewallRuleRemoved extends RootToolsState {
  final String rule;

  const FirewallRuleRemoved(this.rule);

  @override
  List<Object?> get props => [rule];
}

// Performance Optimization States
class SystemCacheCleared extends RootToolsState {
  const SystemCacheCleared();
}

class DatabaseOptimized extends RootToolsState {
  const DatabaseOptimized();
}

class StorageDefragmented extends RootToolsState {
  const StorageDefragmented();
}

class FilesystemTrimmed extends RootToolsState {
  const FilesystemTrimmed();
}

class MemoryOptimized extends RootToolsState {
  const MemoryOptimized();
}

// Monitoring States
class SystemMonitoringStarted extends RootToolsState {
  const SystemMonitoringStarted();
}

class SystemMonitoringStopped extends RootToolsState {
  const SystemMonitoringStopped();
}

class SystemStatsUpdated extends RootToolsState {
  final Map<String, dynamic> stats;

  const SystemStatsUpdated(this.stats);

  @override
  List<Object?> get props => [stats];
}

class ProcessMonitoringStarted extends RootToolsState {
  const ProcessMonitoringStarted();
}

class ProcessMonitoringStopped extends RootToolsState {
  const ProcessMonitoringStopped();
}

class ProcessesUpdated extends RootToolsState {
  final List<SystemProcess> processes;

  const ProcessesUpdated(this.processes);

  @override
  List<Object?> get props => [processes];
}

// Logs and Diagnostics States
class SystemLogsLoaded extends RootToolsState {
  final List<String> logs;

  const SystemLogsLoaded(this.logs);

  @override
  List<Object?> get props => [logs];
}

class KernelLogsLoaded extends RootToolsState {
  final List<String> logs;

  const KernelLogsLoaded(this.logs);

  @override
  List<Object?> get props => [logs];
}

class ApplicationLogsLoaded extends RootToolsState {
  final List<String> logs;

  const ApplicationLogsLoaded(this.logs);

  @override
  List<Object?> get props => [logs];
}

class LogsCleared extends RootToolsState {
  const LogsCleared();
}

class SystemDiagnosticsCompleted extends RootToolsState {
  final Map<String, dynamic> diagnostics;

  const SystemDiagnosticsCompleted(this.diagnostics);

  @override
  List<Object?> get props => [diagnostics];
}

// Recovery and Repair States
class PermissionsFixed extends RootToolsState {
  const PermissionsFixed();
}

class DalvikCacheRebuilt extends RootToolsState {
  const DalvikCacheRebuilt();
}

class DalvikCacheCleared extends RootToolsState {
  const DalvikCacheCleared();
}

class BootloopFixed extends RootToolsState {
  const BootloopFixed();
}

class FilesystemRepaired extends RootToolsState {
  final String partition;

  const FilesystemRepaired(this.partition);

  @override
  List<Object?> get props => [partition];
}

// Custom Scripts States
class AvailableScriptsLoaded extends RootToolsState {
  final List<String> scripts;

  const AvailableScriptsLoaded(this.scripts);

  @override
  List<Object?> get props => [scripts];
}

class ScriptExecuted extends RootToolsState {
  final String scriptPath;
  final RootCommandResult result;

  const ScriptExecuted(this.scriptPath, this.result);

  @override
  List<Object?> get props => [scriptPath, result];
}

class ScriptInstalled extends RootToolsState {
  final String scriptPath;
  final String name;

  const ScriptInstalled(this.scriptPath, this.name);

  @override
  List<Object?> get props => [scriptPath, name];
}

class ScriptUninstalled extends RootToolsState {
  final String scriptName;

  const ScriptUninstalled(this.scriptName);

  @override
  List<Object?> get props => [scriptName];
}

// System Information Export States
class SystemInfoExported extends RootToolsState {
  final String filePath;

  const SystemInfoExported(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

class InstalledAppsExported extends RootToolsState {
  final String filePath;

  const InstalledAppsExported(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

class SystemLogsExported extends RootToolsState {
  final String filePath;

  const SystemLogsExported(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

class SystemReportGenerated extends RootToolsState {
  final String filePath;

  const SystemReportGenerated(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

// Error States
class RootToolsError extends RootToolsState {
  final String message;
  final String? code;

  const RootToolsError(this.message, {this.code});

  @override
  List<Object?> get props => [message, code];
}

class RootAccessError extends RootToolsState {
  final String message;

  const RootAccessError(this.message);

  @override
  List<Object?> get props => [message];
}

class CommandExecutionError extends RootToolsState {
  final String command;
  final String message;

  const CommandExecutionError(this.command, this.message);

  @override
  List<Object?> get props => [command, message];
}

class SystemOperationError extends RootToolsState {
  final String operation;
  final String message;

  const SystemOperationError(this.operation, this.message);

  @override
  List<Object?> get props => [operation, message];
}
