import 'package:equatable/equatable.dart';

class FileOperation extends Equatable {
  final String id;
  final FileOperationType type;
  final List<String> sourcePaths;
  final String destinationPath;
  final FileOperationStatus status;
  final double progress;
  final DateTime startTime;
  final DateTime? endTime;
  final String? errorMessage;
  final int totalFiles;
  final int processedFiles;
  final int totalBytes;
  final int processedBytes;

  const FileOperation({
    required this.id,
    required this.type,
    required this.sourcePaths,
    required this.destinationPath,
    required this.status,
    this.progress = 0.0,
    required this.startTime,
    this.endTime,
    this.errorMessage,
    this.totalFiles = 0,
    this.processedFiles = 0,
    this.totalBytes = 0,
    this.processedBytes = 0,
  });

  Duration? get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    } else if (status == FileOperationStatus.inProgress) {
      return DateTime.now().difference(startTime);
    }
    return null;
  }

  String get speedText {
    final dur = duration;
    if (dur != null && dur.inSeconds > 0 && processedBytes > 0) {
      final bytesPerSecond = processedBytes / dur.inSeconds;
      return _formatBytes(bytesPerSecond.round()) + '/s';
    }
    return '';
  }

  String get remainingTimeText {
    if (progress > 0 && progress < 1 && status == FileOperationStatus.inProgress) {
      final dur = duration;
      if (dur != null) {
        final remainingSeconds = (dur.inSeconds / progress) - dur.inSeconds;
        return _formatDuration(Duration(seconds: remainingSeconds.round()));
      }
    }
    return '';
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  FileOperation copyWith({
    String? id,
    FileOperationType? type,
    List<String>? sourcePaths,
    String? destinationPath,
    FileOperationStatus? status,
    double? progress,
    DateTime? startTime,
    DateTime? endTime,
    String? errorMessage,
    int? totalFiles,
    int? processedFiles,
    int? totalBytes,
    int? processedBytes,
  }) {
    return FileOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      sourcePaths: sourcePaths ?? this.sourcePaths,
      destinationPath: destinationPath ?? this.destinationPath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      errorMessage: errorMessage ?? this.errorMessage,
      totalFiles: totalFiles ?? this.totalFiles,
      processedFiles: processedFiles ?? this.processedFiles,
      totalBytes: totalBytes ?? this.totalBytes,
      processedBytes: processedBytes ?? this.processedBytes,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        sourcePaths,
        destinationPath,
        status,
        progress,
        startTime,
        endTime,
        errorMessage,
        totalFiles,
        processedFiles,
        totalBytes,
        processedBytes,
      ];
}

enum FileOperationType {
  copy,
  move,
  delete,
  compress,
  extract,
}

enum FileOperationStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled,
  paused,
}

class CompressionOptions extends Equatable {
  final CompressionFormat format;
  final CompressionLevel level;
  final String? password;
  final bool includeSubfolders;

  const CompressionOptions({
    required this.format,
    this.level = CompressionLevel.normal,
    this.password,
    this.includeSubfolders = true,
  });

  @override
  List<Object?> get props => [format, level, password, includeSubfolders];
}

enum CompressionFormat {
  zip,
  tar,
  gzip,
  sevenZip,
}

enum CompressionLevel {
  store,
  fastest,
  fast,
  normal,
  maximum,
  ultra,
}
