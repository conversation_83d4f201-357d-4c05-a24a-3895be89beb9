import 'package:equatable/equatable.dart';

/// Root access status and information
class RootAccess extends Equatable {
  final bool isRooted;
  final bool isSuAvailable;
  final bool isBusyBoxAvailable;
  final String? suVersion;
  final String? busyBoxVersion;
  final RootMethod rootMethod;
  final List<String> availableCommands;
  final Map<String, String> systemProperties;
  final DateTime lastChecked;

  const RootAccess({
    required this.isRooted,
    required this.isSuAvailable,
    required this.isBusyBoxAvailable,
    this.suVersion,
    this.busyBoxVersion,
    required this.rootMethod,
    required this.availableCommands,
    required this.systemProperties,
    required this.lastChecked,
  });

  bool get hasFullAccess => isRooted && isSuAvailable;
  bool get hasAdvancedTools => isBusyBoxAvailable;

  // Additional getters for compatibility
  bool get isGranted => isRooted && isSuAvailable;
  RootMethod get rootType => rootMethod;
  String? get version => suVersion;
  String? get binaryPath => systemProperties['su.binary.path'];

  @override
  List<Object?> get props => [
    isRooted,
    isSuAvailable,
    isBusyBoxAvailable,
    suVersion,
    busyBoxVersion,
    rootMethod,
    availableCommands,
    systemProperties,
    lastChecked,
  ];
}

enum RootMethod { none, magisk, supersu, kingroot, custom, unknown }

/// System partition information
class SystemPartition extends Equatable {
  final String name;
  final String mountPoint;
  final String fileSystem;
  final int totalSize;
  final int usedSize;
  final int availableSize;
  final bool isReadOnly;
  final bool isMounted;
  final List<String> mountOptions;

  const SystemPartition({
    required this.name,
    required this.mountPoint,
    required this.fileSystem,
    required this.totalSize,
    required this.usedSize,
    required this.availableSize,
    required this.isReadOnly,
    required this.isMounted,
    required this.mountOptions,
  });

  double get usagePercentage =>
      totalSize > 0 ? (usedSize / totalSize) * 100 : 0;

  @override
  List<Object?> get props => [
    name,
    mountPoint,
    fileSystem,
    totalSize,
    usedSize,
    availableSize,
    isReadOnly,
    isMounted,
    mountOptions,
  ];
}

/// System process information
class SystemProcess extends Equatable {
  final int pid;
  final int ppid;
  final String name;
  final String command;
  final String user;
  final double cpuUsage;
  final int memoryUsage;
  final ProcessState state;
  final DateTime startTime;
  final List<String> openFiles;

  const SystemProcess({
    required this.pid,
    required this.ppid,
    required this.name,
    required this.command,
    required this.user,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.state,
    required this.startTime,
    required this.openFiles,
  });

  @override
  List<Object?> get props => [
    pid,
    ppid,
    name,
    command,
    user,
    cpuUsage,
    memoryUsage,
    state,
    startTime,
    openFiles,
  ];
}

enum ProcessState { running, sleeping, stopped, zombie, unknown }

/// System service information
class SystemService extends Equatable {
  final String name;
  final String description;
  final ServiceState state;
  final bool isEnabled;
  final bool isSystemService;
  final String? packageName;
  final List<String> dependencies;
  final Map<String, dynamic> properties;

  const SystemService({
    required this.name,
    required this.description,
    required this.state,
    required this.isEnabled,
    required this.isSystemService,
    this.packageName,
    required this.dependencies,
    required this.properties,
  });

  @override
  List<Object?> get props => [
    name,
    description,
    state,
    isEnabled,
    isSystemService,
    packageName,
    dependencies,
    properties,
  ];
}

enum ServiceState { active, inactive, failed, unknown }

/// Root command execution result
class RootCommandResult extends Equatable {
  final String command;
  final int exitCode;
  final String output;
  final String error;
  final Duration executionTime;
  final DateTime timestamp;
  final bool isSuccess;

  const RootCommandResult({
    required this.command,
    required this.exitCode,
    required this.output,
    required this.error,
    required this.executionTime,
    required this.timestamp,
    required this.isSuccess,
  });

  @override
  List<Object?> get props => [
    command,
    exitCode,
    output,
    error,
    executionTime,
    timestamp,
    isSuccess,
  ];
}

/// System backup information
class SystemBackup extends Equatable {
  final String id;
  final String name;
  final BackupType type;
  final List<String> includedPaths;
  final List<String> excludedPaths;
  final int size;
  final DateTime createdAt;
  final String location;
  final bool isCompressed;
  final String? checksum;
  final Map<String, dynamic> metadata;

  const SystemBackup({
    required this.id,
    required this.name,
    required this.type,
    required this.includedPaths,
    required this.excludedPaths,
    required this.size,
    required this.createdAt,
    required this.location,
    required this.isCompressed,
    this.checksum,
    required this.metadata,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    type,
    includedPaths,
    excludedPaths,
    size,
    createdAt,
    location,
    isCompressed,
    checksum,
    metadata,
  ];
}

enum BackupType { full, system, data, boot, recovery, custom }

/// System modification record
class SystemModification extends Equatable {
  final String id;
  final ModificationType type;
  final String description;
  final String targetPath;
  final String? originalValue;
  final String? newValue;
  final DateTime timestamp;
  final bool isReversible;
  final String? backupLocation;

  const SystemModification({
    required this.id,
    required this.type,
    required this.description,
    required this.targetPath,
    this.originalValue,
    this.newValue,
    required this.timestamp,
    required this.isReversible,
    this.backupLocation,
  });

  @override
  List<Object?> get props => [
    id,
    type,
    description,
    targetPath,
    originalValue,
    newValue,
    timestamp,
    isReversible,
    backupLocation,
  ];
}

enum ModificationType {
  fileEdit,
  permissionChange,
  propertyChange,
  serviceModification,
  systemAppRemoval,
  kernelModule,
  other,
}
