import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart';
import '../constants/app_constants.dart';

class FileUtils {
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }
  
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }
  
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }
  
  static String getDirectoryPath(String filePath) {
    return path.dirname(filePath);
  }
  
  static String joinPaths(String path1, String path2) {
    return path.join(path1, path2);
  }
  
  static bool isImage(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.imageExtensions.contains(extension);
  }
  
  static bool isVideo(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.videoExtensions.contains(extension);
  }
  
  static bool isAudio(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.audioExtensions.contains(extension);
  }
  
  static bool isDocument(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.documentExtensions.contains(extension);
  }
  
  static bool isArchive(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.archiveExtensions.contains(extension);
  }
  
  static bool isApk(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.apkExtensions.contains(extension);
  }
  
  static FileType getFileType(String filePath) {
    if (isImage(filePath)) return FileType.image;
    if (isVideo(filePath)) return FileType.video;
    if (isAudio(filePath)) return FileType.audio;
    if (isDocument(filePath)) return FileType.document;
    if (isArchive(filePath)) return FileType.archive;
    if (isApk(filePath)) return FileType.apk;
    return FileType.other;
  }
  
  static String? getMimeType(String filePath) {
    return lookupMimeType(filePath);
  }
  
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    int i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }
  
  static bool isHidden(String filePath) {
    final fileName = getFileName(filePath);
    return fileName.startsWith(AppConstants.hiddenPrefix);
  }
  
  static bool isSystemFile(String filePath) {
    return filePath.startsWith(AppConstants.systemPath) ||
           filePath.startsWith(AppConstants.dataPath);
  }
  
  static bool isValidFileName(String fileName) {
    if (fileName.isEmpty || fileName.length > AppConstants.maxFileNameLength) {
      return false;
    }
    
    // Check for invalid characters
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    return !invalidChars.hasMatch(fileName);
  }
  
  static String sanitizeFileName(String fileName) {
    // Remove invalid characters
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    String sanitized = fileName.replaceAll(invalidChars, '_');
    
    // Trim whitespace and dots
    sanitized = sanitized.trim().replaceAll(RegExp(r'^\.+|\.+$'), '');
    
    // Ensure it's not empty
    if (sanitized.isEmpty) {
      sanitized = 'unnamed_file';
    }
    
    // Truncate if too long
    if (sanitized.length > AppConstants.maxFileNameLength) {
      sanitized = sanitized.substring(0, AppConstants.maxFileNameLength);
    }
    
    return sanitized;
  }
  
  static Future<bool> fileExists(String filePath) async {
    return await File(filePath).exists();
  }
  
  static Future<bool> directoryExists(String dirPath) async {
    return await Directory(dirPath).exists();
  }
  
  static Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      return await file.length();
    } catch (e) {
      return 0;
    }
  }
  
  static Future<DateTime?> getFileModifiedDate(String filePath) async {
    try {
      final file = File(filePath);
      final stat = await file.stat();
      return stat.modified;
    } catch (e) {
      return null;
    }
  }
}

enum FileType {
  image,
  video,
  audio,
  document,
  archive,
  apk,
  other,
}
