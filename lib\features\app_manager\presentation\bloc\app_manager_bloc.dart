import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../domain/usecases/get_all_apps.dart';
import '../../domain/usecases/manage_app.dart';
import '../../domain/usecases/batch_operations.dart';
import '../../domain/repositories/app_manager_repository.dart';
import '../../domain/entities/app_info.dart';
import 'app_manager_event.dart';
import 'app_manager_state.dart';

@injectable
class AppManagerBloc extends Bloc<AppManagerEvent, AppManagerState> {
  final GetAllApps _getAllApps;
  final ManageApp _manageApp;
  final BatchOperations _batchOperations;
  final AppManagerRepository _repository;
  
  StreamSubscription? _batchOperationSubscription;
  StreamSubscription? _installationSubscription;

  AppManagerBloc(
    this._getAllApps,
    this._manageApp,
    this._batchOperations,
    this._repository,
  ) : super(const AppManagerInitial()) {
    on<LoadAllAppsEvent>(_onLoadAllApps);
    on<LoadUserAppsEvent>(_onLoadUserApps);
    on<LoadSystemAppsEvent>(_onLoadSystemApps);
    on<LoadRunningAppsEvent>(_onLoadRunningApps);
    on<RefreshAppsEvent>(_onRefreshApps);
    on<SearchAppsEvent>(_onSearchApps);
    on<FilterAppsEvent>(_onFilterApps);
    on<SortAppsEvent>(_onSortApps);
    on<UninstallAppEvent>(_onUninstallApp);
    on<DisableAppEvent>(_onDisableApp);
    on<EnableAppEvent>(_onEnableApp);
    on<ClearAppDataEvent>(_onClearAppData);
    on<ClearAppCacheEvent>(_onClearAppCache);
    on<ForceStopAppEvent>(_onForceStopApp);
    on<LaunchAppEvent>(_onLaunchApp);
    on<StartBatchOperationEvent>(_onStartBatchOperation);
    on<CancelBatchOperationEvent>(_onCancelBatchOperation);
    on<LoadAppUsageStatsEvent>(_onLoadAppUsageStats);
    on<LoadAppAnalysisEvent>(_onLoadAppAnalysis);
    on<SelectAppEvent>(_onSelectApp);
    on<SelectAllAppsEvent>(_onSelectAllApps);
    on<InstallApkEvent>(_onInstallApk);
    on<BackupAppEvent>(_onBackupApp);
    on<RestoreAppEvent>(_onRestoreApp);
  }

  @override
  Future<void> close() {
    _batchOperationSubscription?.cancel();
    _installationSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLoadAllApps(
    LoadAllAppsEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(const AppManagerLoading());
    
    final result = await _getAllApps();
    
    result.fold(
      (failure) => emit(AppManagerError(failure.message)),
      (apps) => emit(AppsLoaded(
        apps: apps,
        filteredApps: apps,
      )),
    );
  }

  Future<void> _onLoadUserApps(
    LoadUserAppsEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(const AppManagerLoading());
    
    final result = await _repository.getUserApps();
    
    result.fold(
      (failure) => emit(AppManagerError(failure.message)),
      (apps) => emit(AppsLoaded(
        apps: apps,
        filteredApps: apps,
        filterType: AppType.user,
      )),
    );
  }

  Future<void> _onLoadSystemApps(
    LoadSystemAppsEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(const AppManagerLoading());
    
    final result = await _repository.getSystemApps();
    
    result.fold(
      (failure) => emit(AppManagerError(failure.message)),
      (apps) => emit(AppsLoaded(
        apps: apps,
        filteredApps: apps,
        filterType: AppType.system,
      )),
    );
  }

  Future<void> _onLoadRunningApps(
    LoadRunningAppsEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(const AppManagerLoading());
    
    final result = await _repository.getRunningApps();
    
    result.fold(
      (failure) => emit(AppManagerError(failure.message)),
      (apps) => emit(AppsLoaded(
        apps: apps,
        filteredApps: apps,
        filterStatus: AppStatus.running,
      )),
    );
  }

  Future<void> _onRefreshApps(
    RefreshAppsEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    if (state is AppsLoaded) {
      final currentState = state as AppsLoaded;
      emit(const AppManagerLoading());
      
      final result = await _getAllApps();
      
      result.fold(
        (failure) => emit(AppManagerError(failure.message)),
        (apps) {
          final filteredApps = _applyFiltersAndSort(
            apps,
            currentState.searchQuery,
            currentState.filterType,
            currentState.filterStatus,
            currentState.filterEnabled,
            currentState.sortType,
            currentState.sortAscending,
          );
          
          emit(currentState.copyWith(
            apps: apps,
            filteredApps: filteredApps,
          ));
        },
      );
    } else {
      add(const LoadAllAppsEvent());
    }
  }

  void _onSearchApps(
    SearchAppsEvent event,
    Emitter<AppManagerState> emit,
  ) {
    if (state is AppsLoaded) {
      final currentState = state as AppsLoaded;
      
      final filteredApps = _applyFiltersAndSort(
        currentState.apps,
        event.query,
        currentState.filterType,
        currentState.filterStatus,
        currentState.filterEnabled,
        currentState.sortType,
        currentState.sortAscending,
      );
      
      emit(currentState.copyWith(
        searchQuery: event.query,
        filteredApps: filteredApps,
      ));
    }
  }

  void _onFilterApps(
    FilterAppsEvent event,
    Emitter<AppManagerState> emit,
  ) {
    if (state is AppsLoaded) {
      final currentState = state as AppsLoaded;
      
      final filteredApps = _applyFiltersAndSort(
        currentState.apps,
        currentState.searchQuery,
        event.type,
        event.status,
        event.isEnabled,
        currentState.sortType,
        currentState.sortAscending,
      );
      
      emit(currentState.copyWith(
        filterType: event.type,
        filterStatus: event.status,
        filterEnabled: event.isEnabled,
        filteredApps: filteredApps,
      ));
    }
  }

  void _onSortApps(
    SortAppsEvent event,
    Emitter<AppManagerState> emit,
  ) {
    if (state is AppsLoaded) {
      final currentState = state as AppsLoaded;
      
      final filteredApps = _applyFiltersAndSort(
        currentState.apps,
        currentState.searchQuery,
        currentState.filterType,
        currentState.filterStatus,
        currentState.filterEnabled,
        event.sortType,
        event.ascending,
      );
      
      emit(currentState.copyWith(
        sortType: event.sortType,
        sortAscending: event.ascending,
        filteredApps: filteredApps,
      ));
    }
  }

  Future<void> _onUninstallApp(
    UninstallAppEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Uninstalling',
    ));
    
    final result = await _manageApp.uninstall(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Uninstall',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Uninstall',
          message: 'App uninstalled successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onDisableApp(
    DisableAppEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Disabling',
    ));
    
    final result = await _manageApp.disable(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Disable',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Disable',
          message: 'App disabled successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onEnableApp(
    EnableAppEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Enabling',
    ));
    
    final result = await _manageApp.enable(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Enable',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Enable',
          message: 'App enabled successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onClearAppData(
    ClearAppDataEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Clearing data',
    ));
    
    final result = await _manageApp.clearData(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Clear data',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Clear data',
          message: 'App data cleared successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onClearAppCache(
    ClearAppCacheEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Clearing cache',
    ));
    
    final result = await _manageApp.clearCache(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Clear cache',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Clear cache',
          message: 'App cache cleared successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onForceStopApp(
    ForceStopAppEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    emit(AppOperationLoading(
      packageName: event.packageName,
      operation: 'Force stopping',
    ));
    
    final result = await _manageApp.forceStop(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Force stop',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Force stop',
          message: 'App stopped successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }

  Future<void> _onLaunchApp(
    LaunchAppEvent event,
    Emitter<AppManagerState> emit,
  ) async {
    final result = await _manageApp.launch(event.packageName);
    
    result.fold(
      (failure) => emit(AppOperationError(
        packageName: event.packageName,
        operation: 'Launch',
        message: failure.message,
      )),
      (_) {
        emit(AppOperationSuccess(
          packageName: event.packageName,
          operation: 'Launch',
          message: 'App launched successfully',
        ));
        add(const RefreshAppsEvent());
      },
    );
  }
