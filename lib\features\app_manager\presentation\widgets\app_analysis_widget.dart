import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';

import '../bloc/app_manager_bloc.dart';
import '../bloc/app_manager_state.dart';
import '../../domain/entities/app_info.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class AppAnalysisWidget extends StatelessWidget {
  const AppAnalysisWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppManagerBloc, AppManagerState>(
      builder: (context, state) {
        if (state is AppManagerLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: AppConstants.defaultPadding),
                Text('Analyzing apps...'),
              ],
            ),
          );
        }
        
        if (state is AppAnalysisLoaded) {
          return _buildAnalysisContent(context, state);
        }
        
        if (state is AppManagerError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Error Loading Analysis',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        }
        
        return const Center(
          child: Text('No analysis data available'),
        );
      },
    );
  }

  Widget _buildAnalysisContent(BuildContext context, AppAnalysisLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Storage Analysis Chart
          _buildStorageAnalysisCard(context, state.sizeAnalysis),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Largest Apps
          _buildLargestAppsCard(context, state.largestApps),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Unused Apps
          _buildUnusedAppsCard(context, state.unusedApps),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Quick Stats
          _buildQuickStatsCard(context, state),
        ],
      ),
    );
  }

  Widget _buildStorageAnalysisCard(BuildContext context, Map<String, int> sizeAnalysis) {
    if (sizeAnalysis.isEmpty) {
      return const SizedBox.shrink();
    }

    final sortedApps = sizeAnalysis.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final topApps = sortedApps.take(10).toList();
    final totalSize = sizeAnalysis.values.fold(0, (sum, size) => sum + size);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Storage Usage Analysis',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Pie Chart
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: topApps.asMap().entries.map((entry) {
                    final index = entry.key;
                    final app = entry.value;
                    final percentage = (app.value / totalSize) * 100;
                    
                    return PieChartSectionData(
                      color: _getChartColor(index),
                      value: percentage,
                      title: '${percentage.toStringAsFixed(1)}%',
                      radius: 60,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Legend
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: topApps.asMap().entries.map((entry) {
                final index = entry.key;
                final app = entry.value;
                
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: _getChartColor(index),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${app.key} (${FileUtils.formatFileSize(app.value)})',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLargestAppsCard(BuildContext context, List<AppInfo> largestApps) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Largest Apps',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            if (largestApps.isEmpty)
              const Center(
                child: Text('No apps found'),
              )
            else
              ...largestApps.take(10).map((app) => Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                      ),
                      child: Icon(
                        Icons.apps,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            app.appName,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            app.packageName,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Text(
                      FileUtils.formatFileSize(app.totalSize),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildUnusedAppsCard(BuildContext context, List<AppInfo> unusedApps) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Unused Apps (30+ days)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            if (unusedApps.isEmpty)
              const Center(
                child: Text('All apps have been used recently'),
              )
            else
              ...unusedApps.take(10).map((app) => Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.warningColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                      ),
                      child: Icon(
                        Icons.schedule,
                        color: AppTheme.warningColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            app.appName,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Last used: ${_formatDate(app.lastUsedDate)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      FileUtils.formatFileSize(app.totalSize),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.warningColor,
                      ),
                    ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard(BuildContext context, AppAnalysisLoaded state) {
    final totalApps = state.largestApps.length;
    final totalSize = state.sizeAnalysis.values.fold(0, (sum, size) => sum + size);
    final unusedCount = state.unusedApps.length;
    final averageSize = totalApps > 0 ? totalSize / totalApps : 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Stats',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Apps',
                    totalApps.toString(),
                    Icons.apps,
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Size',
                    FileUtils.formatFileSize(totalSize),
                    Icons.storage,
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Unused Apps',
                    unusedCount.toString(),
                    Icons.schedule,
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Average Size',
                    FileUtils.formatFileSize(averageSize.round()),
                    Icons.analytics,
                    AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getChartColor(int index) {
    final colors = [
      AppTheme.primaryColor,
      AppTheme.successColor,
      AppTheme.warningColor,
      AppTheme.errorColor,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.brown,
    ];
    return colors[index % colors.length];
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else if (difference < 30) {
      final weeks = (difference / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else {
      final months = (difference / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    }
  }
}
