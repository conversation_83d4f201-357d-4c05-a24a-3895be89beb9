import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import '../bloc/root_tools_bloc.dart';
import '../bloc/root_tools_state.dart';
import '../bloc/root_tools_event.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/animated_card.dart';

class SystemMonitoringCard extends StatefulWidget {
  const SystemMonitoringCard({super.key});

  @override
  State<SystemMonitoringCard> createState() => _SystemMonitoringCardState();
}

class _SystemMonitoringCardState extends State<SystemMonitoringCard> {
  bool _isMonitoring = false;
  StreamSubscription? _monitoringSubscription;
  Map<String, dynamic>? _currentStats;

  @override
  void dispose() {
    _monitoringSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RootToolsBloc, RootToolsState>(
      listener: (context, state) {
        if (state is SystemMonitoringStarted) {
          setState(() {
            _isMonitoring = true;
          });
        } else if (state is SystemMonitoringStopped) {
          setState(() {
            _isMonitoring = false;
            _currentStats = null;
          });
        } else if (state is SystemStatsUpdated) {
          setState(() {
            _currentStats = state.stats;
          });
        }
      },
      child: AnimatedCard(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppColors.outline.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color:
                          _isMonitoring
                              ? Colors.green.withValues(alpha: 0.1)
                              : AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _isMonitoring ? Icons.monitor_heart : Icons.monitor,
                      color: _isMonitoring ? Colors.green : AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مراقبة النظام',
                          style: AppTextStyles.headlineSmall.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _isMonitoring ? 'المراقبة نشطة' : 'المراقبة متوقفة',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color:
                                _isMonitoring
                                    ? Colors.green
                                    : AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _isMonitoring,
                    onChanged: (value) {
                      if (value) {
                        context.read<RootToolsBloc>().add(
                          const StartSystemMonitoringEvent(),
                        );
                      } else {
                        context.read<RootToolsBloc>().add(
                          const StopSystemMonitoringEvent(),
                        );
                      }
                    },
                    activeColor: Colors.green,
                  ),
                ],
              ),

              const SizedBox(height: 20),

              if (_currentStats != null)
                _buildMonitoringData(_currentStats!)
              else if (_isMonitoring)
                _buildLoadingState()
              else
                _buildInactiveState(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMonitoringData(Map<String, dynamic> stats) {
    return Column(
      children: [
        // CPU Usage
        _buildStatItem(
          label: 'استخدام المعالج',
          value: '${(stats['cpu_usage'] ?? 0).toStringAsFixed(1)}%',
          percentage: (stats['cpu_usage'] ?? 0) / 100,
          color: _getUsageColor(stats['cpu_usage'] ?? 0),
          icon: Icons.developer_board,
        ),

        const SizedBox(height: 16),

        // Memory Usage
        _buildStatItem(
          label: 'استخدام الذاكرة',
          value: '${(stats['memory_usage'] ?? 0).toStringAsFixed(1)}%',
          percentage: (stats['memory_usage'] ?? 0) / 100,
          color: _getUsageColor(stats['memory_usage'] ?? 0),
          icon: Icons.memory,
        ),

        const SizedBox(height: 16),

        // Disk Usage
        _buildStatItem(
          label: 'استخدام التخزين',
          value: '${(stats['disk_usage'] ?? 0).toStringAsFixed(1)}%',
          percentage: (stats['disk_usage'] ?? 0) / 100,
          color: _getUsageColor(stats['disk_usage'] ?? 0),
          icon: Icons.storage,
        ),

        const SizedBox(height: 20),

        // Additional Stats Row
        Row(
          children: [
            Expanded(
              child: _buildMiniStat(
                label: 'درجة الحرارة',
                value: '${(stats['temperature'] ?? 0).toStringAsFixed(1)}°C',
                icon: Icons.thermostat,
                color: _getTemperatureColor(stats['temperature'] ?? 0),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMiniStat(
                label: 'البطارية',
                value: '${stats['battery_level'] ?? 0}%',
                icon: Icons.battery_full,
                color: _getBatteryColor(stats['battery_level'] ?? 0),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Network Stats
        Row(
          children: [
            Expanded(
              child: _buildMiniStat(
                label: 'التحميل',
                value: _formatNetworkSpeed(stats['network_rx'] ?? 0),
                icon: Icons.download,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMiniStat(
                label: 'الرفع',
                value: _formatNetworkSpeed(stats['network_tx'] ?? 0),
                icon: Icons.upload,
                color: Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    required double percentage,
    required Color color,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage.clamp(0.0, 1.0),
          backgroundColor: AppColors.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildMiniStat({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(40),
        child: Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات المراقبة...'),
          ],
        ),
      ),
    );
  }

  Widget _buildInactiveState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.monitor_outlined,
              color: AppColors.textSecondary,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'قم بتفعيل المراقبة لعرض إحصائيات النظام في الوقت الفعلي',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getUsageColor(double usage) {
    if (usage > 80) {
      return Colors.red;
    } else if (usage > 60) {
      return Colors.orange;
    } else if (usage > 40) {
      return Colors.yellow[700]!;
    } else {
      return Colors.green;
    }
  }

  Color _getTemperatureColor(double temperature) {
    if (temperature > 60) {
      return Colors.red;
    } else if (temperature > 45) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel < 20) {
      return Colors.red;
    } else if (batteryLevel < 50) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _formatNetworkSpeed(int bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '$bytesPerSecond B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }
}
