import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/cleanup_item.dart';
import '../repositories/cleaner_repository.dart';

@lazySingleton
class AnalyzeSystem {
  final CleanerRepository repository;

  const AnalyzeSystem(this.repository);

  ResultFuture<SystemAnalysis> call() async {
    return await repository.analyzeSystem();
  }

  Stream<SystemAnalysis> stream() {
    return repository.analyzeSystemStream();
  }
}
