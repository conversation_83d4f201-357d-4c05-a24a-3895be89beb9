import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/root_tools_bloc.dart';
import '../bloc/root_tools_state.dart';
import '../../domain/entities/root_access.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/animated_card.dart';

class RootAccessCard extends StatelessWidget {
  final VoidCallback onRequestRoot;
  final VoidCallback onRevokeRoot;

  const RootAccessCard({
    super.key,
    required this.onRequestRoot,
    required this.onRevokeRoot,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RootToolsBloc, RootToolsState>(
      builder: (context, state) {
        return AnimatedCard(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: _getGradient(state),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: _getStatusColor(state).withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getStatusIcon(state),
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حالة صلاحيات الروت',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getStatusText(state),
                            style: AppTextStyles.headlineSmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (state is RootAccessLoaded)
                      _buildStatusBadge(state.rootAccess),
                  ],
                ),
                
                if (state is RootAccessLoaded) ...[
                  const SizedBox(height: 20),
                  _buildRootDetails(state.rootAccess),
                ],
                
                const SizedBox(height: 20),
                _buildActionButtons(state),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusBadge(RootAccess rootAccess) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: rootAccess.isGranted ? Colors.green : Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            rootAccess.isGranted ? 'نشط' : 'غير نشط',
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRootDetails(RootAccess rootAccess) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildDetailRow(
            'نوع الروت',
            rootAccess.rootType.toString().split('.').last,
            Icons.security,
          ),
          const SizedBox(height: 12),
          _buildDetailRow(
            'الإصدار',
            rootAccess.version ?? 'غير محدد',
            Icons.info,
          ),
          const SizedBox(height: 12),
          _buildDetailRow(
            'المسار',
            rootAccess.binaryPath ?? 'غير محدد',
            Icons.folder,
          ),
          if (rootAccess.lastChecked != null) ...[
            const SizedBox(height: 12),
            _buildDetailRow(
              'آخر فحص',
              _formatDateTime(rootAccess.lastChecked!),
              Icons.access_time,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.8),
          size: 18,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(RootToolsState state) {
    if (state is RootAccessLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (state is RootAccessLoaded) {
      if (state.rootAccess.isGranted) {
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: onRevokeRoot,
                icon: const Icon(Icons.block),
                label: const Text('إلغاء الصلاحيات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Colors.white.withOpacity(0.3),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // Navigate to root tools
                },
                icon: const Icon(Icons.build),
                label: const Text('أدوات الروت'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        );
      } else {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: onRequestRoot,
            icon: const Icon(Icons.security),
            label: const Text('طلب صلاحيات الروت'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      }
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onRequestRoot,
        icon: const Icon(Icons.refresh),
        label: const Text('فحص الصلاحيات'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white.withOpacity(0.2),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Colors.white.withOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getGradient(RootToolsState state) {
    if (state is RootAccessLoaded && state.rootAccess.isGranted) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF4CAF50),
          Color(0xFF2E7D32),
        ],
      );
    } else if (state is RootAccessLoaded && !state.rootAccess.isGranted) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFF44336),
          Color(0xFFD32F2F),
        ],
      );
    } else {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF9E9E9E),
          Color(0xFF757575),
        ],
      );
    }
  }

  Color _getStatusColor(RootToolsState state) {
    if (state is RootAccessLoaded && state.rootAccess.isGranted) {
      return Colors.green;
    } else if (state is RootAccessLoaded && !state.rootAccess.isGranted) {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }

  IconData _getStatusIcon(RootToolsState state) {
    if (state is RootAccessLoaded && state.rootAccess.isGranted) {
      return Icons.verified_user;
    } else if (state is RootAccessLoaded && !state.rootAccess.isGranted) {
      return Icons.security;
    } else if (state is RootAccessLoading) {
      return Icons.hourglass_empty;
    } else {
      return Icons.help_outline;
    }
  }

  String _getStatusText(RootToolsState state) {
    if (state is RootAccessLoaded && state.rootAccess.isGranted) {
      return 'صلاحيات الروت متاحة';
    } else if (state is RootAccessLoaded && !state.rootAccess.isGranted) {
      return 'صلاحيات الروت غير متاحة';
    } else if (state is RootAccessLoading) {
      return 'جاري الفحص...';
    } else if (state is RootAccessError) {
      return 'خطأ في الفحص';
    } else {
      return 'حالة غير معروفة';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
