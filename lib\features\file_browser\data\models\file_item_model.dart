import 'dart:io';
import '../../domain/entities/file_item.dart';
import '../../../../core/utils/file_utils.dart';

class FileItemModel extends FileItem {
  const FileItemModel({
    required super.name,
    required super.path,
    required super.size,
    required super.modifiedDate,
    required super.isDirectory,
    required super.isHidden,
    required super.permissions,
    super.mimeType,
    super.extension,
    super.fileType,
  });

  factory FileItemModel.fromFileSystemEntity(FileSystemEntity entity) {
    final stat = entity.statSync();
    final path = entity.path;
    final name = FileUtils.getFileName(path);
    final isDirectory = entity is Directory;
    final isHidden = FileUtils.isHidden(path);
    final size = isDirectory ? 0 : stat.size;
    final modifiedDate = stat.modified;
    final extension = isDirectory ? null : FileUtils.getFileExtension(path);
    final mimeType = isDirectory ? null : FileUtils.getMimeType(path);
    final fileType = isDirectory ? null : FileUtils.getFileType(path);
    
    // Get permissions (simplified)
    final permissions = FilePermissions(
      readable: stat.mode & 0x100 != 0, // Owner read
      writable: stat.mode & 0x080 != 0, // Owner write
      executable: stat.mode & 0x040 != 0, // Owner execute
    );

    return FileItemModel(
      name: name,
      path: path,
      size: size,
      modifiedDate: modifiedDate,
      isDirectory: isDirectory,
      isHidden: isHidden,
      permissions: permissions,
      mimeType: mimeType,
      extension: extension,
      fileType: fileType,
    );
  }

  factory FileItemModel.fromJson(Map<String, dynamic> json) {
    return FileItemModel(
      name: json['name'] as String,
      path: json['path'] as String,
      size: json['size'] as int,
      modifiedDate: DateTime.parse(json['modifiedDate'] as String),
      isDirectory: json['isDirectory'] as bool,
      isHidden: json['isHidden'] as bool,
      permissions: FilePermissions.fromJson(json['permissions'] as Map<String, dynamic>),
      mimeType: json['mimeType'] as String?,
      extension: json['extension'] as String?,
      fileType: json['fileType'] != null 
          ? FileType.values[json['fileType'] as int]
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'path': path,
      'size': size,
      'modifiedDate': modifiedDate.toIso8601String(),
      'isDirectory': isDirectory,
      'isHidden': isHidden,
      'permissions': permissions.toJson(),
      'mimeType': mimeType,
      'extension': extension,
      'fileType': fileType?.index,
    };
  }

  FileItemModel copyWith({
    String? name,
    String? path,
    int? size,
    DateTime? modifiedDate,
    bool? isDirectory,
    bool? isHidden,
    FilePermissions? permissions,
    String? mimeType,
    String? extension,
    FileType? fileType,
  }) {
    return FileItemModel(
      name: name ?? this.name,
      path: path ?? this.path,
      size: size ?? this.size,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      isDirectory: isDirectory ?? this.isDirectory,
      isHidden: isHidden ?? this.isHidden,
      permissions: permissions ?? this.permissions,
      mimeType: mimeType ?? this.mimeType,
      extension: extension ?? this.extension,
      fileType: fileType ?? this.fileType,
    );
  }
}
