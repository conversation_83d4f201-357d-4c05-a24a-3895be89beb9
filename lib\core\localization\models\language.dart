import 'package:flutter/material.dart';

/// نموذج اللغة
class Language {
  final String code;
  final String name;
  final String nativeName;
  final String flag;
  final bool isRTL;
  final Locale locale;

  const Language({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.flag,
    required this.isRTL,
    required this.locale,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Language &&
          runtimeType == other.runtimeType &&
          code == other.code;

  @override
  int get hashCode => code.hashCode;

  @override
  String toString() => 'Language(code: $code, name: $name, nativeName: $nativeName)';

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'nativeName': nativeName,
      'flag': flag,
      'isRTL': isRTL,
      'locale': {
        'languageCode': locale.languageCode,
        'countryCode': locale.countryCode,
      },
    };
  }

  /// إنشاء من JSON
  factory Language.fromJson(Map<String, dynamic> json) {
    return Language(
      code: json['code'] as String,
      name: json['name'] as String,
      nativeName: json['nativeName'] as String,
      flag: json['flag'] as String,
      isRTL: json['isRTL'] as bool,
      locale: Locale(
        json['locale']['languageCode'] as String,
        json['locale']['countryCode'] as String?,
      ),
    );
  }

  /// نسخ مع تعديل
  Language copyWith({
    String? code,
    String? name,
    String? nativeName,
    String? flag,
    bool? isRTL,
    Locale? locale,
  }) {
    return Language(
      code: code ?? this.code,
      name: name ?? this.name,
      nativeName: nativeName ?? this.nativeName,
      flag: flag ?? this.flag,
      isRTL: isRTL ?? this.isRTL,
      locale: locale ?? this.locale,
    );
  }
}

/// اللغات المدعومة
class SupportedLanguages {
  static const Language arabic = Language(
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    isRTL: true,
    locale: Locale('ar', 'SA'),
  );

  static const Language english = Language(
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    isRTL: false,
    locale: Locale('en', 'US'),
  );

  static const Language french = Language(
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    isRTL: false,
    locale: Locale('fr', 'FR'),
  );

  static const Language spanish = Language(
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    isRTL: false,
    locale: Locale('es', 'ES'),
  );

  static const Language german = Language(
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    isRTL: false,
    locale: Locale('de', 'DE'),
  );

  static const Language chinese = Language(
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    isRTL: false,
    locale: Locale('zh', 'CN'),
  );

  static const Language japanese = Language(
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    isRTL: false,
    locale: Locale('ja', 'JP'),
  );

  static const Language korean = Language(
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    isRTL: false,
    locale: Locale('ko', 'KR'),
  );

  static const Language russian = Language(
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    isRTL: false,
    locale: Locale('ru', 'RU'),
  );

  static const Language portuguese = Language(
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇵🇹',
    isRTL: false,
    locale: Locale('pt', 'PT'),
  );

  static const Language italian = Language(
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    flag: '🇮🇹',
    isRTL: false,
    locale: Locale('it', 'IT'),
  );

  static const Language dutch = Language(
    code: 'nl',
    name: 'Dutch',
    nativeName: 'Nederlands',
    flag: '🇳🇱',
    isRTL: false,
    locale: Locale('nl', 'NL'),
  );

  static const Language turkish = Language(
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe',
    flag: '🇹🇷',
    isRTL: false,
    locale: Locale('tr', 'TR'),
  );

  static const Language hindi = Language(
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    isRTL: false,
    locale: Locale('hi', 'IN'),
  );

  static const Language urdu = Language(
    code: 'ur',
    name: 'Urdu',
    nativeName: 'اردو',
    flag: '🇵🇰',
    isRTL: true,
    locale: Locale('ur', 'PK'),
  );

  static const Language persian = Language(
    code: 'fa',
    name: 'Persian',
    nativeName: 'فارسی',
    flag: '🇮🇷',
    isRTL: true,
    locale: Locale('fa', 'IR'),
  );

  /// قائمة جميع اللغات المدعومة
  static const List<Language> all = [
    arabic,
    english,
    french,
    spanish,
    german,
    chinese,
    japanese,
    korean,
    russian,
    portuguese,
    italian,
    dutch,
    turkish,
    hindi,
    urdu,
    persian,
  ];

  /// اللغات الأساسية (المتاحة حالياً)
  static const List<Language> primary = [
    arabic,
    english,
  ];

  /// اللغات RTL
  static const List<Language> rtlLanguages = [
    arabic,
    urdu,
    persian,
  ];

  /// اللغات LTR
  static List<Language> get ltrLanguages =>
      all.where((lang) => !lang.isRTL).toList();

  /// البحث عن لغة بالكود
  static Language? findByCode(String code) {
    try {
      return all.firstWhere((lang) => lang.code == code);
    } catch (e) {
      return null;
    }
  }

  /// البحث عن لغة بالـ Locale
  static Language? findByLocale(Locale locale) {
    try {
      return all.firstWhere((lang) => lang.locale == locale);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على اللغة الافتراضية
  static Language get defaultLanguage => english;

  /// الحصول على اللغة المفضلة للنظام
  static Language getSystemLanguage() {
    // الحصول على لغة النظام
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    
    // البحث عن اللغة المطابقة
    Language? language = findByLocale(systemLocale);
    
    // إذا لم توجد، البحث بكود اللغة فقط
    if (language == null) {
      language = findByCode(systemLocale.languageCode);
    }
    
    // إذا لم توجد، استخدام اللغة الافتراضية
    return language ?? defaultLanguage;
  }

  /// التحقق من دعم اللغة
  static bool isSupported(String code) {
    return findByCode(code) != null;
  }

  /// التحقق من كون اللغة RTL
  static bool isRTL(String code) {
    final language = findByCode(code);
    return language?.isRTL ?? false;
  }

  /// الحصول على قائمة الـ Locales المدعومة
  static List<Locale> get supportedLocales {
    return primary.map((lang) => lang.locale).toList();
  }
}
