import 'package:equatable/equatable.dart';
import '../../domain/entities/root_access.dart';

abstract class RootToolsEvent extends Equatable {
  const RootToolsEvent();

  @override
  List<Object?> get props => [];
}

// Root Access Events
class CheckRootAccessEvent extends RootToolsEvent {
  const CheckRootAccessEvent();
}

class RequestRootPermissionEvent extends RootToolsEvent {
  const RequestRootPermissionEvent();
}

class RevokeRootPermissionEvent extends RootToolsEvent {
  const RevokeRootPermissionEvent();
}

// Command Execution Events
class ExecuteCommandEvent extends RootToolsEvent {
  final String command;

  const ExecuteCommandEvent(this.command);

  @override
  List<Object?> get props => [command];
}

class ExecuteCommandWithTimeoutEvent extends RootToolsEvent {
  final String command;
  final Duration timeout;

  const ExecuteCommandWithTimeoutEvent(this.command, this.timeout);

  @override
  List<Object?> get props => [command, timeout];
}

class ExecuteBatchCommandsEvent extends RootToolsEvent {
  final List<String> commands;

  const ExecuteBatchCommandsEvent(this.commands);

  @override
  List<Object?> get props => [commands];
}

class StartCommandStreamEvent extends RootToolsEvent {
  final String command;

  const StartCommandStreamEvent(this.command);

  @override
  List<Object?> get props => [command];
}

class StopCommandStreamEvent extends RootToolsEvent {
  const StopCommandStreamEvent();
}

// System Information Events
class LoadSystemPartitionsEvent extends RootToolsEvent {
  const LoadSystemPartitionsEvent();
}

class LoadRunningProcessesEvent extends RootToolsEvent {
  const LoadRunningProcessesEvent();
}

class LoadSystemServicesEvent extends RootToolsEvent {
  const LoadSystemServicesEvent();
}

class LoadSystemPropertiesEvent extends RootToolsEvent {
  const LoadSystemPropertiesEvent();
}

class LoadSystemInfoEvent extends RootToolsEvent {
  const LoadSystemInfoEvent();
}

// Process Management Events
class KillProcessEvent extends RootToolsEvent {
  final int pid;

  const KillProcessEvent(this.pid);

  @override
  List<Object?> get props => [pid];
}

class KillProcessByNameEvent extends RootToolsEvent {
  final String processName;

  const KillProcessByNameEvent(this.processName);

  @override
  List<Object?> get props => [processName];
}

class ChangeProcessPriorityEvent extends RootToolsEvent {
  final int pid;
  final int priority;

  const ChangeProcessPriorityEvent(this.pid, this.priority);

  @override
  List<Object?> get props => [pid, priority];
}

// Service Management Events
class StartServiceEvent extends RootToolsEvent {
  final String serviceName;

  const StartServiceEvent(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class StopServiceEvent extends RootToolsEvent {
  final String serviceName;

  const StopServiceEvent(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class RestartServiceEvent extends RootToolsEvent {
  final String serviceName;

  const RestartServiceEvent(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class EnableServiceEvent extends RootToolsEvent {
  final String serviceName;

  const EnableServiceEvent(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

class DisableServiceEvent extends RootToolsEvent {
  final String serviceName;

  const DisableServiceEvent(this.serviceName);

  @override
  List<Object?> get props => [serviceName];
}

// File System Events
class MountPartitionEvent extends RootToolsEvent {
  final String partition;
  final String mountPoint;

  const MountPartitionEvent(this.partition, this.mountPoint);

  @override
  List<Object?> get props => [partition, mountPoint];
}

class UnmountPartitionEvent extends RootToolsEvent {
  final String partition;

  const UnmountPartitionEvent(this.partition);

  @override
  List<Object?> get props => [partition];
}

class RemountPartitionEvent extends RootToolsEvent {
  final String partition;
  final bool readOnly;

  const RemountPartitionEvent(this.partition, this.readOnly);

  @override
  List<Object?> get props => [partition, readOnly];
}

class ChangeFilePermissionsEvent extends RootToolsEvent {
  final String path;
  final String permissions;

  const ChangeFilePermissionsEvent(this.path, this.permissions);

  @override
  List<Object?> get props => [path, permissions];
}

class ChangeFileOwnershipEvent extends RootToolsEvent {
  final String path;
  final String owner;
  final String group;

  const ChangeFileOwnershipEvent(this.path, this.owner, this.group);

  @override
  List<Object?> get props => [path, owner, group];
}

// System Modifications Events
class ModifySystemPropertyEvent extends RootToolsEvent {
  final String property;
  final String value;

  const ModifySystemPropertyEvent(this.property, this.value);

  @override
  List<Object?> get props => [property, value];
}

class ModifyBuildPropEvent extends RootToolsEvent {
  final Map<String, String> properties;

  const ModifyBuildPropEvent(this.properties);

  @override
  List<Object?> get props => [properties];
}

class InstallSystemAppEvent extends RootToolsEvent {
  final String apkPath;

  const InstallSystemAppEvent(this.apkPath);

  @override
  List<Object?> get props => [apkPath];
}

class UninstallSystemAppEvent extends RootToolsEvent {
  final String packageName;

  const UninstallSystemAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class FreezeAppEvent extends RootToolsEvent {
  final String packageName;

  const FreezeAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

class UnfreezeAppEvent extends RootToolsEvent {
  final String packageName;

  const UnfreezeAppEvent(this.packageName);

  @override
  List<Object?> get props => [packageName];
}

// Backup and Restore Events
class CreateSystemBackupEvent extends RootToolsEvent {
  final String name;
  final BackupType type;
  final List<String> paths;

  const CreateSystemBackupEvent(this.name, this.type, this.paths);

  @override
  List<Object?> get props => [name, type, paths];
}

class RestoreSystemBackupEvent extends RootToolsEvent {
  final String backupId;

  const RestoreSystemBackupEvent(this.backupId);

  @override
  List<Object?> get props => [backupId];
}

class LoadAvailableBackupsEvent extends RootToolsEvent {
  const LoadAvailableBackupsEvent();
}

class DeleteBackupEvent extends RootToolsEvent {
  final String backupId;

  const DeleteBackupEvent(this.backupId);

  @override
  List<Object?> get props => [backupId];
}

// Boot Management Events
class LoadBootInfoEvent extends RootToolsEvent {
  const LoadBootInfoEvent();
}

class FlashRecoveryEvent extends RootToolsEvent {
  final String recoveryPath;

  const FlashRecoveryEvent(this.recoveryPath);

  @override
  List<Object?> get props => [recoveryPath];
}

class FlashKernelEvent extends RootToolsEvent {
  final String kernelPath;

  const FlashKernelEvent(this.kernelPath);

  @override
  List<Object?> get props => [kernelPath];
}

class RebootToRecoveryEvent extends RootToolsEvent {
  const RebootToRecoveryEvent();
}

class RebootToBootloaderEvent extends RootToolsEvent {
  const RebootToBootloaderEvent();
}

class RebootToDownloadModeEvent extends RootToolsEvent {
  const RebootToDownloadModeEvent();
}

// Advanced Tools Events
class LoadInstalledModulesEvent extends RootToolsEvent {
  const LoadInstalledModulesEvent();
}

class InstallModuleEvent extends RootToolsEvent {
  final String modulePath;

  const InstallModuleEvent(this.modulePath);

  @override
  List<Object?> get props => [modulePath];
}

class UninstallModuleEvent extends RootToolsEvent {
  final String moduleId;

  const UninstallModuleEvent(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

class EnableModuleEvent extends RootToolsEvent {
  final String moduleId;

  const EnableModuleEvent(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

class DisableModuleEvent extends RootToolsEvent {
  final String moduleId;

  const DisableModuleEvent(this.moduleId);

  @override
  List<Object?> get props => [moduleId];
}

// System Tweaks Events
class EnableDeveloperOptionsEvent extends RootToolsEvent {
  const EnableDeveloperOptionsEvent();
}

class EnableAdbDebuggingEvent extends RootToolsEvent {
  const EnableAdbDebuggingEvent();
}

class SetAnimationScaleEvent extends RootToolsEvent {
  final double scale;

  const SetAnimationScaleEvent(this.scale);

  @override
  List<Object?> get props => [scale];
}

class SetDpiEvent extends RootToolsEvent {
  final int dpi;

  const SetDpiEvent(this.dpi);

  @override
  List<Object?> get props => [dpi];
}

class SetGovernorEvent extends RootToolsEvent {
  final String governor;

  const SetGovernorEvent(this.governor);

  @override
  List<Object?> get props => [governor];
}

class SetCpuFrequencyEvent extends RootToolsEvent {
  final int minFreq;
  final int maxFreq;

  const SetCpuFrequencyEvent(this.minFreq, this.maxFreq);

  @override
  List<Object?> get props => [minFreq, maxFreq];
}

// Security and Privacy Events
class RemoveSystemAppsEvent extends RootToolsEvent {
  final List<String> packageNames;

  const RemoveSystemAppsEvent(this.packageNames);

  @override
  List<Object?> get props => [packageNames];
}

class DisableSystemAppsEvent extends RootToolsEvent {
  final List<String> packageNames;

  const DisableSystemAppsEvent(this.packageNames);

  @override
  List<Object?> get props => [packageNames];
}

class BlockAdsEvent extends RootToolsEvent {
  const BlockAdsEvent();
}

class UnblockAdsEvent extends RootToolsEvent {
  const UnblockAdsEvent();
}

class EnableFirewallEvent extends RootToolsEvent {
  const EnableFirewallEvent();
}

class DisableFirewallEvent extends RootToolsEvent {
  const DisableFirewallEvent();
}

class LoadFirewallRulesEvent extends RootToolsEvent {
  const LoadFirewallRulesEvent();
}

class AddFirewallRuleEvent extends RootToolsEvent {
  final String rule;

  const AddFirewallRuleEvent(this.rule);

  @override
  List<Object?> get props => [rule];
}

class RemoveFirewallRuleEvent extends RootToolsEvent {
  final String rule;

  const RemoveFirewallRuleEvent(this.rule);

  @override
  List<Object?> get props => [rule];
}

// Performance Optimization Events
class ClearSystemCacheEvent extends RootToolsEvent {
  const ClearSystemCacheEvent();
}

class OptimizeDatabaseEvent extends RootToolsEvent {
  const OptimizeDatabaseEvent();
}

class DefragmentStorageEvent extends RootToolsEvent {
  const DefragmentStorageEvent();
}

class TrimFilesystemEvent extends RootToolsEvent {
  const TrimFilesystemEvent();
}

class OptimizeMemoryEvent extends RootToolsEvent {
  const OptimizeMemoryEvent();
}

// Monitoring Events
class StartSystemMonitoringEvent extends RootToolsEvent {
  const StartSystemMonitoringEvent();
}

class StopSystemMonitoringEvent extends RootToolsEvent {
  const StopSystemMonitoringEvent();
}

class StartProcessMonitoringEvent extends RootToolsEvent {
  const StartProcessMonitoringEvent();
}

class StopProcessMonitoringEvent extends RootToolsEvent {
  const StopProcessMonitoringEvent();
}

// Logs and Diagnostics Events
class LoadSystemLogsEvent extends RootToolsEvent {
  const LoadSystemLogsEvent();
}

class LoadKernelLogsEvent extends RootToolsEvent {
  const LoadKernelLogsEvent();
}

class LoadApplicationLogsEvent extends RootToolsEvent {
  const LoadApplicationLogsEvent();
}

class ClearLogsEvent extends RootToolsEvent {
  const ClearLogsEvent();
}

class RunSystemDiagnosticsEvent extends RootToolsEvent {
  const RunSystemDiagnosticsEvent();
}

// Recovery and Repair Events
class FixPermissionsEvent extends RootToolsEvent {
  const FixPermissionsEvent();
}

class RebuildDalvikCacheEvent extends RootToolsEvent {
  const RebuildDalvikCacheEvent();
}

class ClearDalvikCacheEvent extends RootToolsEvent {
  const ClearDalvikCacheEvent();
}

class FixBootloopEvent extends RootToolsEvent {
  const FixBootloopEvent();
}

class RepairFilesystemEvent extends RootToolsEvent {
  final String partition;

  const RepairFilesystemEvent(this.partition);

  @override
  List<Object?> get props => [partition];
}

// Custom Scripts Events
class LoadAvailableScriptsEvent extends RootToolsEvent {
  const LoadAvailableScriptsEvent();
}

class ExecuteScriptEvent extends RootToolsEvent {
  final String scriptPath;

  const ExecuteScriptEvent(this.scriptPath);

  @override
  List<Object?> get props => [scriptPath];
}

class InstallScriptEvent extends RootToolsEvent {
  final String scriptPath;
  final String name;

  const InstallScriptEvent(this.scriptPath, this.name);

  @override
  List<Object?> get props => [scriptPath, name];
}

class UninstallScriptEvent extends RootToolsEvent {
  final String scriptName;

  const UninstallScriptEvent(this.scriptName);

  @override
  List<Object?> get props => [scriptName];
}

// System Information Export Events
class ExportSystemInfoEvent extends RootToolsEvent {
  const ExportSystemInfoEvent();
}

class ExportInstalledAppsEvent extends RootToolsEvent {
  const ExportInstalledAppsEvent();
}

class ExportSystemLogsEvent extends RootToolsEvent {
  const ExportSystemLogsEvent();
}

class GenerateSystemReportEvent extends RootToolsEvent {
  const GenerateSystemReportEvent();
}
