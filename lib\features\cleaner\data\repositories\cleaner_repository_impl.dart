import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/cleanup_item.dart';
import '../../domain/repositories/cleaner_repository.dart';
import '../datasources/cleaner_datasource.dart';
import '../models/cleanup_item_model.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: CleanerRepository)
class CleanerRepositoryImpl implements CleanerRepository {
  final CleanerDataSource dataSource;

  const CleanerRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, SystemAnalysis>> analyzeSystem() async {
    try {
      final result = await dataSource.analyzeSystem();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<SystemAnalysis> analyzeSystemStream() {
    return dataSource.analyzeSystemStream();
  }

  @override
  Future<Either<Failure, void>> cancelAnalysis() async {
    try {
      await dataSource.cancelAnalysis();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel analysis: $e'));
    }
  }

  @override
  Future<Either<Failure, CleanupResult>> performCleanup(List<CleanupItem> items) async {
    try {
      final itemModels = items.map((item) => CleanupItemModel.fromEntity(item)).toList();
      final result = await dataSource.performCleanup(itemModels);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<CleanupResult> performCleanupStream(List<CleanupItem> items) {
    final itemModels = items.map((item) => CleanupItemModel.fromEntity(item)).toList();
    return dataSource.performCleanupStream(itemModels);
  }

  @override
  Future<Either<Failure, void>> cancelCleanup() async {
    try {
      await dataSource.cancelCleanup();
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to cancel cleanup: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findCacheFiles() async {
    try {
      final result = await dataSource.findCacheFiles();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find cache files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findTemporaryFiles() async {
    try {
      final result = await dataSource.findTemporaryFiles();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find temporary files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findLogFiles() async {
    try {
      final result = await dataSource.findLogFiles();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find log files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findThumbnails() async {
    try {
      final result = await dataSource.findThumbnails();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find thumbnails: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findEmptyFolders() async {
    try {
      final result = await dataSource.findEmptyFolders();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find empty folders: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findLargeFiles(int minSizeMB) async {
    try {
      final result = await dataSource.findLargeFiles(minSizeMB);
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find large files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findOldFiles(int daysOld) async {
    try {
      final result = await dataSource.findOldFiles(daysOld);
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find old files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findApkFiles() async {
    try {
      final result = await dataSource.findApkFiles();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find APK files: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupItem>>> findResidualFiles() async {
    try {
      final result = await dataSource.findResidualFiles();
      return Right(result.cast<CleanupItem>());
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to find residual files: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, int>>> analyzeLargestDirectories() async {
    try {
      final result = await dataSource.analyzeLargestDirectories();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to analyze directories: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, int>>> analyzeFileTypeDistribution() async {
    try {
      final result = await dataSource.analyzeFileTypeDistribution();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to analyze file types: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> calculateDirectorySize(String path) async {
    try {
      final result = await dataSource.calculateDirectorySize(path);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to calculate directory size: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CleanupResult>>> getCleanupHistory() async {
    try {
      final result = await dataSource.getCleanupHistory();
      return Right(result.cast<CleanupResult>());
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to get cleanup history: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveCleanupResult(CleanupResult result) async {
    try {
      final resultModel = result is CleanupResultModel 
          ? result 
          : CleanupResultModel(
              deletedFiles: result.deletedFiles,
              totalDeletedSize: result.totalDeletedSize,
              totalDeletedCount: result.totalDeletedCount,
              failedDeletions: result.failedDeletions,
              cleanupDuration: result.cleanupDuration,
              isComplete: result.isComplete,
              errorMessage: result.errorMessage,
            );
      
      await dataSource.saveCleanupResult(resultModel);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to save cleanup result: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearCleanupHistory() async {
    try {
      await dataSource.clearCleanupHistory();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to clear cleanup history: $e'));
    }
  }
}
