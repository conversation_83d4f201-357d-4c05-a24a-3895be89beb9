import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../repositories/root_tools_repository.dart';

@lazySingleton
class SecurityManager {
  final RootToolsRepository repository;

  const SecurityManager(this.repository);

  // App Security
  ResultVoid removeSystemApps(List<String> packageNames) async {
    return await repository.removeSystemApps(packageNames);
  }

  ResultVoid disableSystemApps(List<String> packageNames) async {
    return await repository.disableSystemApps(packageNames);
  }

  ResultVoid freezeApp(String packageName) async {
    return await repository.freezeApp(packageName);
  }

  ResultVoid unfreezeApp(String packageName) async {
    return await repository.unfreezeApp(packageName);
  }

  // Network Security
  ResultVoid blockAds() async {
    return await repository.blockAds();
  }

  ResultVoid unblockAds() async {
    return await repository.unblockAds();
  }

  ResultVoid enableFirewall() async {
    return await repository.enableFirewall();
  }

  ResultVoid disableFirewall() async {
    return await repository.disableFirewall();
  }

  ResultFuture<List<String>> getFirewallRules() async {
    return await repository.getFirewallRules();
  }

  ResultVoid addFirewallRule(String rule) async {
    return await repository.addFirewallRule(rule);
  }

  ResultVoid removeFirewallRule(String rule) async {
    return await repository.removeFirewallRule(rule);
  }

  // Privacy Protection
  ResultVoid disableLocationTracking() async {
    return await repository.modifySystemProperty('ro.config.nocheckin', '1');
  }

  ResultVoid disableAnalytics() async {
    final analyticsApps = [
      'com.google.android.gms.analytics',
      'com.facebook.analytics',
      'com.crashlytics.android',
      'com.flurry.android',
    ];
    return await repository.disableSystemApps(analyticsApps);
  }

  ResultVoid blockTelemetry() async {
    final telemetryHosts = [
      'google-analytics.com',
      'googleadservices.com',
      'googlesyndication.com',
      'facebook.com/tr',
      'graph.facebook.com',
    ];
    
    for (final host in telemetryHosts) {
      await repository.addFirewallRule('REJECT $host');
    }
    
    return const Right(null);
  }

  // System Hardening
  ResultVoid enableSecureBoot() async {
    return await repository.modifySystemProperty('ro.secure', '1');
  }

  ResultVoid disableAdbInProduction() async {
    return await repository.modifySystemProperty('ro.adb.secure', '1');
  }

  ResultVoid enableVerifyApps() async {
    return await repository.modifySystemProperty('ro.config.verifyapps', '1');
  }

  ResultVoid disableUnknownSources() async {
    return await repository.modifySystemProperty('ro.config.unknown_sources', '0');
  }

  // Permission Management
  ResultVoid revokePermission(String packageName, String permission) async {
    final command = 'pm revoke $packageName $permission';
    final result = await repository.executeCommand(command);
    return result.fold(
      (failure) => Left(failure),
      (commandResult) => commandResult.isSuccess 
          ? const Right(null) 
          : Left(ServerFailure(message: commandResult.error)),
    );
  }

  ResultVoid grantPermission(String packageName, String permission) async {
    final command = 'pm grant $packageName $permission';
    final result = await repository.executeCommand(command);
    return result.fold(
      (failure) => Left(failure),
      (commandResult) => commandResult.isSuccess 
          ? const Right(null) 
          : Left(ServerFailure(message: commandResult.error)),
    );
  }

  ResultFuture<List<String>> getAppPermissions(String packageName) async {
    final command = 'dumpsys package $packageName | grep permission';
    final result = await repository.executeCommand(command);
    return result.fold(
      (failure) => Left(failure),
      (commandResult) {
        if (commandResult.isSuccess) {
          final permissions = commandResult.output
              .split('\n')
              .where((line) => line.trim().isNotEmpty)
              .map((line) => line.trim())
              .toList();
          return Right(permissions);
        } else {
          return Left(ServerFailure(message: commandResult.error));
        }
      },
    );
  }

  // Malware Protection
  ResultFuture<List<String>> scanForMalware() async {
    final suspiciousApps = <String>[];
    
    // Check for apps with suspicious permissions
    final dangerousPermissions = [
      'android.permission.SEND_SMS',
      'android.permission.CALL_PHONE',
      'android.permission.ACCESS_FINE_LOCATION',
      'android.permission.RECORD_AUDIO',
      'android.permission.CAMERA',
      'android.permission.READ_CONTACTS',
      'android.permission.READ_SMS',
    ];

    // This would be implemented with actual malware scanning logic
    // For now, return empty list
    return Right(suspiciousApps);
  }

  ResultVoid quarantineApp(String packageName) async {
    // Disable the app and revoke all permissions
    await repository.disableSystemApps([packageName]);
    
    final permissions = await getAppPermissions(packageName);
    return permissions.fold(
      (failure) => Left(failure),
      (perms) async {
        for (final permission in perms) {
          await revokePermission(packageName, permission);
        }
        return const Right(null);
      },
    );
  }

  // Security Audit
  ResultFuture<SecurityAuditReport> performSecurityAudit() async {
    final report = SecurityAuditReport(
      timestamp: DateTime.now(),
      issues: [],
      recommendations: [],
      riskLevel: SecurityRiskLevel.low,
    );

    // Check for common security issues
    final issues = <SecurityIssue>[];
    final recommendations = <String>[];

    // Check if ADB is enabled
    final adbEnabled = await _checkAdbEnabled();
    if (adbEnabled) {
      issues.add(SecurityIssue(
        type: SecurityIssueType.adbEnabled,
        severity: SecuritySeverity.medium,
        description: 'ADB debugging is enabled',
        recommendation: 'Disable ADB debugging in production',
      ));
    }

    // Check for unknown sources
    final unknownSources = await _checkUnknownSources();
    if (unknownSources) {
      issues.add(SecurityIssue(
        type: SecurityIssueType.unknownSources,
        severity: SecuritySeverity.high,
        description: 'Unknown sources installation is enabled',
        recommendation: 'Disable installation from unknown sources',
      ));
    }

    // Check for root access
    final rootAccess = await repository.checkRootAccess();
    return rootAccess.fold(
      (failure) => Left(failure),
      (access) {
        if (access.isRooted) {
          issues.add(SecurityIssue(
            type: SecurityIssueType.rootAccess,
            severity: SecuritySeverity.high,
            description: 'Device has root access',
            recommendation: 'Consider unrooting for better security',
          ));
        }

        final updatedReport = SecurityAuditReport(
          timestamp: report.timestamp,
          issues: issues,
          recommendations: recommendations,
          riskLevel: _calculateRiskLevel(issues),
        );

        return Right(updatedReport);
      },
    );
  }

  // Helper methods
  Future<bool> _checkAdbEnabled() async {
    final result = await repository.getSystemProperties();
    return result.fold(
      (failure) => false,
      (properties) => properties['ro.adb.secure'] != '1',
    );
  }

  Future<bool> _checkUnknownSources() async {
    final result = await repository.getSystemProperties();
    return result.fold(
      (failure) => false,
      (properties) => properties['ro.config.unknown_sources'] == '1',
    );
  }

  SecurityRiskLevel _calculateRiskLevel(List<SecurityIssue> issues) {
    if (issues.any((issue) => issue.severity == SecuritySeverity.critical)) {
      return SecurityRiskLevel.critical;
    }
    if (issues.any((issue) => issue.severity == SecuritySeverity.high)) {
      return SecurityRiskLevel.high;
    }
    if (issues.any((issue) => issue.severity == SecuritySeverity.medium)) {
      return SecurityRiskLevel.medium;
    }
    return SecurityRiskLevel.low;
  }

  // Security recommendations
  List<String> getSecurityRecommendations() {
    return [
      'Disable ADB debugging in production',
      'Disable installation from unknown sources',
      'Enable app verification',
      'Use strong screen lock',
      'Keep system updated',
      'Review app permissions regularly',
      'Enable device encryption',
      'Use VPN for public WiFi',
      'Disable unnecessary system apps',
      'Enable firewall protection',
      'Block ads and trackers',
      'Regular security audits',
    ];
  }

  Map<String, String> getPrivacySettings() {
    return {
      'Location Services': 'Disable for better privacy',
      'Analytics': 'Disable data collection',
      'Crash Reporting': 'Disable automatic reporting',
      'Usage Statistics': 'Disable usage tracking',
      'Personalized Ads': 'Disable ad personalization',
      'Background App Refresh': 'Limit background activity',
      'Microphone Access': 'Review app permissions',
      'Camera Access': 'Review app permissions',
      'Contacts Access': 'Review app permissions',
      'SMS Access': 'Review app permissions',
    };
  }
}

class SecurityAuditReport {
  final DateTime timestamp;
  final List<SecurityIssue> issues;
  final List<String> recommendations;
  final SecurityRiskLevel riskLevel;

  const SecurityAuditReport({
    required this.timestamp,
    required this.issues,
    required this.recommendations,
    required this.riskLevel,
  });
}

class SecurityIssue {
  final SecurityIssueType type;
  final SecuritySeverity severity;
  final String description;
  final String recommendation;

  const SecurityIssue({
    required this.type,
    required this.severity,
    required this.description,
    required this.recommendation,
  });
}

enum SecurityIssueType {
  adbEnabled,
  unknownSources,
  rootAccess,
  weakPermissions,
  suspiciousApp,
  outdatedSystem,
  insecureNetwork,
}

enum SecuritySeverity {
  low,
  medium,
  high,
  critical,
}

enum SecurityRiskLevel {
  low,
  medium,
  high,
  critical,
}
