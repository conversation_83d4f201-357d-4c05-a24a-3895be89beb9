import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/root_tools_bloc.dart';
import '../bloc/root_tools_event.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/animated_card.dart';

class QuickActionsGrid extends StatelessWidget {
  const QuickActionsGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.outline.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.flash_on,
                    color: AppColors.secondary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الإجراءات السريعة',
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'أدوات سريعة للمهام الشائعة',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                _buildQuickAction(
                  context,
                  title: 'مسح الذاكرة المؤقتة',
                  subtitle: 'تحسين الأداء',
                  icon: Icons.cleaning_services,
                  color: Colors.blue,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const ClearSystemCacheEvent(),
                      ),
                ),
                _buildQuickAction(
                  context,
                  title: 'تحسين قاعدة البيانات',
                  subtitle: 'تسريع التطبيقات',
                  icon: Icons.data_usage,
                  color: Colors.green,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const OptimizeDatabaseEvent(),
                      ),
                ),
                _buildQuickAction(
                  context,
                  title: 'تحسين الذاكرة',
                  subtitle: 'تحرير الذاكرة',
                  icon: Icons.memory,
                  color: Colors.orange,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const OptimizeMemoryEvent(),
                      ),
                ),
                _buildQuickAction(
                  context,
                  title: 'حظر الإعلانات',
                  subtitle: 'حماية الخصوصية',
                  icon: Icons.block,
                  color: Colors.red,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const BlockAdsEvent(),
                      ),
                ),
                _buildQuickAction(
                  context,
                  title: 'تشخيص النظام',
                  subtitle: 'فحص الصحة',
                  icon: Icons.health_and_safety,
                  color: Colors.purple,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const RunSystemDiagnosticsEvent(),
                      ),
                ),
                _buildQuickAction(
                  context,
                  title: 'إصلاح الصلاحيات',
                  subtitle: 'حل المشاكل',
                  icon: Icons.build_circle,
                  color: Colors.teal,
                  onTap:
                      () => context.read<RootToolsBloc>().add(
                        const FixPermissionsEvent(),
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
