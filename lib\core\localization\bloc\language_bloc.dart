import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/language.dart';
import '../services/translation_service.dart';
import 'language_event.dart';
import 'language_state.dart';

@singleton
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  final TranslationService _translationService;
  final SharedPreferences _prefs;

  static const String _languageCodeKey = 'selected_language_code';
  static const String _languagePreferencesKey = 'language_preferences';
  static const String _translationSettingsKey = 'translation_settings';

  Language _currentLanguage = SupportedLanguages.defaultLanguage;
  Map<String, dynamic> _preferences = {};
  Map<String, dynamic> _settings = {};

  LanguageBloc(
    this._translationService,
    this._prefs,
  ) : super(const LanguageInitial()) {
    // تسجيل معالجات الأحداث
    on<LoadSavedLanguageEvent>(_onLoadSavedLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);
    on<LoadTranslationsEvent>(_onLoadTranslations);
    on<ReloadTranslationsEvent>(_onReloadTranslations);
    on<UpdateTranslationEvent>(_onUpdateTranslation);
    on<ClearTranslationsEvent>(_onClearTranslations);
    on<ImportTranslationsEvent>(_onImportTranslations);
    on<MergeTranslationsEvent>(_onMergeTranslations);
    on<SearchTranslationsEvent>(_onSearchTranslations);
    on<ValidateTranslationsEvent>(_onValidateTranslations);
    on<ExportTranslationsEvent>(_onExportTranslations);
    on<GetTranslationInfoEvent>(_onGetTranslationInfo);
    on<DetectSystemLanguageEvent>(_onDetectSystemLanguage);
    on<ToggleTextDirectionEvent>(_onToggleTextDirection);
    on<ResetToDefaultLanguageEvent>(_onResetToDefaultLanguage);
    on<SaveLanguagePreferencesEvent>(_onSaveLanguagePreferences);
    on<LoadLanguagePreferencesEvent>(_onLoadLanguagePreferences);
    on<UpdateTranslationSettingsEvent>(_onUpdateTranslationSettings);
    on<ToggleAutoTranslationEvent>(_onToggleAutoTranslation);
    on<UpdateTranslationCacheEvent>(_onUpdateTranslationCache);
    on<ClearTranslationCacheEvent>(_onClearTranslationCache);
    on<LoadAdditionalTranslationsEvent>(_onLoadAdditionalTranslations);
    on<UnloadAdditionalTranslationsEvent>(_onUnloadAdditionalTranslations);
    on<UpdateTranslationDictionaryEvent>(_onUpdateTranslationDictionary);
    on<AddCustomTranslationEvent>(_onAddCustomTranslation);
    on<RemoveCustomTranslationEvent>(_onRemoveCustomTranslation);
    on<UpdateFontSettingsEvent>(_onUpdateFontSettings);
    on<UpdateLayoutSettingsEvent>(_onUpdateLayoutSettings);
    on<UpdateFormattingSettingsEvent>(_onUpdateFormattingSettings);
    on<UpdateLocaleSettingsEvent>(_onUpdateLocaleSettings);
  }

  /// اللغة الحالية
  Language get currentLanguage => _currentLanguage;

  /// التفضيلات الحالية
  Map<String, dynamic> get preferences => _preferences;

  /// الإعدادات الحالية
  Map<String, dynamic> get settings => _settings;

  /// تحميل اللغة المحفوظة
  Future<void> _onLoadSavedLanguage(
    LoadSavedLanguageEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Loading saved language...'));

      // تحميل كود اللغة المحفوظ
      final savedLanguageCode = _prefs.getString(_languageCodeKey);
      
      Language languageToLoad;
      if (savedLanguageCode != null) {
        final savedLanguage = SupportedLanguages.findByCode(savedLanguageCode);
        languageToLoad = savedLanguage ?? SupportedLanguages.getSystemLanguage();
      } else {
        languageToLoad = SupportedLanguages.getSystemLanguage();
      }

      // تحميل الترجمات
      await _translationService.loadTranslations(languageToLoad);
      _currentLanguage = languageToLoad;

      // تحميل التفضيلات والإعدادات
      await _loadPreferencesAndSettings();

      emit(LanguageLoaded(
        language: languageToLoad,
        translations: _translationService.translations,
        isRTL: languageToLoad.isRTL,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to load saved language: $e',
        fallbackLanguage: SupportedLanguages.defaultLanguage,
      ));
    }
  }

  /// تغيير اللغة
  Future<void> _onChangeLanguage(
    ChangeLanguageEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Changing language...'));

      final previousLanguage = _currentLanguage;
      
      // تحميل الترجمات للغة الجديدة
      await _translationService.loadTranslations(event.language);
      _currentLanguage = event.language;

      // حفظ اللغة الجديدة
      await _prefs.setString(_languageCodeKey, event.language.code);

      emit(LanguageChanged(
        previousLanguage: previousLanguage,
        currentLanguage: event.language,
        translations: _translationService.translations,
        isRTL: event.language.isRTL,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to change language: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحميل الترجمات
  Future<void> _onLoadTranslations(
    LoadTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Loading translations...'));

      await _translationService.loadTranslations(event.language);
      _currentLanguage = event.language;

      emit(LanguageLoaded(
        language: event.language,
        translations: _translationService.translations,
        isRTL: event.language.isRTL,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to load translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// إعادة تحميل الترجمات
  Future<void> _onReloadTranslations(
    ReloadTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Reloading translations...'));

      await _translationService.reloadTranslations();

      emit(TranslationsReloaded(
        language: _currentLanguage,
        translations: _translationService.translations,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to reload translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث ترجمة معينة
  Future<void> _onUpdateTranslation(
    UpdateTranslationEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _translationService.updateTranslation(event.key, event.value);

      emit(TranslationsUpdated(
        language: _currentLanguage,
        translations: _translationService.translations,
        updatedKey: event.key,
        updatedValue: event.value,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update translation: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// مسح الترجمات
  Future<void> _onClearTranslations(
    ClearTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _translationService.clearTranslations();

      emit(TranslationsCleared(_currentLanguage));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to clear translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// استيراد الترجمات
  Future<void> _onImportTranslations(
    ImportTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Importing translations...'));

      _translationService.importTranslations(event.jsonString);
      final importedCount = _translationService.getAllKeys().length;

      emit(TranslationsImported(
        language: _currentLanguage,
        translations: _translationService.translations,
        importedCount: importedCount,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to import translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// دمج الترجمات
  Future<void> _onMergeTranslations(
    MergeTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Merging translations...'));

      final beforeCount = _translationService.getAllKeys().length;
      _translationService.mergeTranslations(event.translations);
      final afterCount = _translationService.getAllKeys().length;
      final mergedCount = afterCount - beforeCount;

      emit(TranslationsMerged(
        language: _currentLanguage,
        translations: _translationService.translations,
        mergedCount: mergedCount,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to merge translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// البحث في الترجمات
  Future<void> _onSearchTranslations(
    SearchTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      final results = _translationService.searchTranslations(event.query);

      emit(TranslationSearchResults(
        query: event.query,
        results: results,
        language: _currentLanguage,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to search translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// التحقق من الترجمات
  Future<void> _onValidateTranslations(
    ValidateTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Validating translations...'));

      final validationResults = _translationService.validateTranslations(event.referenceLanguage);

      emit(TranslationValidationResults(
        language: _currentLanguage,
        referenceLanguage: event.referenceLanguage,
        validationResults: validationResults,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to validate translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تصدير الترجمات
  Future<void> _onExportTranslations(
    ExportTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      final exportedData = _translationService.exportTranslations();

      emit(TranslationsExported(
        language: _currentLanguage,
        exportedData: exportedData,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to export translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// الحصول على معلومات الترجمة
  Future<void> _onGetTranslationInfo(
    GetTranslationInfoEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      final info = _translationService.getTranslationInfo();

      emit(TranslationInfo(
        language: _currentLanguage,
        info: info,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to get translation info: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديد لغة النظام
  Future<void> _onDetectSystemLanguage(
    DetectSystemLanguageEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      final detectedLanguage = SupportedLanguages.getSystemLanguage();
      final shouldChange = detectedLanguage.code != _currentLanguage.code;

      emit(SystemLanguageDetected(
        detectedLanguage: detectedLanguage,
        currentLanguage: _currentLanguage,
        shouldChange: shouldChange,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to detect system language: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تبديل اتجاه النص
  Future<void> _onToggleTextDirection(
    ToggleTextDirectionEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      // هذا مجرد مثال - في الواقع اتجاه النص يعتمد على اللغة
      final wasToggled = !_currentLanguage.isRTL;

      emit(TextDirectionToggled(
        language: _currentLanguage,
        isRTL: _currentLanguage.isRTL,
        wasToggled: wasToggled,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to toggle text direction: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// إعادة تعيين اللغة للافتراضية
  Future<void> _onResetToDefaultLanguage(
    ResetToDefaultLanguageEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Resetting to default language...'));

      final previousLanguage = _currentLanguage;
      final defaultLanguage = SupportedLanguages.defaultLanguage;

      await _translationService.loadTranslations(defaultLanguage);
      _currentLanguage = defaultLanguage;

      // حفظ اللغة الافتراضية
      await _prefs.setString(_languageCodeKey, defaultLanguage.code);

      emit(LanguageResetToDefault(
        previousLanguage: previousLanguage,
        defaultLanguage: defaultLanguage,
        translations: _translationService.translations,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to reset to default language: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// حفظ تفضيلات اللغة
  Future<void> _onSaveLanguagePreferences(
    SaveLanguagePreferencesEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _preferences = event.preferences;
      await _prefs.setString(_languagePreferencesKey, _translationService.exportTranslations());

      emit(LanguagePreferencesSaved(
        language: _currentLanguage,
        preferences: _preferences,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to save language preferences: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحميل تفضيلات اللغة
  Future<void> _onLoadLanguagePreferences(
    LoadLanguagePreferencesEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      await _loadPreferencesAndSettings();

      emit(LanguagePreferencesLoaded(
        language: _currentLanguage,
        preferences: _preferences,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to load language preferences: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث إعدادات الترجمة
  Future<void> _onUpdateTranslationSettings(
    UpdateTranslationSettingsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings = {..._settings, ...event.settings};
      await _prefs.setString(_translationSettingsKey, _translationService.exportTranslations());

      emit(TranslationSettingsUpdated(
        language: _currentLanguage,
        settings: _settings,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update translation settings: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تفعيل/تعطيل الترجمة التلقائية
  Future<void> _onToggleAutoTranslation(
    ToggleAutoTranslationEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings['autoTranslation'] = event.enabled;
      await _prefs.setString(_translationSettingsKey, _translationService.exportTranslations());

      emit(AutoTranslationToggled(
        language: _currentLanguage,
        isEnabled: event.enabled,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to toggle auto translation: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث ذاكرة التخزين المؤقت
  Future<void> _onUpdateTranslationCache(
    UpdateTranslationCacheEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      // تحديث ذاكرة التخزين المؤقت
      final cacheSize = _translationService.getAllKeys().length;
      final lastUpdated = DateTime.now();

      emit(TranslationCacheUpdated(
        language: _currentLanguage,
        cacheSize: cacheSize,
        lastUpdated: lastUpdated,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update translation cache: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// مسح ذاكرة التخزين المؤقت
  Future<void> _onClearTranslationCache(
    ClearTranslationCacheEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _translationService.clearTranslations();

      emit(TranslationCacheCleared(_currentLanguage));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to clear translation cache: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحميل ترجمات إضافية
  Future<void> _onLoadAdditionalTranslations(
    LoadAdditionalTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      emit(const LanguageLoading(message: 'Loading additional translations...'));

      // هنا يمكن تحميل ترجمات إضافية من ملفات منفصلة
      // للوحدات المختلفة (مثل root_tools، network_tools، إلخ)

      emit(AdditionalTranslationsLoaded(
        language: event.language,
        module: event.module,
        translations: _translationService.translations,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to load additional translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// إلغاء تحميل ترجمات إضافية
  Future<void> _onUnloadAdditionalTranslations(
    UnloadAdditionalTranslationsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      // إلغاء تحميل ترجمات وحدة معينة

      emit(AdditionalTranslationsUnloaded(
        language: _currentLanguage,
        module: event.module,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to unload additional translations: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث قاموس الترجمة
  Future<void> _onUpdateTranslationDictionary(
    UpdateTranslationDictionaryEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      // تحديث قاموس الترجمة
      final updatedCount = event.dictionary.length;

      emit(TranslationDictionaryUpdated(
        language: _currentLanguage,
        dictionary: event.dictionary,
        updatedCount: updatedCount,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update translation dictionary: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// إضافة ترجمة مخصصة
  Future<void> _onAddCustomTranslation(
    AddCustomTranslationEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _translationService.updateTranslation(event.key, event.value);

      emit(CustomTranslationAdded(
        language: event.language,
        key: event.key,
        value: event.value,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to add custom translation: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// إزالة ترجمة مخصصة
  Future<void> _onRemoveCustomTranslation(
    RemoveCustomTranslationEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      // إزالة ترجمة مخصصة (يتطلب تطوير إضافي في TranslationService)

      emit(CustomTranslationRemoved(
        language: event.language,
        key: event.key,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to remove custom translation: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث إعدادات الخط
  Future<void> _onUpdateFontSettings(
    UpdateFontSettingsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings['fontFamily'] = event.fontFamily;
      _settings['fontSize'] = event.fontSize;

      emit(FontSettingsUpdated(
        language: event.language,
        fontFamily: event.fontFamily,
        fontSize: event.fontSize,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update font settings: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث إعدادات التخطيط
  Future<void> _onUpdateLayoutSettings(
    UpdateLayoutSettingsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings['isRTL'] = event.isRTL;
      _settings['textAlign'] = event.textAlign;

      emit(LayoutSettingsUpdated(
        language: _currentLanguage,
        isRTL: event.isRTL,
        textAlign: event.textAlign,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update layout settings: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث إعدادات التنسيق
  Future<void> _onUpdateFormattingSettings(
    UpdateFormattingSettingsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings['dateFormat'] = event.dateFormat;
      _settings['timeFormat'] = event.timeFormat;
      _settings['numberFormat'] = event.numberFormat;

      emit(FormattingSettingsUpdated(
        language: _currentLanguage,
        dateFormat: event.dateFormat,
        timeFormat: event.timeFormat,
        numberFormat: event.numberFormat,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update formatting settings: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحديث إعدادات الإقليم
  Future<void> _onUpdateLocaleSettings(
    UpdateLocaleSettingsEvent event,
    Emitter<LanguageState> emit,
  ) async {
    try {
      _settings['currency'] = event.currency;
      _settings['timezone'] = event.timezone;
      _settings['calendar'] = event.calendar;

      emit(LocaleSettingsUpdated(
        language: _currentLanguage,
        currency: event.currency,
        timezone: event.timezone,
        calendar: event.calendar,
      ));
    } catch (e) {
      emit(LanguageError(
        message: 'Failed to update locale settings: $e',
        fallbackLanguage: _currentLanguage,
      ));
    }
  }

  /// تحميل التفضيلات والإعدادات
  Future<void> _loadPreferencesAndSettings() async {
    try {
      final preferencesJson = _prefs.getString(_languagePreferencesKey);
      if (preferencesJson != null) {
        // تحليل التفضيلات من JSON
        _preferences = {}; // يمكن تطوير هذا لاحقاً
      }

      final settingsJson = _prefs.getString(_translationSettingsKey);
      if (settingsJson != null) {
        // تحليل الإعدادات من JSON
        _settings = {}; // يمكن تطوير هذا لاحقاً
      }
    } catch (e) {
      // في حالة الخطأ، استخدام القيم الافتراضية
      _preferences = {};
      _settings = {};
    }
  }
}
