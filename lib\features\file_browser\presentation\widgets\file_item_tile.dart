import 'package:flutter/material.dart';
import '../../domain/entities/file_item.dart';
import '../../../../core/utils/file_utils.dart';
import '../../../../core/constants/app_constants.dart';

class FileItemTile extends StatelessWidget {
  final FileItem file;
  final VoidCallback onTap;
  final VoidCallback onLongPress;

  const FileItemTile({
    super.key,
    required this.file,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: _buildFileIcon(),
      title: Text(
        file.name,
        style: TextStyle(
          fontWeight: file.isDirectory ? FontWeight.bold : FontWeight.normal,
          color: file.isHidden ? Colors.grey[600] : null,
        ),
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: _buildSubtitle(context),
      trailing: _buildTrailing(context),
      onTap: onTap,
      onLongPress: onLongPress,
    );
  }

  Widget _buildFileIcon() {
    if (file.isDirectory) {
      return Icon(
        Icons.folder,
        color: Colors.blue[600],
        size: 32,
      );
    }

    // File icon based on type
    IconData iconData;
    Color iconColor;

    if (file.fileType != null) {
      switch (file.fileType!) {
        case FileType.image:
          iconData = Icons.image;
          iconColor = Colors.green[600]!;
          break;
        case FileType.video:
          iconData = Icons.video_file;
          iconColor = Colors.red[600]!;
          break;
        case FileType.audio:
          iconData = Icons.audio_file;
          iconColor = Colors.orange[600]!;
          break;
        case FileType.document:
          iconData = Icons.description;
          iconColor = Colors.blue[600]!;
          break;
        case FileType.archive:
          iconData = Icons.archive;
          iconColor = Colors.purple[600]!;
          break;
        case FileType.apk:
          iconData = Icons.android;
          iconColor = Colors.green[700]!;
          break;
        case FileType.other:
          iconData = Icons.insert_drive_file;
          iconColor = Colors.grey[600]!;
          break;
      }
    } else {
      iconData = Icons.insert_drive_file;
      iconColor = Colors.grey[600]!;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 32,
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    final List<String> subtitleParts = [];

    // Add size info
    if (file.isDirectory) {
      subtitleParts.add('Folder');
    } else {
      subtitleParts.add(FileUtils.formatFileSize(file.size));
    }

    // Add modified date
    final modifiedDate = file.modifiedDate;
    final now = DateTime.now();
    final difference = now.difference(modifiedDate);

    String dateString;
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        dateString = '${difference.inMinutes}m ago';
      } else {
        dateString = '${difference.inHours}h ago';
      }
    } else if (difference.inDays < 7) {
      dateString = '${difference.inDays}d ago';
    } else {
      dateString = '${modifiedDate.day}/${modifiedDate.month}/${modifiedDate.year}';
    }
    
    subtitleParts.add(dateString);

    // Add permissions if not readable
    if (!file.permissions.readable) {
      subtitleParts.add('No read access');
    }

    return Text(
      subtitleParts.join(' • '),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Colors.grey[600],
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget? _buildTrailing(BuildContext context) {
    final List<Widget> trailingWidgets = [];

    // Add hidden indicator
    if (file.isHidden) {
      trailingWidgets.add(
        Icon(
          Icons.visibility_off,
          size: 16,
          color: Colors.grey[500],
        ),
      );
    }

    // Add permission indicators
    if (!file.permissions.writable) {
      trailingWidgets.add(
        Icon(
          Icons.lock,
          size: 16,
          color: Colors.orange[600],
        ),
      );
    }

    // Add more options button
    trailingWidgets.add(
      Icon(
        Icons.more_vert,
        color: Colors.grey[600],
      ),
    );

    if (trailingWidgets.length == 1) {
      return trailingWidgets.first;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: trailingWidgets
          .map((widget) => Padding(
                padding: const EdgeInsets.only(left: AppConstants.smallPadding),
                child: widget,
              ))
          .toList(),
    );
  }
}
