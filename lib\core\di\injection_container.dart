import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'injection_container.config.dart';
import '../../features/app_manager/app_manager_injection.dart';

final sl = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async => sl.init();

// Manual registration for external dependencies
Future<void> init() async {
  await configureDependencies();
  
  // Register external dependencies here if needed
  // Example:
  // sl.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
}
