import '../../domain/entities/file_operation.dart';

class FileOperationModel extends FileOperation {
  const FileOperationModel({
    required super.id,
    required super.type,
    required super.sourcePaths,
    required super.destinationPath,
    required super.status,
    super.progress,
    required super.startTime,
    super.endTime,
    super.errorMessage,
    super.totalFiles,
    super.processedFiles,
    super.totalBytes,
    super.processedBytes,
  });

  factory FileOperationModel.fromJson(Map<String, dynamic> json) {
    return FileOperationModel(
      id: json['id'] as String,
      type: FileOperationType.values[json['type'] as int],
      sourcePaths: (json['sourcePaths'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      destinationPath: json['destinationPath'] as String,
      status: FileOperationStatus.values[json['status'] as int],
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null 
          ? DateTime.parse(json['endTime'] as String)
          : null,
      errorMessage: json['errorMessage'] as String?,
      totalFiles: json['totalFiles'] as int? ?? 0,
      processedFiles: json['processedFiles'] as int? ?? 0,
      totalBytes: json['totalBytes'] as int? ?? 0,
      processedBytes: json['processedBytes'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'sourcePaths': sourcePaths,
      'destinationPath': destinationPath,
      'status': status.index,
      'progress': progress,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'errorMessage': errorMessage,
      'totalFiles': totalFiles,
      'processedFiles': processedFiles,
      'totalBytes': totalBytes,
      'processedBytes': processedBytes,
    };
  }

  @override
  FileOperationModel copyWith({
    String? id,
    FileOperationType? type,
    List<String>? sourcePaths,
    String? destinationPath,
    FileOperationStatus? status,
    double? progress,
    DateTime? startTime,
    DateTime? endTime,
    String? errorMessage,
    int? totalFiles,
    int? processedFiles,
    int? totalBytes,
    int? processedBytes,
  }) {
    return FileOperationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      sourcePaths: sourcePaths ?? this.sourcePaths,
      destinationPath: destinationPath ?? this.destinationPath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      errorMessage: errorMessage ?? this.errorMessage,
      totalFiles: totalFiles ?? this.totalFiles,
      processedFiles: processedFiles ?? this.processedFiles,
      totalBytes: totalBytes ?? this.totalBytes,
      processedBytes: processedBytes ?? this.processedBytes,
    );
  }
}
