import '../../domain/entities/network_device.dart';

class NetworkDeviceModel extends NetworkDevice {
  const NetworkDeviceModel({
    required super.ipAddress,
    required super.macAddress,
    required super.hostname,
    required super.deviceType,
    required super.isOnline,
    required super.lastSeen,
    super.manufacturer,
    super.openPorts,
    super.services,
  });

  factory NetworkDeviceModel.fromJson(Map<String, dynamic> json) {
    return NetworkDeviceModel(
      ipAddress: json['ipAddress'] as String,
      macAddress: json['macAddress'] as String,
      hostname: json['hostname'] as String,
      deviceType: DeviceType.values[json['deviceType'] as int],
      isOnline: json['isOnline'] as bool,
      lastSeen: DateTime.parse(json['lastSeen'] as String),
      manufacturer: json['manufacturer'] as String?,
      openPorts: (json['openPorts'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      services: (json['services'] as List<dynamic>?)
          ?.map((e) => NetworkService.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ipAddress': ipAddress,
      'macAddress': macAddress,
      'hostname': hostname,
      'deviceType': deviceType.index,
      'isOnline': isOnline,
      'lastSeen': lastSeen.toIso8601String(),
      'manufacturer': manufacturer,
      'openPorts': openPorts,
      'services': services?.map((e) => e.toJson()).toList(),
    };
  }

  NetworkDeviceModel copyWith({
    String? ipAddress,
    String? macAddress,
    String? hostname,
    DeviceType? deviceType,
    bool? isOnline,
    DateTime? lastSeen,
    String? manufacturer,
    List<int>? openPorts,
    List<NetworkService>? services,
  }) {
    return NetworkDeviceModel(
      ipAddress: ipAddress ?? this.ipAddress,
      macAddress: macAddress ?? this.macAddress,
      hostname: hostname ?? this.hostname,
      deviceType: deviceType ?? this.deviceType,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      manufacturer: manufacturer ?? this.manufacturer,
      openPorts: openPorts ?? this.openPorts,
      services: services ?? this.services,
    );
  }
}
