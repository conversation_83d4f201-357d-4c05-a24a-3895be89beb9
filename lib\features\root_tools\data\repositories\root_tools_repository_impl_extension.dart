// Extension file for RootToolsRepositoryImpl
// This file contains the remaining method implementations

import 'package:dartz/dartz.dart';
import '../datasources/root_tools_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

extension RootToolsRepositoryImplExtension on RootToolsDataSource {
  // System Tweaks
  Future<Either<Failure, void>> enableDeveloperOptionsExt() async {
    try {
      await enableDeveloperOptions();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> enableAdbDebuggingExt() async {
    try {
      await enableAdbDebugging();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> setAnimationScaleExt(double scale) async {
    try {
      await setAnimationScale(scale);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> setDpiExt(int dpi) async {
    try {
      await setDpi(dpi);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> setGovernorExt(String governor) async {
    try {
      await setGovernor(governor);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> setCpuFrequencyExt(int minFreq, int maxFreq) async {
    try {
      await setCpuFrequency(minFreq, maxFreq);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Security and Privacy
  Future<Either<Failure, void>> removeSystemAppsExt(List<String> packageNames) async {
    try {
      await removeSystemApps(packageNames);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> disableSystemAppsExt(List<String> packageNames) async {
    try {
      await disableSystemApps(packageNames);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> blockAdsExt() async {
    try {
      await blockAds();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> unblockAdsExt() async {
    try {
      await unblockAds();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> enableFirewallExt() async {
    try {
      await enableFirewall();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> disableFirewallExt() async {
    try {
      await disableFirewall();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, List<String>>> getFirewallRulesExt() async {
    try {
      final result = await getFirewallRules();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> addFirewallRuleExt(String rule) async {
    try {
      await addFirewallRule(rule);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> removeFirewallRuleExt(String rule) async {
    try {
      await removeFirewallRule(rule);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Performance Optimization
  Future<Either<Failure, void>> clearSystemCacheExt() async {
    try {
      await clearSystemCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> optimizeDatabaseExt() async {
    try {
      await optimizeDatabase();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> defragmentStorageExt() async {
    try {
      await defragmentStorage();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> trimFilesystemExt() async {
    try {
      await trimFilesystem();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> optimizeMemoryExt() async {
    try {
      await optimizeMemory();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Logs and Diagnostics
  Future<Either<Failure, List<String>>> getSystemLogsExt() async {
    try {
      final result = await getSystemLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, List<String>>> getKernelLogsExt() async {
    try {
      final result = await getKernelLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, List<String>>> getApplicationLogsExt() async {
    try {
      final result = await getApplicationLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> clearLogsExt() async {
    try {
      await clearLogs();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, Map<String, dynamic>>> runSystemDiagnosticsExt() async {
    try {
      final result = await runSystemDiagnostics();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Recovery and Repair
  Future<Either<Failure, void>> fixPermissionsExt() async {
    try {
      await fixPermissions();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> rebuildDalvikCacheExt() async {
    try {
      await rebuildDalvikCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> clearDalvikCacheExt() async {
    try {
      await clearDalvikCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> fixBootloopExt() async {
    try {
      await fixBootloop();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  Future<Either<Failure, void>> repairFilesystemExt(String partition) async {
    try {
      await repairFilesystem(partition);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
