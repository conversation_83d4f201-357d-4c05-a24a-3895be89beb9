import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/file_item.dart';
import '../../domain/repositories/file_repository.dart';
import '../datasources/file_system_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

@LazySingleton(as: FileRepository)
class FileRepositoryImpl implements FileRepository {
  final FileSystemDataSource dataSource;

  const FileRepositoryImpl(this.dataSource);

  @override
  Future<Either<Failure, List<FileItem>>> getDirectoryContents(String path) async {
    try {
      final result = await dataSource.getDirectoryContents(path);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, FileItem>> getFileInfo(String path) async {
    try {
      final result = await dataSource.getFileInfo(path);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> createDirectory(String path) async {
    try {
      final result = await dataSource.createDirectory(path);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteFile(String path) async {
    try {
      final result = await dataSource.deleteFile(path);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteDirectory(String path) async {
    try {
      final result = await dataSource.deleteDirectory(path);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> copyFile(String sourcePath, String destinationPath) async {
    try {
      final result = await dataSource.copyFile(sourcePath, destinationPath);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> moveFile(String sourcePath, String destinationPath) async {
    try {
      final result = await dataSource.moveFile(sourcePath, destinationPath);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> renameFile(String oldPath, String newPath) async {
    try {
      final result = await dataSource.renameFile(oldPath, newPath);
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getRootDirectories() async {
    try {
      final result = await dataSource.getRootDirectories();
      return Right(result);
    } on FileSystemException catch (e) {
      return Left(FileSystemFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> fileExists(String path) async {
    try {
      final result = await dataSource.fileExists(path);
      return Right(result);
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> directoryExists(String path) async {
    try {
      final result = await dataSource.directoryExists(path);
      return Right(result);
    } catch (e) {
      return Left(FileSystemFailure(message: 'Unexpected error: $e'));
    }
  }
}
