import 'package:equatable/equatable.dart';
import '../../../../core/utils/file_utils.dart';

class FileItem extends Equatable {
  final String name;
  final String path;
  final int size;
  final DateTime modifiedDate;
  final bool isDirectory;
  final bool isHidden;
  final FilePermissions permissions;
  final String? mimeType;
  final String? extension;
  final FileType? fileType;

  const FileItem({
    required this.name,
    required this.path,
    required this.size,
    required this.modifiedDate,
    required this.isDirectory,
    required this.isHidden,
    required this.permissions,
    this.mimeType,
    this.extension,
    this.fileType,
  });

  @override
  List<Object?> get props => [
        name,
        path,
        size,
        modifiedDate,
        isDirectory,
        isHidden,
        permissions,
        mimeType,
        extension,
        fileType,
      ];
}

class FilePermissions extends Equatable {
  final bool readable;
  final bool writable;
  final bool executable;

  const FilePermissions({
    required this.readable,
    required this.writable,
    required this.executable,
  });

  factory FilePermissions.fromJson(Map<String, dynamic> json) {
    return FilePermissions(
      readable: json['readable'] as bool,
      writable: json['writable'] as bool,
      executable: json['executable'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'readable': readable,
      'writable': writable,
      'executable': executable,
    };
  }

  String get permissionString {
    return '${readable ? 'r' : '-'}${writable ? 'w' : '-'}${executable ? 'x' : '-'}';
  }

  @override
  List<Object?> get props => [readable, writable, executable];
}
