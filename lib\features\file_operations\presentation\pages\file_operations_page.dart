import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/file_operations_bloc.dart';
import '../bloc/file_operations_event.dart';
import '../bloc/file_operations_state.dart';
import '../widgets/operation_progress_card.dart';
import '../widgets/compression_dialog.dart';
import '../widgets/conflict_resolution_dialog.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/injection_container.dart';

class FileOperationsPage extends StatelessWidget {
  const FileOperationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<FileOperationsBloc>()
        ..add(const LoadActiveOperationsEvent()),
      child: const FileOperationsView(),
    );
  }
}

class FileOperationsView extends StatelessWidget {
  const FileOperationsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('File Operations'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<FileOperationsBloc>().add(
                const LoadActiveOperationsEvent(),
              );
            },
          ),
        ],
      ),
      body: BlocConsumer<FileOperationsBloc, FileOperationsState>(
        listener: (context, state) {
          if (state is FileOperationsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          } else if (state is OperationStarted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${_getOperationName(state.type)} started'),
                backgroundColor: AppTheme.successColor,
              ),
            );
          } else if (state is ConflictsDetected) {
            _showConflictDialog(context, state);
          } else if (state is OperationCompleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${_getOperationName(state.operation.type)} completed'),
                backgroundColor: AppTheme.successColor,
              ),
            );
          } else if (state is OperationFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${_getOperationName(state.operation.type)} failed: ${state.operation.errorMessage}'),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is FileOperationsLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is FileOperationsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  ElevatedButton(
                    onPressed: () {
                      context.read<FileOperationsBloc>().add(
                        const LoadActiveOperationsEvent(),
                      );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is FileOperationsLoaded) {
            return DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'Active Operations'),
                      Tab(text: 'Completed Operations'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildActiveOperations(context, state.activeOperations),
                        _buildCompletedOperations(context, state.completedOperations),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }

          return const Center(
            child: Text('No operations'),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showOperationMenu(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildActiveOperations(BuildContext context, List<dynamic> operations) {
    if (operations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No active operations',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: operations.length,
      itemBuilder: (context, index) {
        final operation = operations[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: OperationProgressCard(
            operation: operation,
            onPause: () {
              context.read<FileOperationsBloc>().add(
                PauseOperationEvent(operation.id),
              );
            },
            onResume: () {
              context.read<FileOperationsBloc>().add(
                ResumeOperationEvent(operation.id),
              );
            },
            onCancel: () {
              context.read<FileOperationsBloc>().add(
                CancelOperationEvent(operation.id),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildCompletedOperations(BuildContext context, List<dynamic> operations) {
    if (operations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No completed operations',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: operations.length,
      itemBuilder: (context, index) {
        final operation = operations[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: OperationProgressCard(
            operation: operation,
            isCompleted: true,
          ),
        );
      },
    );
  }

  void _showOperationMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.compress),
              title: const Text('Compress Files'),
              onTap: () {
                Navigator.pop(context);
                _showCompressionDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.folder_zip),
              title: const Text('Extract Archive'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show extraction dialog
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCompressionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CompressionDialog(),
    );
  }

  void _showConflictDialog(BuildContext context, ConflictsDetected state) {
    showDialog(
      context: context,
      builder: (context) => ConflictResolutionDialog(
        conflictingFiles: state.conflictingFiles,
        sourcePaths: state.sourcePaths,
        destinationPath: state.destinationPath,
      ),
    );
  }

  String _getOperationName(dynamic type) {
    switch (type.toString()) {
      case 'FileOperationType.copy':
        return 'Copy';
      case 'FileOperationType.move':
        return 'Move';
      case 'FileOperationType.delete':
        return 'Delete';
      case 'FileOperationType.compress':
        return 'Compression';
      case 'FileOperationType.extract':
        return 'Extraction';
      default:
        return 'Operation';
    }
  }
}
