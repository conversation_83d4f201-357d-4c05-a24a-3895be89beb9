import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/cleanup_item.dart';
import '../repositories/cleaner_repository.dart';

@lazySingleton
class PerformCleanup {
  final CleanerRepository repository;

  const PerformCleanup(this.repository);

  ResultFuture<CleanupResult> call(List<CleanupItem> items) async {
    return await repository.performCleanup(items);
  }

  Stream<CleanupResult> stream(List<CleanupItem> items) {
    return repository.performCleanupStream(items);
  }
}
