import '../../domain/entities/search_result.dart';
import '../../domain/entities/search_filter.dart';
import '../../../file_browser/data/models/file_item_model.dart';

class SearchResultModel extends SearchResult {
  const SearchResultModel({
    required super.files,
    required super.query,
    required super.filter,
    required super.totalResults,
    required super.searchDuration,
    super.isComplete,
    super.errorMessage,
  });

  factory SearchResultModel.fromJson(Map<String, dynamic> json) {
    return SearchResultModel(
      files: (json['files'] as List<dynamic>)
          .map((e) => FileItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      query: json['query'] as String,
      filter: _filterFromJson(json['filter'] as Map<String, dynamic>),
      totalResults: json['totalResults'] as int,
      searchDuration: Duration(milliseconds: json['searchDurationMs'] as int),
      isComplete: json['isComplete'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'files': files.map((e) => (e as FileItemModel).toJson()).toList(),
      'query': query,
      'filter': _filterToJson(filter),
      'totalResults': totalResults,
      'searchDurationMs': searchDuration.inMilliseconds,
      'isComplete': isComplete,
      'errorMessage': errorMessage,
    };
  }

  static SearchFilter _filterFromJson(Map<String, dynamic> json) {
    return SearchFilter(
      query: json['query'] as String?,
      fileTypes: (json['fileTypes'] as List<dynamic>?)
          ?.map((e) => SearchFileType.values[e as int])
          .toList() ?? [],
      minSize: json['minSize'] as int?,
      maxSize: json['maxSize'] as int?,
      modifiedAfter: json['modifiedAfter'] != null 
          ? DateTime.parse(json['modifiedAfter'] as String)
          : null,
      modifiedBefore: json['modifiedBefore'] != null 
          ? DateTime.parse(json['modifiedBefore'] as String)
          : null,
      includeHidden: json['includeHidden'] as bool? ?? false,
      includeSystemFiles: json['includeSystemFiles'] as bool? ?? false,
      extensions: (json['extensions'] as List<dynamic>?)?.cast<String>(),
      searchPaths: (json['searchPaths'] as List<dynamic>?)?.cast<String>(),
      caseSensitive: json['caseSensitive'] as bool? ?? false,
      useRegex: json['useRegex'] as bool? ?? false,
      depth: SearchDepth.values[json['depth'] as int? ?? 3],
    );
  }

  static Map<String, dynamic> _filterToJson(SearchFilter filter) {
    return {
      'query': filter.query,
      'fileTypes': filter.fileTypes.map((e) => e.index).toList(),
      'minSize': filter.minSize,
      'maxSize': filter.maxSize,
      'modifiedAfter': filter.modifiedAfter?.toIso8601String(),
      'modifiedBefore': filter.modifiedBefore?.toIso8601String(),
      'includeHidden': filter.includeHidden,
      'includeSystemFiles': filter.includeSystemFiles,
      'extensions': filter.extensions,
      'searchPaths': filter.searchPaths,
      'caseSensitive': filter.caseSensitive,
      'useRegex': filter.useRegex,
      'depth': filter.depth.index,
    };
  }
}

class DuplicateGroupModel extends DuplicateGroup {
  const DuplicateGroupModel({
    required super.id,
    required super.duplicates,
    required super.totalSize,
    super.hash,
    required super.detectionMethod,
  });

  factory DuplicateGroupModel.fromJson(Map<String, dynamic> json) {
    return DuplicateGroupModel(
      id: json['id'] as String,
      duplicates: (json['duplicates'] as List<dynamic>)
          .map((e) => FileItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalSize: json['totalSize'] as int,
      hash: json['hash'] as String?,
      detectionMethod: DuplicateSearchMethod.values[json['detectionMethod'] as int],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'duplicates': duplicates.map((e) => (e as FileItemModel).toJson()).toList(),
      'totalSize': totalSize,
      'hash': hash,
      'detectionMethod': detectionMethod.index,
    };
  }
}

class DuplicateSearchResultModel extends DuplicateSearchResult {
  const DuplicateSearchResultModel({
    required super.duplicateGroups,
    required super.totalDuplicates,
    required super.totalWastedSpace,
    required super.searchDuration,
    required super.filter,
    super.isComplete,
    super.errorMessage,
  });

  factory DuplicateSearchResultModel.fromJson(Map<String, dynamic> json) {
    return DuplicateSearchResultModel(
      duplicateGroups: (json['duplicateGroups'] as List<dynamic>)
          .map((e) => DuplicateGroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalDuplicates: json['totalDuplicates'] as int,
      totalWastedSpace: json['totalWastedSpace'] as int,
      searchDuration: Duration(milliseconds: json['searchDurationMs'] as int),
      filter: _duplicateFilterFromJson(json['filter'] as Map<String, dynamic>),
      isComplete: json['isComplete'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'duplicateGroups': duplicateGroups.map((e) => (e as DuplicateGroupModel).toJson()).toList(),
      'totalDuplicates': totalDuplicates,
      'totalWastedSpace': totalWastedSpace,
      'searchDurationMs': searchDuration.inMilliseconds,
      'filter': _duplicateFilterToJson(filter),
      'isComplete': isComplete,
      'errorMessage': errorMessage,
    };
  }

  static DuplicateSearchFilter _duplicateFilterFromJson(Map<String, dynamic> json) {
    return DuplicateSearchFilter(
      searchPaths: (json['searchPaths'] as List<dynamic>).cast<String>(),
      method: DuplicateSearchMethod.values[json['method'] as int],
      minFileSize: json['minFileSize'] as int?,
      fileTypes: (json['fileTypes'] as List<dynamic>?)
          ?.map((e) => SearchFileType.values[e as int])
          .toList() ?? [SearchFileType.all],
      includeHidden: json['includeHidden'] as bool? ?? false,
      includeSystemFiles: json['includeSystemFiles'] as bool? ?? false,
    );
  }

  static Map<String, dynamic> _duplicateFilterToJson(DuplicateSearchFilter filter) {
    return {
      'searchPaths': filter.searchPaths,
      'method': filter.method.index,
      'minFileSize': filter.minFileSize,
      'fileTypes': filter.fileTypes.map((e) => e.index).toList(),
      'includeHidden': filter.includeHidden,
      'includeSystemFiles': filter.includeSystemFiles,
    };
  }
}
