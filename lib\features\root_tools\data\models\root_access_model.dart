import '../../domain/entities/root_access.dart';

class RootAccessModel extends RootAccess {
  const RootAccessModel({
    required super.isRooted,
    required super.isSuAvailable,
    required super.isBusyBoxAvailable,
    super.suVersion,
    super.busyBoxVersion,
    required super.rootMethod,
    required super.availableCommands,
    required super.systemProperties,
    required super.lastChecked,
  });

  factory RootAccessModel.fromJson(Map<String, dynamic> json) {
    return RootAccessModel(
      isRooted: json['isRooted'] as bool,
      isSuAvailable: json['isSuAvailable'] as bool,
      isBusyBoxAvailable: json['isBusyBoxAvailable'] as bool,
      suVersion: json['suVersion'] as String?,
      busyBoxVersion: json['busyBoxVersion'] as String?,
      rootMethod: RootMethod.values[json['rootMethod'] as int],
      availableCommands: (json['availableCommands'] as List<dynamic>).cast<String>(),
      systemProperties: Map<String, String>.from(json['systemProperties'] as Map),
      lastChecked: DateTime.parse(json['lastChecked'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isRooted': isRooted,
      'isSuAvailable': isSuAvailable,
      'isBusyBoxAvailable': isBusyBoxAvailable,
      'suVersion': suVersion,
      'busyBoxVersion': busyBoxVersion,
      'rootMethod': rootMethod.index,
      'availableCommands': availableCommands,
      'systemProperties': systemProperties,
      'lastChecked': lastChecked.toIso8601String(),
    };
  }

  factory RootAccessModel.mock() {
    return RootAccessModel(
      isRooted: true,
      isSuAvailable: true,
      isBusyBoxAvailable: true,
      suVersion: 'v2.5.1',
      busyBoxVersion: 'v1.35.0',
      rootMethod: RootMethod.magisk,
      availableCommands: [
        'su', 'busybox', 'mount', 'umount', 'chmod', 'chown', 'kill',
        'ps', 'ls', 'cat', 'grep', 'find', 'df', 'du', 'top', 'netstat',
        'iptables', 'service', 'reboot', 'shutdown', 'tar', 'gzip',
      ],
      systemProperties: {
        'ro.build.version.release': '13',
        'ro.build.version.sdk': '33',
        'ro.product.model': 'Mock Device',
        'ro.product.manufacturer': 'Mock Manufacturer',
        'ro.hardware': 'mock_hardware',
        'ro.secure': '0',
        'ro.debuggable': '1',
        'ro.adb.secure': '0',
      },
      lastChecked: DateTime.now(),
    );
  }
}

class SystemPartitionModel extends SystemPartition {
  const SystemPartitionModel({
    required super.name,
    required super.mountPoint,
    required super.fileSystem,
    required super.totalSize,
    required super.usedSize,
    required super.availableSize,
    required super.isReadOnly,
    required super.isMounted,
    required super.mountOptions,
  });

  factory SystemPartitionModel.fromJson(Map<String, dynamic> json) {
    return SystemPartitionModel(
      name: json['name'] as String,
      mountPoint: json['mountPoint'] as String,
      fileSystem: json['fileSystem'] as String,
      totalSize: json['totalSize'] as int,
      usedSize: json['usedSize'] as int,
      availableSize: json['availableSize'] as int,
      isReadOnly: json['isReadOnly'] as bool,
      isMounted: json['isMounted'] as bool,
      mountOptions: (json['mountOptions'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'mountPoint': mountPoint,
      'fileSystem': fileSystem,
      'totalSize': totalSize,
      'usedSize': usedSize,
      'availableSize': availableSize,
      'isReadOnly': isReadOnly,
      'isMounted': isMounted,
      'mountOptions': mountOptions,
    };
  }

  static List<SystemPartitionModel> mockPartitions() {
    return [
      SystemPartitionModel(
        name: 'system',
        mountPoint: '/system',
        fileSystem: 'ext4',
        totalSize: 4 * 1024 * 1024 * 1024, // 4GB
        usedSize: 3 * 1024 * 1024 * 1024, // 3GB
        availableSize: 1024 * 1024 * 1024, // 1GB
        isReadOnly: true,
        isMounted: true,
        mountOptions: ['ro', 'seclabel'],
      ),
      SystemPartitionModel(
        name: 'data',
        mountPoint: '/data',
        fileSystem: 'ext4',
        totalSize: 32 * 1024 * 1024 * 1024, // 32GB
        usedSize: 16 * 1024 * 1024 * 1024, // 16GB
        availableSize: 16 * 1024 * 1024 * 1024, // 16GB
        isReadOnly: false,
        isMounted: true,
        mountOptions: ['rw', 'seclabel', 'nosuid', 'nodev'],
      ),
      SystemPartitionModel(
        name: 'cache',
        mountPoint: '/cache',
        fileSystem: 'ext4',
        totalSize: 512 * 1024 * 1024, // 512MB
        usedSize: 128 * 1024 * 1024, // 128MB
        availableSize: 384 * 1024 * 1024, // 384MB
        isReadOnly: false,
        isMounted: true,
        mountOptions: ['rw', 'seclabel', 'nosuid', 'nodev'],
      ),
      SystemPartitionModel(
        name: 'vendor',
        mountPoint: '/vendor',
        fileSystem: 'ext4',
        totalSize: 1024 * 1024 * 1024, // 1GB
        usedSize: 800 * 1024 * 1024, // 800MB
        availableSize: 224 * 1024 * 1024, // 224MB
        isReadOnly: true,
        isMounted: true,
        mountOptions: ['ro', 'seclabel'],
      ),
    ];
  }
}

class SystemProcessModel extends SystemProcess {
  const SystemProcessModel({
    required super.pid,
    required super.ppid,
    required super.name,
    required super.command,
    required super.user,
    required super.cpuUsage,
    required super.memoryUsage,
    required super.state,
    required super.startTime,
    required super.openFiles,
  });

  factory SystemProcessModel.fromJson(Map<String, dynamic> json) {
    return SystemProcessModel(
      pid: json['pid'] as int,
      ppid: json['ppid'] as int,
      name: json['name'] as String,
      command: json['command'] as String,
      user: json['user'] as String,
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      memoryUsage: json['memoryUsage'] as int,
      state: ProcessState.values[json['state'] as int],
      startTime: DateTime.parse(json['startTime'] as String),
      openFiles: (json['openFiles'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pid': pid,
      'ppid': ppid,
      'name': name,
      'command': command,
      'user': user,
      'cpuUsage': cpuUsage,
      'memoryUsage': memoryUsage,
      'state': state.index,
      'startTime': startTime.toIso8601String(),
      'openFiles': openFiles,
    };
  }

  static List<SystemProcessModel> mockProcesses() {
    final now = DateTime.now();
    return [
      SystemProcessModel(
        pid: 1,
        ppid: 0,
        name: 'init',
        command: '/init',
        user: 'root',
        cpuUsage: 0.1,
        memoryUsage: 4 * 1024 * 1024, // 4MB
        state: ProcessState.running,
        startTime: now.subtract(const Duration(hours: 24)),
        openFiles: ['/dev/null', '/proc/1/fd'],
      ),
      SystemProcessModel(
        pid: 123,
        ppid: 1,
        name: 'system_server',
        command: 'system_server',
        user: 'system',
        cpuUsage: 5.2,
        memoryUsage: 256 * 1024 * 1024, // 256MB
        state: ProcessState.running,
        startTime: now.subtract(const Duration(hours: 23)),
        openFiles: ['/dev/binder', '/system/framework'],
      ),
      SystemProcessModel(
        pid: 456,
        ppid: 123,
        name: 'com.android.systemui',
        command: 'com.android.systemui',
        user: 'u0_a10',
        cpuUsage: 2.1,
        memoryUsage: 128 * 1024 * 1024, // 128MB
        state: ProcessState.running,
        startTime: now.subtract(const Duration(hours: 22)),
        openFiles: ['/dev/graphics', '/system/media'],
      ),
    ];
  }
}

class SystemServiceModel extends SystemService {
  const SystemServiceModel({
    required super.name,
    required super.description,
    required super.state,
    required super.isEnabled,
    required super.isSystemService,
    super.packageName,
    required super.dependencies,
    required super.properties,
  });

  factory SystemServiceModel.fromJson(Map<String, dynamic> json) {
    return SystemServiceModel(
      name: json['name'] as String,
      description: json['description'] as String,
      state: ServiceState.values[json['state'] as int],
      isEnabled: json['isEnabled'] as bool,
      isSystemService: json['isSystemService'] as bool,
      packageName: json['packageName'] as String?,
      dependencies: (json['dependencies'] as List<dynamic>).cast<String>(),
      properties: Map<String, dynamic>.from(json['properties'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'state': state.index,
      'isEnabled': isEnabled,
      'isSystemService': isSystemService,
      'packageName': packageName,
      'dependencies': dependencies,
      'properties': properties,
    };
  }

  static List<SystemServiceModel> mockServices() {
    return [
      SystemServiceModel(
        name: 'wifi',
        description: 'WiFi Service',
        state: ServiceState.active,
        isEnabled: true,
        isSystemService: true,
        dependencies: ['netd'],
        properties: {'interface': 'wlan0', 'status': 'connected'},
      ),
      SystemServiceModel(
        name: 'bluetooth',
        description: 'Bluetooth Service',
        state: ServiceState.active,
        isEnabled: true,
        isSystemService: true,
        dependencies: [],
        properties: {'adapter': 'hci0', 'status': 'enabled'},
      ),
      SystemServiceModel(
        name: 'location',
        description: 'Location Service',
        state: ServiceState.active,
        isEnabled: true,
        isSystemService: true,
        dependencies: ['wifi', 'gps'],
        properties: {'provider': 'gps', 'accuracy': 'high'},
      ),
    ];
  }
}

class RootCommandResultModel extends RootCommandResult {
  const RootCommandResultModel({
    required super.command,
    required super.exitCode,
    required super.output,
    required super.error,
    required super.executionTime,
    required super.timestamp,
    required super.isSuccess,
  });

  factory RootCommandResultModel.fromJson(Map<String, dynamic> json) {
    return RootCommandResultModel(
      command: json['command'] as String,
      exitCode: json['exitCode'] as int,
      output: json['output'] as String,
      error: json['error'] as String,
      executionTime: Duration(milliseconds: json['executionTimeMs'] as int),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isSuccess: json['isSuccess'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'command': command,
      'exitCode': exitCode,
      'output': output,
      'error': error,
      'executionTimeMs': executionTime.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
      'isSuccess': isSuccess,
    };
  }
}
