# Localization System Configuration
# نظام إعدادات الترجمة والتدويل

# Basic Configuration
name: "localization"
version: "1.0.0"
description: "Comprehensive localization system for HD File Explorer"

# Supported Languages
languages:
  primary:
    - code: "ar"
      name: "Arabic"
      native_name: "العربية"
      flag: "🇸🇦"
      rtl: true
      locale: "ar_SA"
      status: "complete"
      
    - code: "en"
      name: "English"
      native_name: "English"
      flag: "🇺🇸"
      rtl: false
      locale: "en_US"
      status: "complete"
      
  secondary:
    - code: "fr"
      name: "French"
      native_name: "Français"
      flag: "🇫🇷"
      rtl: false
      locale: "fr_FR"
      status: "in_progress"
      
    - code: "es"
      name: "Spanish"
      native_name: "Español"
      flag: "🇪🇸"
      rtl: false
      locale: "es_ES"
      status: "planned"

# Default Settings
defaults:
  language: "en"
  auto_detect: false
  show_native_names: true
  rtl_support: true
  cache_enabled: true
  validation_enabled: true

# Performance Settings
performance:
  lazy_loading: true
  preload_primary: true
  compression: false
  max_memory_mb: 50
  batch_size: 100
  cache_cleanup_minutes: 30

# File Paths
paths:
  translations: "assets/translations"
  fonts: "assets/fonts"
  cache: "cache/translations"
  logs: "logs/translations"
  exports: "exports/translations"

# File Extensions
extensions:
  translation: ".json"
  font: ".ttf"
  cache: ".cache"
  log: ".log"
  export: ".json"

# Font Configurations
fonts:
  ar:
    family: "Cairo"
    size: 14.0
    weight: "normal"
    height: 1.5
    
  en:
    family: "Roboto"
    size: 14.0
    weight: "normal"
    height: 1.4
    
  fa:
    family: "Vazir"
    size: 14.0
    weight: "normal"
    height: 1.5

# Layout Configurations
layout:
  rtl:
    text_direction: "rtl"
    text_align: "right"
    icon_direction: "reversed"
    padding_direction: "reversed"
    
  ltr:
    text_direction: "ltr"
    text_align: "left"
    icon_direction: "normal"
    padding_direction: "normal"

# Number Formatting
number_format:
  ar:
    use_arabic_digits: false
    decimal_separator: "."
    thousands_separator: ","
    currency_symbol: "ر.س"
    currency_position: "after"
    
  en:
    use_arabic_digits: false
    decimal_separator: "."
    thousands_separator: ","
    currency_symbol: "$"
    currency_position: "before"

# Date Formatting
date_format:
  ar:
    date_format: "dd/MM/yyyy"
    time_format: "HH:mm"
    datetime_format: "dd/MM/yyyy HH:mm"
    use_24_hour: true
    first_day_of_week: 6  # Saturday
    
  en:
    date_format: "MM/dd/yyyy"
    time_format: "hh:mm a"
    datetime_format: "MM/dd/yyyy hh:mm a"
    use_24_hour: false
    first_day_of_week: 0  # Sunday

# Pluralization Rules
pluralization:
  ar: ["zero", "one", "two", "few", "many", "other"]
  en: ["one", "other"]
  ru: ["one", "few", "many", "other"]
  pl: ["one", "few", "many", "other"]

# Validation Rules
validation:
  max_key_length: 100
  max_value_length: 1000
  allowed_key_pattern: "^[a-zA-Z0-9._-]+$"
  required_keys:
    - "app.name"
    - "common.ok"
    - "common.cancel"
  forbidden_keys:
    - "password"
    - "secret"
    - "token"

# Security Settings
security:
  validate_sources: true
  enable_encryption: false
  allow_custom_translations: true
  enable_audit: false
  max_translation_length: 10000

# Debug Settings
debug:
  show_translation_keys: false
  show_missing_translations: true
  log_translation_usage: false
  enable_metrics: false
  show_language_info: false

# Accessibility Settings
accessibility:
  screen_reader_support: true
  high_contrast_mode: false
  large_text_mode: false
  voice_navigation: false
  gesture_navigation: true

# Features
features:
  - "multi_language_support"
  - "rtl_support"
  - "dynamic_language_switching"
  - "translation_caching"
  - "custom_translations"
  - "language_detection"
  - "font_optimization"
  - "layout_mirroring"
  - "number_formatting"
  - "date_formatting"
  - "pluralization"
  - "parameter_substitution"
  - "translation_validation"
  - "export_import"

# Translation Categories
categories:
  - name: "app"
    description: "Application-wide translations"
    priority: "high"
    
  - name: "common"
    description: "Common UI elements"
    priority: "high"
    
  - name: "navigation"
    description: "Navigation elements"
    priority: "high"
    
  - name: "dashboard"
    description: "Dashboard specific translations"
    priority: "medium"
    
  - name: "file_browser"
    description: "File browser translations"
    priority: "medium"
    
  - name: "settings"
    description: "Settings page translations"
    priority: "medium"
    
  - name: "root_tools"
    description: "Root tools translations"
    priority: "low"

# Quality Assurance
qa:
  translation_review_required: true
  native_speaker_review: true
  context_validation: true
  ui_testing_required: true
  automated_testing: true

# Maintenance
maintenance:
  auto_cleanup_cache: true
  cleanup_interval_hours: 24
  backup_translations: true
  backup_interval_days: 7
  log_retention_days: 30

# Monitoring
monitoring:
  track_usage: true
  track_performance: true
  track_errors: true
  report_missing_translations: true
  analytics_enabled: false

# Integration
integration:
  bloc_pattern: true
  dependency_injection: true
  shared_preferences: true
  asset_loading: true
  hot_reload_support: true
