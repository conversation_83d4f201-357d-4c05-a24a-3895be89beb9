import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../models/language.dart';
import '../bloc/language_bloc.dart';
import '../bloc/language_event.dart';
import '../bloc/language_state.dart';
import '../widgets/language_selector.dart';
import '../extensions/translation_extensions.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/loading_overlay.dart';

/// صفحة إعدادات اللغة
class LanguageSettingsPage extends StatefulWidget {
  const LanguageSettingsPage({super.key});

  @override
  State<LanguageSettingsPage> createState() => _LanguageSettingsPageState();
}

class _LanguageSettingsPageState extends State<LanguageSettingsPage> {
  bool _autoDetectLanguage = false;
  bool _showNativeNames = true;
  bool _enableRTLSupport = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    // تحميل الإعدادات المحفوظة
    context.read<LanguageBloc>().add(const LoadLanguagePreferencesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: context.tr('settings.language'),
        subtitle: context.tr('settings.language_description'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showLanguageInfo(),
            tooltip: context.tr('common.info'),
          ),
        ],
      ),
      body: BlocConsumer<LanguageBloc, LanguageState>(
        listener: _handleStateChanges,
        builder: (context, state) {
          return LoadingOverlay(
            isLoading: state is LanguageLoading,
            loadingMessage: _getLoadingMessage(state),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Language Selector
                  _buildLanguageSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Language Settings
                  _buildLanguageSettings(),
                  
                  const SizedBox(height: 24),
                  
                  // Advanced Settings
                  _buildAdvancedSettings(),
                  
                  const SizedBox(height: 24),
                  
                  // Language Information
                  _buildLanguageInformation(),
                  
                  const SizedBox(height: 24),
                  
                  // Actions
                  _buildActions(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.language,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  context.tr('settings.language'),
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const LanguageSelector(
              showFlags: true,
              showNativeNames: true,
              isCompact: false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('settings.general'),
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Auto Detect Language
            SwitchListTile(
              title: Text(context.tr('settings.auto_detect_language')),
              subtitle: Text(context.tr('settings.auto_detect_language_description')),
              value: _autoDetectLanguage,
              onChanged: (value) {
                setState(() {
                  _autoDetectLanguage = value;
                });
                if (value) {
                  context.read<LanguageBloc>().add(const DetectSystemLanguageEvent());
                }
              },
              secondary: const Icon(Icons.auto_awesome),
            ),
            
            const Divider(),
            
            // Show Native Names
            SwitchListTile(
              title: Text(context.tr('settings.show_native_names')),
              subtitle: Text(context.tr('settings.show_native_names_description')),
              value: _showNativeNames,
              onChanged: (value) {
                setState(() {
                  _showNativeNames = value;
                });
              },
              secondary: const Icon(Icons.translate),
            ),
            
            const Divider(),
            
            // Enable RTL Support
            SwitchListTile(
              title: Text(context.tr('settings.enable_rtl_support')),
              subtitle: Text(context.tr('settings.enable_rtl_support_description')),
              value: _enableRTLSupport,
              onChanged: (value) {
                setState(() {
                  _enableRTLSupport = value;
                });
              },
              secondary: const Icon(Icons.format_textdirection_r_to_l),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('settings.advanced'),
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Reload Translations
            ListTile(
              leading: const Icon(Icons.refresh),
              title: Text(context.tr('settings.reload_translations')),
              subtitle: Text(context.tr('settings.reload_translations_description')),
              onTap: () {
                context.read<LanguageBloc>().add(const ReloadTranslationsEvent());
              },
            ),
            
            const Divider(),
            
            // Clear Translation Cache
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: Text(context.tr('settings.clear_translation_cache')),
              subtitle: Text(context.tr('settings.clear_translation_cache_description')),
              onTap: () => _showClearCacheDialog(),
            ),
            
            const Divider(),
            
            // Export Translations
            ListTile(
              leading: const Icon(Icons.file_download),
              title: Text(context.tr('settings.export_translations')),
              subtitle: Text(context.tr('settings.export_translations_description')),
              onTap: () {
                context.read<LanguageBloc>().add(const ExportTranslationsEvent());
              },
            ),
            
            const Divider(),
            
            // Reset to Default
            ListTile(
              leading: Icon(Icons.restore, color: Colors.orange),
              title: Text(
                context.tr('settings.reset_to_default'),
                style: TextStyle(color: Colors.orange),
              ),
              subtitle: Text(context.tr('settings.reset_to_default_description')),
              onTap: () => _showResetDialog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageInformation() {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        Language currentLanguage = SupportedLanguages.defaultLanguage;
        
        if (state is LanguageLoaded) {
          currentLanguage = state.language;
        } else if (state is LanguageChanged) {
          currentLanguage = state.currentLanguage;
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('settings.language_information'),
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                _buildInfoRow(
                  context.tr('settings.current_language'),
                  currentLanguage.nativeName,
                  Icons.language,
                ),
                
                _buildInfoRow(
                  context.tr('settings.language_code'),
                  currentLanguage.code.toUpperCase(),
                  Icons.code,
                ),
                
                _buildInfoRow(
                  context.tr('settings.text_direction'),
                  currentLanguage.isRTL 
                      ? context.tr('settings.right_to_left')
                      : context.tr('settings.left_to_right'),
                  Icons.format_textdirection_l_to_r,
                ),
                
                _buildInfoRow(
                  context.tr('settings.locale'),
                  currentLanguage.locale.toString(),
                  Icons.location_on,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _saveSettings(),
            icon: const Icon(Icons.save),
            label: Text(context.tr('settings.save_changes')),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _resetSettings(),
            icon: const Icon(Icons.refresh),
            label: Text(context.tr('settings.reset')),
          ),
        ),
      ],
    );
  }

  void _handleStateChanges(BuildContext context, LanguageState state) {
    if (state is LanguageChanged) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('settings.language_changed')),
          backgroundColor: Colors.green,
        ),
      );
    } else if (state is LanguageError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
        ),
      );
    } else if (state is TranslationsExported) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('settings.translations_exported')),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  String _getLoadingMessage(LanguageState state) {
    if (state is LanguageLoading) {
      return state.message ?? context.tr('common.loading');
    }
    return context.tr('common.loading');
  }

  void _showLanguageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('settings.language_information')),
        content: const LanguageInfo(),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('common.close')),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('settings.clear_translation_cache')),
        content: Text(context.tr('settings.clear_cache_confirmation')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<LanguageBloc>().add(const ClearTranslationCacheEvent());
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.clear')),
          ),
        ],
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('settings.reset_to_default')),
        content: Text(context.tr('settings.reset_confirmation')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<LanguageBloc>().add(const ResetToDefaultLanguageEvent());
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: Text(context.tr('common.reset')),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    final preferences = {
      'autoDetectLanguage': _autoDetectLanguage,
      'showNativeNames': _showNativeNames,
      'enableRTLSupport': _enableRTLSupport,
    };
    
    context.read<LanguageBloc>().add(SaveLanguagePreferencesEvent(preferences));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('settings.changes_saved')),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetSettings() {
    setState(() {
      _autoDetectLanguage = false;
      _showNativeNames = true;
      _enableRTLSupport = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('settings.settings_reset')),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
