// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:hd_file_explore/features/dashboard/data/datasources/storage_datasource.dart'
    as _i616;
import 'package:hd_file_explore/features/dashboard/data/repositories/storage_repository_impl.dart'
    as _i729;
import 'package:hd_file_explore/features/dashboard/domain/repositories/storage_repository.dart'
    as _i727;
import 'package:hd_file_explore/features/dashboard/domain/usecases/get_storage_info.dart'
    as _i4;
import 'package:hd_file_explore/features/dashboard/presentation/bloc/dashboard_bloc.dart'
    as _i774;
import 'package:hd_file_explore/features/file_browser/data/datasources/file_system_datasource.dart'
    as _i498;
import 'package:hd_file_explore/features/file_browser/data/repositories/file_repository_impl.dart'
    as _i883;
import 'package:hd_file_explore/features/file_browser/domain/repositories/file_repository.dart'
    as _i459;
import 'package:hd_file_explore/features/file_browser/domain/usecases/create_directory.dart'
    as _i519;
import 'package:hd_file_explore/features/file_browser/domain/usecases/delete_file.dart'
    as _i243;
import 'package:hd_file_explore/features/file_browser/domain/usecases/get_directory_contents.dart'
    as _i507;
import 'package:hd_file_explore/features/file_browser/presentation/bloc/file_browser_bloc.dart'
    as _i1009;
import 'package:hd_file_explore/features/file_operations/data/datasources/file_operations_datasource.dart'
    as _i690;
import 'package:hd_file_explore/features/file_operations/data/repositories/file_operations_repository_impl.dart'
    as _i739;
import 'package:hd_file_explore/features/file_operations/domain/repositories/file_operations_repository.dart'
    as _i554;
import 'package:hd_file_explore/features/file_operations/domain/usecases/compress_files.dart'
    as _i221;
import 'package:hd_file_explore/features/file_operations/domain/usecases/copy_files.dart'
    as _i513;
import 'package:hd_file_explore/features/file_operations/presentation/bloc/file_operations_bloc.dart'
    as _i322;
import 'package:hd_file_explore/features/search/data/datasources/search_datasource.dart'
    as _i575;
import 'package:hd_file_explore/features/search/data/repositories/search_repository_impl.dart'
    as _i466;
import 'package:hd_file_explore/features/search/domain/repositories/search_repository.dart'
    as _i893;
import 'package:hd_file_explore/features/search/domain/usecases/find_duplicates.dart'
    as _i1011;
import 'package:hd_file_explore/features/search/domain/usecases/search_files.dart'
    as _i754;
import 'package:hd_file_explore/features/search/presentation/bloc/search_bloc.dart'
    as _i315;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    gh.lazySingleton<_i575.SearchDataSource>(
      () => _i575.SearchDataSourceImpl(),
    );
    gh.lazySingleton<_i498.FileSystemDataSource>(
      () => _i498.FileSystemDataSourceImpl(),
    );
    gh.lazySingleton<_i616.StorageDataSource>(
      () => _i616.StorageDataSourceImpl(),
    );
    gh.lazySingleton<_i690.FileOperationsDataSource>(
      () => _i690.FileOperationsDataSourceImpl(),
    );
    gh.lazySingleton<_i893.SearchRepository>(
      () => _i466.SearchRepositoryImpl(gh<_i575.SearchDataSource>()),
    );
    gh.lazySingleton<_i459.FileRepository>(
      () => _i883.FileRepositoryImpl(gh<_i498.FileSystemDataSource>()),
    );
    gh.lazySingleton<_i727.StorageRepository>(
      () => _i729.StorageRepositoryImpl(gh<_i616.StorageDataSource>()),
    );
    gh.lazySingleton<_i519.CreateDirectory>(
      () => _i519.CreateDirectory(gh<_i459.FileRepository>()),
    );
    gh.lazySingleton<_i243.DeleteFile>(
      () => _i243.DeleteFile(gh<_i459.FileRepository>()),
    );
    gh.lazySingleton<_i507.GetDirectoryContents>(
      () => _i507.GetDirectoryContents(gh<_i459.FileRepository>()),
    );
    gh.factory<_i1009.FileBrowserBloc>(
      () => _i1009.FileBrowserBloc(
        gh<_i507.GetDirectoryContents>(),
        gh<_i519.CreateDirectory>(),
        gh<_i243.DeleteFile>(),
      ),
    );
    gh.lazySingleton<_i554.FileOperationsRepository>(
      () => _i739.FileOperationsRepositoryImpl(
        gh<_i690.FileOperationsDataSource>(),
      ),
    );
    gh.lazySingleton<_i4.GetStorageInfo>(
      () => _i4.GetStorageInfo(gh<_i727.StorageRepository>()),
    );
    gh.lazySingleton<_i221.CompressFiles>(
      () => _i221.CompressFiles(gh<_i554.FileOperationsRepository>()),
    );
    gh.lazySingleton<_i513.CopyFiles>(
      () => _i513.CopyFiles(gh<_i554.FileOperationsRepository>()),
    );
    gh.lazySingleton<_i1011.FindDuplicates>(
      () => _i1011.FindDuplicates(gh<_i893.SearchRepository>()),
    );
    gh.lazySingleton<_i754.SearchFiles>(
      () => _i754.SearchFiles(gh<_i893.SearchRepository>()),
    );
    gh.factory<_i774.DashboardBloc>(
      () => _i774.DashboardBloc(gh<_i4.GetStorageInfo>()),
    );
    gh.factory<_i322.FileOperationsBloc>(
      () => _i322.FileOperationsBloc(
        gh<_i513.CopyFiles>(),
        gh<_i221.CompressFiles>(),
        gh<_i554.FileOperationsRepository>(),
      ),
    );
    gh.factory<_i315.SearchBloc>(
      () => _i315.SearchBloc(
        gh<_i754.SearchFiles>(),
        gh<_i1011.FindDuplicates>(),
        gh<_i893.SearchRepository>(),
      ),
    );
    return this;
  }
}
