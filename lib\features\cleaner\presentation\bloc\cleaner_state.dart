import 'package:equatable/equatable.dart';
import '../../domain/entities/cleanup_item.dart';

abstract class CleanerState extends Equatable {
  const CleanerState();

  @override
  List<Object?> get props => [];
}

class CleanerInitial extends CleanerState {
  const CleanerInitial();
}

class SystemAnalysisLoading extends CleanerState {
  const SystemAnalysisLoading();
}

class SystemAnalysisInProgress extends CleanerState {
  final SystemAnalysis partialAnalysis;

  const SystemAnalysisInProgress(this.partialAnalysis);

  @override
  List<Object?> get props => [partialAnalysis];
}

class SystemAnalysisCompleted extends CleanerState {
  final SystemAnalysis analysis;

  const SystemAnalysisCompleted(this.analysis);

  @override
  List<Object?> get props => [analysis];
}

class SystemAnalysisError extends CleanerState {
  final String message;

  const SystemAnalysisError(this.message);

  @override
  List<Object?> get props => [message];
}

class CleanupLoading extends CleanerState {
  const CleanupLoading();
}

class CleanupInProgress extends CleanerState {
  final CleanupResult partialResult;

  const CleanupInProgress(this.partialResult);

  @override
  List<Object?> get props => [partialResult];
}

class CleanupCompleted extends CleanerState {
  final CleanupResult result;

  const CleanupCompleted(this.result);

  @override
  List<Object?> get props => [result];
}

class CleanupError extends CleanerState {
  final String message;

  const CleanupError(this.message);

  @override
  List<Object?> get props => [message];
}

class CleanupItemsUpdated extends CleanerState {
  final List<CleanupItem> items;

  const CleanupItemsUpdated(this.items);

  @override
  List<Object?> get props => [items];
}

class CleanupHistoryLoaded extends CleanerState {
  final List<CleanupResult> history;

  const CleanupHistoryLoaded(this.history);

  @override
  List<Object?> get props => [history];
}

class SpecificCleanupItemsFound extends CleanerState {
  final CleanupType type;
  final List<CleanupItem> items;

  const SpecificCleanupItemsFound({
    required this.type,
    required this.items,
  });

  @override
  List<Object?> get props => [type, items];
}
