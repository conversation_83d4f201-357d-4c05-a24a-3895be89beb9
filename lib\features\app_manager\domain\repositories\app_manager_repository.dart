import '../../../../core/utils/typedef.dart';
import '../entities/app_info.dart';

abstract class AppManagerRepository {
  // App Discovery
  ResultFuture<List<AppInfo>> getAllApps();
  ResultFuture<List<AppInfo>> getUserApps();
  ResultFuture<List<AppInfo>> getSystemApps();
  ResultFuture<List<AppInfo>> getRunningApps();
  ResultFuture<AppInfo> getAppInfo(String packageName);
  
  // App Operations
  ResultVoid uninstallApp(String packageName);
  ResultVoid disableApp(String packageName);
  ResultVoid enableApp(String packageName);
  ResultVoid clearAppData(String packageName);
  ResultVoid clearAppCache(String packageName);
  ResultVoid forceStopApp(String packageName);
  ResultVoid launchApp(String packageName);
  
  // Batch Operations
  ResultFuture<AppBatchOperation> startBatchOperation(
    AppBatchOperationType type,
    List<String> packageNames,
  );
  Stream<AppBatchOperation> getBatchOperationStream(String operationId);
  ResultVoid cancelBatchOperation(String operationId);
  
  // App Usage Statistics
  ResultFuture<AppUsageStats> getAppUsageStats(String packageName);
  ResultFuture<List<AppUsageStats>> getAllUsageStats();
  ResultFuture<Map<String, Duration>> getDailyUsageStats(DateTime date);
  
  // App Backup & Restore
  ResultFuture<String> backupApp(String packageName, String backupPath);
  ResultVoid restoreApp(String backupPath);
  ResultFuture<List<String>> getAvailableBackups();
  
  // App Permissions
  ResultFuture<List<String>> getAppPermissions(String packageName);
  ResultFuture<Map<String, bool>> getPermissionStatus(String packageName);
  ResultVoid grantPermission(String packageName, String permission);
  ResultVoid revokePermission(String packageName, String permission);
  
  // App Analysis
  ResultFuture<Map<String, int>> getAppSizeAnalysis();
  ResultFuture<List<AppInfo>> getLargestApps(int limit);
  ResultFuture<List<AppInfo>> getUnusedApps(int daysUnused);
  ResultFuture<List<AppInfo>> getAppsWithPermission(String permission);
  
  // App Installation
  ResultVoid installApk(String apkPath);
  Stream<double> getInstallationProgress(String apkPath);
  ResultFuture<bool> verifyApkSignature(String apkPath);
  
  // App Updates
  ResultFuture<List<AppInfo>> getUpdatableApps();
  ResultVoid updateApp(String packageName);
  ResultVoid updateAllApps();
  
  // System Integration
  ResultFuture<List<AppInfo>> getDefaultApps();
  ResultVoid setDefaultApp(String packageName, String category);
  ResultVoid clearDefaultApp(String category);
}
