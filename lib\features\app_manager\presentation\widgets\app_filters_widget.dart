import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/app_manager_bloc.dart';
import '../bloc/app_manager_event.dart';
import '../bloc/app_manager_state.dart';
import '../../domain/entities/app_info.dart';
import '../../../../core/constants/app_constants.dart';

class AppFiltersWidget extends StatefulWidget {
  const AppFiltersWidget({super.key});

  @override
  State<AppFiltersWidget> createState() => _AppFiltersWidgetState();
}

class _AppFiltersWidgetState extends State<AppFiltersWidget> {
  AppType? selectedType;
  AppStatus? selectedStatus;
  bool? selectedEnabled;

  @override
  void initState() {
    super.initState();
    final state = context.read<AppManagerBloc>().state;
    if (state is AppsLoaded) {
      selectedType = state.filterType;
      selectedStatus = state.filterStatus;
      selectedEnabled = state.filterEnabled;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Apps'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // App Type Filter
            Text(
              'App Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: selectedType == null,
                  onSelected: (selected) {
                    setState(() {
                      selectedType = selected ? null : selectedType;
                    });
                  },
                ),
                ...AppType.values.map((type) => FilterChip(
                  label: Text(_getAppTypeName(type)),
                  selected: selectedType == type,
                  onSelected: (selected) {
                    setState(() {
                      selectedType = selected ? type : null;
                    });
                  },
                )),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // App Status Filter
            Text(
              'App Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: selectedStatus == null,
                  onSelected: (selected) {
                    setState(() {
                      selectedStatus = selected ? null : selectedStatus;
                    });
                  },
                ),
                ...AppStatus.values.map((status) => FilterChip(
                  label: Text(_getAppStatusName(status)),
                  selected: selectedStatus == status,
                  onSelected: (selected) {
                    setState(() {
                      selectedStatus = selected ? status : null;
                    });
                  },
                )),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Enabled Status Filter
            Text(
              'Enabled Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: selectedEnabled == null,
                  onSelected: (selected) {
                    setState(() {
                      selectedEnabled = selected ? null : selectedEnabled;
                    });
                  },
                ),
                FilterChip(
                  label: const Text('Enabled'),
                  selected: selectedEnabled == true,
                  onSelected: (selected) {
                    setState(() {
                      selectedEnabled = selected ? true : null;
                    });
                  },
                ),
                FilterChip(
                  label: const Text('Disabled'),
                  selected: selectedEnabled == false,
                  onSelected: (selected) {
                    setState(() {
                      selectedEnabled = selected ? false : null;
                    });
                  },
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              selectedType = null;
              selectedStatus = null;
              selectedEnabled = null;
            });
          },
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<AppManagerBloc>().add(
              FilterAppsEvent(
                type: selectedType,
                status: selectedStatus,
                isEnabled: selectedEnabled,
              ),
            );
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  String _getAppTypeName(AppType type) {
    switch (type) {
      case AppType.user:
        return 'User Apps';
      case AppType.system:
        return 'System Apps';
      case AppType.updated:
        return 'Updated Apps';
      case AppType.disabled:
        return 'Disabled Apps';
    }
  }

  String _getAppStatusName(AppStatus status) {
    switch (status) {
      case AppStatus.running:
        return 'Running';
      case AppStatus.stopped:
        return 'Stopped';
      case AppStatus.cached:
        return 'Cached';
      case AppStatus.background:
        return 'Background';
    }
  }
}
