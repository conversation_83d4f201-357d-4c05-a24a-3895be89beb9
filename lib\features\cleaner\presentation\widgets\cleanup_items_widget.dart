import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/cleaner_bloc.dart';
import '../bloc/cleaner_event.dart';
import '../bloc/cleaner_state.dart';
import '../../domain/entities/cleanup_item.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/file_utils.dart';

class CleanupItemsWidget extends StatelessWidget {
  const CleanupItemsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CleanerBloc, CleanerState>(
      builder: (context, state) {
        if (state is CleanerInitial) {
          return const EmptyCleanupState();
        }
        
        if (state is SystemAnalysisCompleted) {
          return _buildCleanupItems(context, state.analysis.cleanupItems);
        }
        
        if (state is CleanupItemsUpdated) {
          return _buildCleanupItems(context, state.items);
        }
        
        if (state is CleanupLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: AppConstants.defaultPadding),
                Text('Cleaning up files...'),
              ],
            ),
          );
        }
        
        if (state is CleanupInProgress) {
          return _buildCleanupProgress(context, state.partialResult);
        }
        
        if (state is CleanupCompleted) {
          return _buildCleanupResult(context, state.result);
        }
        
        if (state is CleanupError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'Cleanup Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          );
        }
        
        return const EmptyCleanupState();
      },
    );
  }

  Widget _buildCleanupItems(BuildContext context, List<CleanupItem> items) {
    if (items.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.green,
            ),
            SizedBox(height: AppConstants.defaultPadding),
            Text(
              'System is Clean!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            SizedBox(height: AppConstants.smallPadding),
            Text(
              'No cleanup opportunities found',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    final selectedItems = items.where((item) => item.isSelected).toList();
    final totalSelectedSize = selectedItems.fold<int>(
      0, 
      (sum, item) => sum + item.totalSize,
    );

    return Column(
      children: [
        // Header with selection controls
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${items.length} cleanup items found',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      context.read<CleanerBloc>().add(
                        const SelectAllCleanupItemsEvent(isSelected: true),
                      );
                    },
                    child: const Text('Select All'),
                  ),
                  TextButton(
                    onPressed: () {
                      context.read<CleanerBloc>().add(
                        const SelectAllCleanupItemsEvent(isSelected: false),
                      );
                    },
                    child: const Text('Clear All'),
                  ),
                ],
              ),
              
              if (selectedItems.isNotEmpty) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${selectedItems.length} items selected • ${FileUtils.formatFileSize(totalSelectedSize)} to clean',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _showCleanupConfirmation(context, selectedItems),
                      icon: const Icon(Icons.cleaning_services),
                      label: const Text('Clean Now'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.successColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        
        // Items list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: CleanupItemCard(
                  item: item,
                  onToggle: (isSelected) {
                    context.read<CleanerBloc>().add(
                      ToggleCleanupItemEvent(
                        itemId: item.id,
                        isSelected: isSelected,
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCleanupProgress(BuildContext context, CleanupResult result) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Cleaning up files...',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Deleted ${result.totalDeletedCount} files',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            'Freed ${FileUtils.formatFileSize(result.totalDeletedSize)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCleanupResult(BuildContext context, CleanupResult result) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 64,
            color: AppTheme.successColor,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Cleanup Completed!',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Deleted ${result.totalDeletedCount} files',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            'Freed ${FileUtils.formatFileSize(result.totalDeletedSize)} of space',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          if (result.failedDeletions.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '${result.failedDeletions.length} files could not be deleted',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.warningColor,
              ),
            ),
          ],
          
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton(
            onPressed: () {
              context.read<CleanerBloc>().add(const StartSystemAnalysisEvent());
            },
            child: const Text('Analyze Again'),
          ),
        ],
      ),
    );
  }

  void _showCleanupConfirmation(BuildContext context, List<CleanupItem> items) {
    final totalSize = items.fold<int>(0, (sum, item) => sum + item.totalSize);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Cleanup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete ${items.length} items?',
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'This will free up ${FileUtils.formatFileSize(totalSize)} of space.',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<CleanerBloc>().add(StartCleanupEvent(items));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class CleanupItemCard extends StatelessWidget {
  final CleanupItem item;
  final Function(bool) onToggle;

  const CleanupItemCard({
    super.key,
    required this.item,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: CheckboxListTile(
        value: item.isSelected,
        onChanged: (value) => onToggle(value ?? false),
        title: Text(
          item.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(item.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  _getCleanupTypeIcon(item.type),
                  size: 16,
                  color: _getPriorityColor(item.priority),
                ),
                const SizedBox(width: 4),
                Text(
                  '${item.fileCount} files • ${FileUtils.formatFileSize(item.totalSize)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getPriorityColor(item.priority),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (!item.isSafeToDelete)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.warningColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Text(
                      'Caution',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        secondary: Icon(
          _getPriorityIcon(item.priority),
          color: _getPriorityColor(item.priority),
        ),
      ),
    );
  }

  IconData _getCleanupTypeIcon(CleanupType type) {
    switch (type) {
      case CleanupType.cache:
        return Icons.cached;
      case CleanupType.temporaryFiles:
        return Icons.schedule_send;
      case CleanupType.logs:
        return Icons.article;
      case CleanupType.thumbnails:
        return Icons.image;
      case CleanupType.downloads:
        return Icons.download;
      case CleanupType.emptyFolders:
        return Icons.folder_open;
      case CleanupType.largeFiles:
        return Icons.storage;
      case CleanupType.oldFiles:
        return Icons.access_time;
      case CleanupType.apkFiles:
        return Icons.android;
      case CleanupType.residualFiles:
        return Icons.delete_sweep;
    }
  }

  IconData _getPriorityIcon(CleanupPriority priority) {
    switch (priority) {
      case CleanupPriority.low:
        return Icons.low_priority;
      case CleanupPriority.medium:
        return Icons.priority_high;
      case CleanupPriority.high:
        return Icons.priority_high;
      case CleanupPriority.critical:
        return Icons.warning;
    }
  }

  Color _getPriorityColor(CleanupPriority priority) {
    switch (priority) {
      case CleanupPriority.low:
        return Colors.grey;
      case CleanupPriority.medium:
        return AppTheme.warningColor;
      case CleanupPriority.high:
        return AppTheme.successColor;
      case CleanupPriority.critical:
        return AppTheme.errorColor;
    }
  }
}

class EmptyCleanupState extends StatelessWidget {
  const EmptyCleanupState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cleaning_services,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Analysis Data',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Run a system analysis first to see cleanup opportunities',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              context.read<CleanerBloc>().add(const StartSystemAnalysisEvent());
            },
            icon: const Icon(Icons.analytics),
            label: const Text('Start Analysis'),
          ),
        ],
      ),
    );
  }
}
