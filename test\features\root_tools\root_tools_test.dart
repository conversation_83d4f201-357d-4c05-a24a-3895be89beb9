import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';

import 'package:hd_file_explorer/features/root_tools/domain/entities/root_access.dart';
import 'package:hd_file_explorer/features/root_tools/domain/repositories/root_tools_repository.dart';
import 'package:hd_file_explorer/features/root_tools/domain/usecases/root_access_manager.dart';
import 'package:hd_file_explorer/features/root_tools/domain/usecases/system_manager.dart';
import 'package:hd_file_explorer/features/root_tools/domain/usecases/security_manager.dart';
import 'package:hd_file_explorer/features/root_tools/data/datasources/root_tools_datasource.dart';
import 'package:hd_file_explorer/features/root_tools/data/repositories/root_tools_repository_impl.dart';
import 'package:hd_file_explorer/features/root_tools/presentation/bloc/root_tools_bloc.dart';
import 'package:hd_file_explorer/features/root_tools/presentation/bloc/root_tools_event.dart';
import 'package:hd_file_explorer/features/root_tools/presentation/bloc/root_tools_state.dart';
import 'package:hd_file_explorer/features/root_tools/root_tools.dart';
import 'package:hd_file_explorer/core/error/failures.dart';

import 'root_tools_test.mocks.dart';

@GenerateMocks([
  RootToolsRepository,
  RootToolsDataSource,
])
void main() {
  group('Root Tools Feature Tests', () {
    late MockRootToolsRepository mockRepository;
    late MockRootToolsDataSource mockDataSource;
    late RootAccessManager rootAccessManager;
    late SystemManager systemManager;
    late SecurityManager securityManager;
    late RootToolsBloc rootToolsBloc;

    setUp(() {
      mockRepository = MockRootToolsRepository();
      mockDataSource = MockRootToolsDataSource();
      rootAccessManager = RootAccessManager(mockRepository);
      systemManager = SystemManager(mockRepository);
      securityManager = SecurityManager(mockRepository);
      rootToolsBloc = RootToolsBloc(
        rootAccessManager,
        systemManager,
        securityManager,
      );
    });

    tearDown(() {
      rootToolsBloc.close();
    });

    group('RootAccess Entity Tests', () {
      test('should create RootAccess with correct properties', () {
        // Arrange
        const rootAccess = RootAccess(
          isGranted: true,
          isAvailable: true,
          rootType: RootType.supersu,
          version: '2.82',
          binaryPath: '/system/bin/su',
          lastChecked: null,
        );

        // Assert
        expect(rootAccess.isGranted, true);
        expect(rootAccess.isAvailable, true);
        expect(rootAccess.rootType, RootType.supersu);
        expect(rootAccess.version, '2.82');
        expect(rootAccess.binaryPath, '/system/bin/su');
      });

      test('should support equality comparison', () {
        // Arrange
        const rootAccess1 = RootAccess(
          isGranted: true,
          isAvailable: true,
          rootType: RootType.supersu,
          version: '2.82',
          binaryPath: '/system/bin/su',
          lastChecked: null,
        );

        const rootAccess2 = RootAccess(
          isGranted: true,
          isAvailable: true,
          rootType: RootType.supersu,
          version: '2.82',
          binaryPath: '/system/bin/su',
          lastChecked: null,
        );

        // Assert
        expect(rootAccess1, equals(rootAccess2));
      });
    });

    group('RootAccessManager Tests', () {
      test('should return RootAccess when checkRootAccess succeeds', () async {
        // Arrange
        const tRootAccess = RootAccess(
          isGranted: true,
          isAvailable: true,
          rootType: RootType.supersu,
          version: '2.82',
          binaryPath: '/system/bin/su',
          lastChecked: null,
        );

        when(mockRepository.checkRootAccess())
            .thenAnswer((_) async => const Right(tRootAccess));

        // Act
        final result = await rootAccessManager.checkRootAccess();

        // Assert
        expect(result, const Right(tRootAccess));
        verify(mockRepository.checkRootAccess());
        verifyNoMoreInteractions(mockRepository);
      });

      test('should return Failure when checkRootAccess fails', () async {
        // Arrange
        const tFailure = ServerFailure(message: 'Root check failed');

        when(mockRepository.checkRootAccess())
            .thenAnswer((_) async => const Left(tFailure));

        // Act
        final result = await rootAccessManager.checkRootAccess();

        // Assert
        expect(result, const Left(tFailure));
        verify(mockRepository.checkRootAccess());
        verifyNoMoreInteractions(mockRepository);
      });

      test('should return true when requestRootPermission succeeds', () async {
        // Arrange
        when(mockRepository.requestRootPermission())
            .thenAnswer((_) async => const Right(true));

        // Act
        final result = await rootAccessManager.requestRootPermission();

        // Assert
        expect(result, const Right(true));
        verify(mockRepository.requestRootPermission());
        verifyNoMoreInteractions(mockRepository);
      });

      test('should validate command safety correctly', () {
        // Test safe commands
        expect(rootAccessManager.isCommandSafe('ls -la'), true);
        expect(rootAccessManager.isCommandSafe('cat /proc/version'), true);
        expect(rootAccessManager.isCommandSafe('ps aux'), true);

        // Test dangerous commands
        expect(rootAccessManager.isCommandSafe('rm -rf /'), false);
        expect(rootAccessManager.isCommandSafe('dd if=/dev/zero of=/dev/sda'), false);
        expect(rootAccessManager.isCommandSafe('mkfs.ext4 /dev/sda1'), false);
      });
    });

    group('RootToolsBloc Tests', () {
      test('initial state should be RootToolsInitial', () {
        // Assert
        expect(rootToolsBloc.state, const RootToolsInitial());
      });

      test('should emit [RootAccessLoading, RootAccessLoaded] when CheckRootAccessEvent is added and succeeds', () async {
        // Arrange
        const tRootAccess = RootAccess(
          isGranted: true,
          isAvailable: true,
          rootType: RootType.supersu,
          version: '2.82',
          binaryPath: '/system/bin/su',
          lastChecked: null,
        );

        when(mockRepository.checkRootAccess())
            .thenAnswer((_) async => const Right(tRootAccess));

        // Assert later
        final expected = [
          const RootAccessLoading(),
          const RootAccessLoaded(tRootAccess),
        ];

        expectLater(rootToolsBloc.stream, emitsInOrder(expected));

        // Act
        rootToolsBloc.add(const CheckRootAccessEvent());
      });

      test('should emit [RootAccessLoading, RootAccessError] when CheckRootAccessEvent is added and fails', () async {
        // Arrange
        const tFailure = ServerFailure(message: 'Root check failed');

        when(mockRepository.checkRootAccess())
            .thenAnswer((_) async => const Left(tFailure));

        // Assert later
        final expected = [
          const RootAccessLoading(),
          const RootAccessError('Root check failed'),
        ];

        expectLater(rootToolsBloc.stream, emitsInOrder(expected));

        // Act
        rootToolsBloc.add(const CheckRootAccessEvent());
      });

      test('should emit [RootAccessLoading, RootPermissionGranted] when RequestRootPermissionEvent succeeds', () async {
        // Arrange
        when(mockRepository.requestRootPermission())
            .thenAnswer((_) async => const Right(true));

        // Assert later
        final expected = [
          const RootAccessLoading(),
          const RootPermissionGranted(),
        ];

        expectLater(rootToolsBloc.stream, emitsInOrder(expected));

        // Act
        rootToolsBloc.add(const RequestRootPermissionEvent());
      });

      test('should emit [CommandExecutionLoading, CommandExecutionSuccess] when ExecuteCommandEvent succeeds', () async {
        // Arrange
        const tCommand = 'ls -la';
        final tResult = RootCommandResult(
          command: tCommand,
          exitCode: 0,
          output: 'file1.txt\nfile2.txt',
          error: '',
          executionTime: const Duration(milliseconds: 100),
          timestamp: DateTime.now(),
          isSuccess: true,
        );

        when(mockRepository.executeCommand(tCommand))
            .thenAnswer((_) async => Right(tResult));

        // Assert later
        final expected = [
          const CommandExecutionLoading(tCommand),
          CommandExecutionSuccess(tResult),
        ];

        expectLater(rootToolsBloc.stream, emitsInOrder(expected));

        // Act
        rootToolsBloc.add(const ExecuteCommandEvent(tCommand));
      });

      test('should emit [CommandExecutionError] when ExecuteCommandEvent with dangerous command', () async {
        // Arrange
        const tDangerousCommand = 'rm -rf /';

        // Assert later
        final expected = [
          const CommandExecutionError(
            tDangerousCommand,
            'Command is potentially dangerous and has been blocked',
          ),
        ];

        expectLater(rootToolsBloc.stream, emitsInOrder(expected));

        // Act
        rootToolsBloc.add(const ExecuteCommandEvent(tDangerousCommand));
      });
    });

    group('RootToolsUtils Tests', () {
      test('should correctly identify safe commands', () {
        expect(RootToolsUtils.isCommandSafe('ls -la'), true);
        expect(RootToolsUtils.isCommandSafe('cat /proc/version'), true);
        expect(RootToolsUtils.isCommandSafe('ps aux'), true);
        expect(RootToolsUtils.isCommandSafe('df -h'), true);
        expect(RootToolsUtils.isCommandSafe('mount'), true);
      });

      test('should correctly identify dangerous commands', () {
        expect(RootToolsUtils.isCommandSafe('rm -rf /'), false);
        expect(RootToolsUtils.isCommandSafe('dd if=/dev/zero of=/dev/sda'), false);
        expect(RootToolsUtils.isCommandSafe('mkfs.ext4 /dev/sda1'), false);
        expect(RootToolsUtils.isCommandSafe('fdisk /dev/sda'), false);
        expect(RootToolsUtils.isCommandSafe('parted /dev/sda'), false);
      });

      test('should format file sizes correctly', () {
        expect(RootToolsUtils.formatFileSize(512), '512 B');
        expect(RootToolsUtils.formatFileSize(1024), '1.0 KB');
        expect(RootToolsUtils.formatFileSize(1048576), '1.0 MB');
        expect(RootToolsUtils.formatFileSize(1073741824), '1.0 GB');
      });

      test('should validate file paths correctly', () {
        expect(RootToolsUtils.isValidPath('/system/bin/su'), true);
        expect(RootToolsUtils.isValidPath('/data/app/com.example'), true);
        expect(RootToolsUtils.isValidPath(''), false);
        expect(RootToolsUtils.isValidPath('/system/../etc/passwd'), false);
        expect(RootToolsUtils.isValidPath('/system//bin'), false);
      });

      test('should generate backup filenames correctly', () {
        final filename = RootToolsUtils.generateBackupFilename('system');
        expect(filename, startsWith('system_backup_'));
        expect(filename, endsWith('.tar.gz'));
      });

      test('should parse system info correctly', () {
        const output = '''
Device: Mock Device
Android Version: 13
API Level: 33
Kernel Version: 5.10.0-mock
''';

        final info = RootToolsUtils.parseSystemInfo(output);
        expect(info['Device'], 'Mock Device');
        expect(info['Android Version'], '13');
        expect(info['API Level'], '33');
        expect(info['Kernel Version'], '5.10.0-mock');
      });
    });

    group('RootToolsFeature Tests', () {
      test('should have correct feature information', () {
        expect(RootToolsFeature.name, 'root_tools');
        expect(RootToolsFeature.displayName, 'أدوات الروت');
        expect(RootToolsFeature.version, '1.0.0');
        expect(RootToolsFeature.capabilities.isNotEmpty, true);
        expect(RootToolsFeature.requiredPermissions.isNotEmpty, true);
      });

      test('should have safety warnings', () {
        expect(RootToolsFeature.safetyWarnings.isNotEmpty, true);
        expect(RootToolsFeature.safetyWarnings.length, greaterThan(3));
      });

      test('should have dangerous and safe commands lists', () {
        expect(RootToolsFeature.dangerousCommands.isNotEmpty, true);
        expect(RootToolsFeature.safeCommands.isNotEmpty, true);
        expect(RootToolsFeature.dangerousCommands.contains('rm -rf /'), true);
        expect(RootToolsFeature.safeCommands.contains('ls'), true);
      });

      test('should have minimum requirements', () {
        expect(RootToolsFeature.minimumRequirements['android_version'], 21);
        expect(RootToolsFeature.minimumRequirements['ram_mb'], 1024);
        expect(RootToolsFeature.minimumRequirements['root_access'], true);
      });

      test('should have default settings', () {
        expect(RootToolsFeature.defaultSettings['auto_check_root'], true);
        expect(RootToolsFeature.defaultSettings['show_safety_warnings'], true);
        expect(RootToolsFeature.defaultSettings['command_timeout_seconds'], 30);
      });
    });

    group('Integration Tests', () {
      test('should work end-to-end for root access check', () async {
        // This would be an integration test that tests the entire flow
        // from UI interaction to data layer
        // Implementation would depend on the actual integration test setup
      });
    });
  });
}
