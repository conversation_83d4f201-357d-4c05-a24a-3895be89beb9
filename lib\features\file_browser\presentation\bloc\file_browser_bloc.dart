import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:path/path.dart' as path;

import '../../domain/usecases/get_directory_contents.dart';
import '../../domain/usecases/create_directory.dart';
import '../../domain/usecases/delete_file.dart';
import 'file_browser_event.dart';
import 'file_browser_state.dart';
import '../../../../core/utils/file_utils.dart';

@injectable
class FileBrowserBloc extends Bloc<FileBrowserEvent, FileBrowserState> {
  final GetDirectoryContents _getDirectoryContents;
  final CreateDirectory _createDirectory;
  final DeleteFile _deleteFile;

  FileBrowserBloc(
    this._getDirectoryContents,
    this._createDirectory,
    this._deleteFile,
  ) : super(const FileBrowserInitial()) {
    on<LoadDirectoryEvent>(_onLoadDirectory);
    on<RefreshDirectoryEvent>(_onRefreshDirectory);
    on<NavigateToDirectoryEvent>(_onNavigateToDirectory);
    on<NavigateBackEvent>(_onNavigateBack);
    on<CreateDirectoryEvent>(_onCreateDirectory);
    on<DeleteFileEvent>(_onDeleteFile);
    on<RenameFileEvent>(_onRenameFile);
    on<ToggleHiddenFilesEvent>(_onToggleHiddenFiles);
    on<ChangeSortOrderEvent>(_onChangeSortOrder);
    on<ChangeViewModeEvent>(_onChangeViewMode);
  }

  Future<void> _onLoadDirectory(
    LoadDirectoryEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    emit(const FileBrowserLoading());

    final result = await _getDirectoryContents(event.path);

    result.fold(
      (failure) => emit(FileBrowserError(failure.message)),
      (files) => emit(
        FileBrowserLoaded(
          files: files,
          currentPath: event.path,
          navigationHistory: [event.path],
          canNavigateBack: false,
        ),
      ),
    );
  }

  Future<void> _onRefreshDirectory(
    RefreshDirectoryEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;
      add(LoadDirectoryEvent(currentState.currentPath));
    }
  }

  Future<void> _onNavigateToDirectory(
    NavigateToDirectoryEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;
      emit(const FileBrowserLoading());

      final result = await _getDirectoryContents(event.path);

      result.fold((failure) => emit(FileBrowserError(failure.message)), (
        files,
      ) {
        final newHistory = List<String>.from(currentState.navigationHistory)
          ..add(event.path);

        emit(
          currentState.copyWith(
            files: files,
            currentPath: event.path,
            navigationHistory: newHistory,
            canNavigateBack: newHistory.length > 1,
          ),
        );
      });
    }
  }

  Future<void> _onNavigateBack(
    NavigateBackEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;

      if (currentState.navigationHistory.length > 1) {
        final newHistory = List<String>.from(currentState.navigationHistory)
          ..removeLast();
        final previousPath = newHistory.last;

        emit(const FileBrowserLoading());

        final result = await _getDirectoryContents(previousPath);

        result.fold(
          (failure) => emit(FileBrowserError(failure.message)),
          (files) => emit(
            currentState.copyWith(
              files: files,
              currentPath: previousPath,
              navigationHistory: newHistory,
              canNavigateBack: newHistory.length > 1,
            ),
          ),
        );
      }
    }
  }

  Future<void> _onCreateDirectory(
    CreateDirectoryEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      emit(FileOperationInProgress('Creating directory', event.name));

      final fullPath = path.join(event.path, event.name);
      final result = await _createDirectory(fullPath);

      result.fold((failure) => emit(FileOperationError(failure.message)), (
        success,
      ) {
        if (success) {
          emit(const FileOperationSuccess('Directory created successfully'));
          add(const RefreshDirectoryEvent());
        } else {
          emit(const FileOperationError('Failed to create directory'));
        }
      });
    }
  }

  Future<void> _onDeleteFile(
    DeleteFileEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      final fileName = FileUtils.getFileName(event.path);

      emit(FileOperationInProgress('Deleting', fileName));

      final result = await _deleteFile(event.path);

      result.fold((failure) => emit(FileOperationError(failure.message)), (
        success,
      ) {
        if (success) {
          emit(const FileOperationSuccess('File deleted successfully'));
          add(const RefreshDirectoryEvent());
        } else {
          emit(const FileOperationError('Failed to delete file'));
        }
      });
    }
  }

  Future<void> _onRenameFile(
    RenameFileEvent event,
    Emitter<FileBrowserState> emit,
  ) async {
    if (state is FileBrowserLoaded) {
      emit(FileOperationInProgress('Renaming', event.newName));

      // This would use a rename use case
      // For now, we'll just show success
      emit(const FileOperationSuccess('File renamed successfully'));
      add(const RefreshDirectoryEvent());
    }
  }

  void _onToggleHiddenFiles(
    ToggleHiddenFilesEvent event,
    Emitter<FileBrowserState> emit,
  ) {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;
      emit(
        currentState.copyWith(showHiddenFiles: !currentState.showHiddenFiles),
      );
    }
  }

  void _onChangeSortOrder(
    ChangeSortOrderEvent event,
    Emitter<FileBrowserState> emit,
  ) {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;
      emit(currentState.copyWith(sortOrder: event.sortOrder));
    }
  }

  void _onChangeViewMode(
    ChangeViewModeEvent event,
    Emitter<FileBrowserState> emit,
  ) {
    if (state is FileBrowserLoaded) {
      final currentState = state as FileBrowserLoaded;
      emit(currentState.copyWith(viewMode: event.viewMode));
    }
  }
}
