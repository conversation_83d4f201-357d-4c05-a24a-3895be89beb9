import 'package:equatable/equatable.dart';
import '../../domain/entities/file_operation.dart';

abstract class FileOperationsEvent extends Equatable {
  const FileOperationsEvent();

  @override
  List<Object?> get props => [];
}

class LoadActiveOperationsEvent extends FileOperationsEvent {
  const LoadActiveOperationsEvent();
}

class StartCopyOperationEvent extends FileOperationsEvent {
  final List<String> sourcePaths;
  final String destinationPath;

  const StartCopyOperationEvent({
    required this.sourcePaths,
    required this.destinationPath,
  });

  @override
  List<Object?> get props => [sourcePaths, destinationPath];
}

class StartMoveOperationEvent extends FileOperationsEvent {
  final List<String> sourcePaths;
  final String destinationPath;

  const StartMoveOperationEvent({
    required this.sourcePaths,
    required this.destinationPath,
  });

  @override
  List<Object?> get props => [sourcePaths, destinationPath];
}

class StartDeleteOperationEvent extends FileOperationsEvent {
  final List<String> filePaths;

  const StartDeleteOperationEvent({
    required this.filePaths,
  });

  @override
  List<Object?> get props => [filePaths];
}

class StartCompressionEvent extends FileOperationsEvent {
  final List<String> sourcePaths;
  final String destinationPath;
  final CompressionOptions options;

  const StartCompressionEvent({
    required this.sourcePaths,
    required this.destinationPath,
    required this.options,
  });

  @override
  List<Object?> get props => [sourcePaths, destinationPath, options];
}

class StartExtractionEvent extends FileOperationsEvent {
  final String archivePath;
  final String destinationPath;
  final String? password;

  const StartExtractionEvent({
    required this.archivePath,
    required this.destinationPath,
    this.password,
  });

  @override
  List<Object?> get props => [archivePath, destinationPath, password];
}

class PauseOperationEvent extends FileOperationsEvent {
  final String operationId;

  const PauseOperationEvent(this.operationId);

  @override
  List<Object?> get props => [operationId];
}

class ResumeOperationEvent extends FileOperationsEvent {
  final String operationId;

  const ResumeOperationEvent(this.operationId);

  @override
  List<Object?> get props => [operationId];
}

class CancelOperationEvent extends FileOperationsEvent {
  final String operationId;

  const CancelOperationEvent(this.operationId);

  @override
  List<Object?> get props => [operationId];
}

class CheckConflictsEvent extends FileOperationsEvent {
  final List<String> sourcePaths;
  final String destinationPath;

  const CheckConflictsEvent({
    required this.sourcePaths,
    required this.destinationPath,
  });

  @override
  List<Object?> get props => [sourcePaths, destinationPath];
}

class OperationUpdatedEvent extends FileOperationsEvent {
  final FileOperation operation;

  const OperationUpdatedEvent(this.operation);

  @override
  List<Object?> get props => [operation];
}
