import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/root_access.dart';
import '../../domain/repositories/root_tools_repository.dart';
import '../datasources/root_tools_datasource.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/utils/typedef.dart';

@LazySingleton(as: RootToolsRepository)
class RootToolsRepositoryImpl implements RootToolsRepository {
  final RootToolsDataSource dataSource;

  const RootToolsRepositoryImpl(this.dataSource);

  @override
  ResultFuture<RootAccess> checkRootAccess() async {
    try {
      final result = await dataSource.checkRootAccess();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<bool> requestRootPermission() async {
    try {
      final result = await dataSource.requestRootPermission();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid revokeRootPermission() async {
    try {
      await dataSource.revokeRootPermission();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<String>> getAvailableCommands() async {
    try {
      final result = await dataSource.getAvailableCommands();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<RootCommandResult> executeCommand(String command) async {
    try {
      final result = await dataSource.executeCommand(command);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<RootCommandResult> executeCommandWithTimeout(
    String command,
    Duration timeout,
  ) async {
    try {
      final result = await dataSource.executeCommandWithTimeout(
        command,
        timeout,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<RootCommandResult>> executeBatchCommands(
    List<String> commands,
  ) async {
    try {
      final result = await dataSource.executeBatchCommands(commands);
      return Right(result.cast<RootCommandResult>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Stream<String> executeCommandStream(String command) {
    return dataSource.executeCommandStream(command);
  }

  @override
  ResultFuture<List<SystemPartition>> getSystemPartitions() async {
    try {
      final result = await dataSource.getSystemPartitions();
      return Right(result.cast<SystemPartition>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<SystemProcess>> getRunningProcesses() async {
    try {
      final result = await dataSource.getRunningProcesses();
      return Right(result.cast<SystemProcess>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<SystemService>> getSystemServices() async {
    try {
      final result = await dataSource.getSystemServices();
      return Right(result.cast<SystemService>());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<Map<String, String>> getSystemProperties() async {
    try {
      final result = await dataSource.getSystemProperties();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<Map<String, dynamic>> getSystemInfo() async {
    try {
      final result = await dataSource.getSystemInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid killProcess(int pid) async {
    try {
      await dataSource.killProcess(pid);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid killProcessByName(String processName) async {
    try {
      await dataSource.killProcessByName(processName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<SystemProcess> getProcessInfo(int pid) async {
    try {
      final result = await dataSource.getProcessInfo(pid);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid changeProcessPriority(int pid, int priority) async {
    try {
      await dataSource.changeProcessPriority(pid, priority);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid startService(String serviceName) async {
    try {
      await dataSource.startService(serviceName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid stopService(String serviceName) async {
    try {
      await dataSource.stopService(serviceName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid restartService(String serviceName) async {
    try {
      await dataSource.restartService(serviceName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid enableService(String serviceName) async {
    try {
      await dataSource.enableService(serviceName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid disableService(String serviceName) async {
    try {
      await dataSource.disableService(serviceName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<ServiceState> getServiceState(String serviceName) async {
    try {
      final result = await dataSource.getServiceState(serviceName);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid mountPartition(String partition, String mountPoint) async {
    try {
      await dataSource.mountPartition(partition, mountPoint);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid unmountPartition(String partition) async {
    try {
      await dataSource.unmountPartition(partition);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid remountPartition(String partition, bool readOnly) async {
    try {
      await dataSource.remountPartition(partition, readOnly);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid changeFilePermissions(String path, String permissions) async {
    try {
      await dataSource.changeFilePermissions(path, permissions);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid changeFileOwnership(
    String path,
    String owner,
    String group,
  ) async {
    try {
      await dataSource.changeFileOwnership(path, owner, group);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> getFilePermissions(String path) async {
    try {
      final result = await dataSource.getFilePermissions(path);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<Map<String, String>> getFileOwnership(String path) async {
    try {
      final result = await dataSource.getFileOwnership(path);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Add remaining methods with similar error handling pattern...
  @override
  ResultVoid modifySystemProperty(String property, String value) async {
    try {
      await dataSource.modifySystemProperty(property, value);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid modifyBuildProp(Map<String, String> properties) async {
    try {
      await dataSource.modifyBuildProp(properties);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid installSystemApp(String apkPath) async {
    try {
      await dataSource.installSystemApp(apkPath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid uninstallSystemApp(String packageName) async {
    try {
      await dataSource.uninstallSystemApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid freezeApp(String packageName) async {
    try {
      await dataSource.freezeApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid unfreezeApp(String packageName) async {
    try {
      await dataSource.unfreezeApp(packageName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<SystemBackup> createSystemBackup(
    String name,
    BackupType type,
    List<String> paths,
  ) async {
    try {
      final result = await dataSource.createSystemBackup(name, type, paths);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid restoreSystemBackup(String backupId) async {
    try {
      await dataSource.restoreSystemBackup(backupId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<SystemBackup>> getAvailableBackups() async {
    try {
      final result = await dataSource.getAvailableBackups();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid deleteBackup(String backupId) async {
    try {
      await dataSource.deleteBackup(backupId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<Map<String, dynamic>> getBootInfo() async {
    try {
      final result = await dataSource.getBootInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid flashRecovery(String recoveryPath) async {
    try {
      await dataSource.flashRecovery(recoveryPath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid flashKernel(String kernelPath) async {
    try {
      await dataSource.flashKernel(kernelPath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid rebootToRecovery() async {
    try {
      await dataSource.rebootToRecovery();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid rebootToBootloader() async {
    try {
      await dataSource.rebootToBootloader();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid rebootToDownloadMode() async {
    try {
      await dataSource.rebootToDownloadMode();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Modules
  @override
  ResultFuture<List<String>> getInstalledModules() async {
    try {
      final result = await dataSource.getInstalledModules();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid installModule(String modulePath) async {
    try {
      await dataSource.installModule(modulePath);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid uninstallModule(String moduleId) async {
    try {
      await dataSource.uninstallModule(moduleId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid enableModule(String moduleId) async {
    try {
      await dataSource.enableModule(moduleId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid disableModule(String moduleId) async {
    try {
      await dataSource.disableModule(moduleId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // System Tweaks
  @override
  ResultVoid enableDeveloperOptions() async {
    try {
      await dataSource.enableDeveloperOptions();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid enableAdbDebugging() async {
    try {
      await dataSource.enableAdbDebugging();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid setAnimationScale(double scale) async {
    try {
      await dataSource.setAnimationScale(scale);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid setDpi(int dpi) async {
    try {
      await dataSource.setDpi(dpi);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid setGovernor(String governor) async {
    try {
      await dataSource.setGovernor(governor);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid setCpuFrequency(int minFreq, int maxFreq) async {
    try {
      await dataSource.setCpuFrequency(minFreq, maxFreq);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Security and Privacy
  @override
  ResultVoid removeSystemApps(List<String> packageNames) async {
    try {
      await dataSource.removeSystemApps(packageNames);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid disableSystemApps(List<String> packageNames) async {
    try {
      await dataSource.disableSystemApps(packageNames);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid blockAds() async {
    try {
      await dataSource.blockAds();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid unblockAds() async {
    try {
      await dataSource.unblockAds();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid enableFirewall() async {
    try {
      await dataSource.enableFirewall();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid disableFirewall() async {
    try {
      await dataSource.disableFirewall();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<String>> getFirewallRules() async {
    try {
      final result = await dataSource.getFirewallRules();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid addFirewallRule(String rule) async {
    try {
      await dataSource.addFirewallRule(rule);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid removeFirewallRule(String rule) async {
    try {
      await dataSource.removeFirewallRule(rule);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Performance Optimization
  @override
  ResultVoid clearSystemCache() async {
    try {
      await dataSource.clearSystemCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid optimizeDatabase() async {
    try {
      await dataSource.optimizeDatabase();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid defragmentStorage() async {
    try {
      await dataSource.defragmentStorage();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid trimFilesystem() async {
    try {
      await dataSource.trimFilesystem();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid optimizeMemory() async {
    try {
      await dataSource.optimizeMemory();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Monitoring Streams
  @override
  Stream<Map<String, dynamic>> getSystemStats() {
    return dataSource.getSystemStats();
  }

  @override
  Stream<List<SystemProcess>> getProcessUpdates() {
    return dataSource.getProcessUpdates().map(
      (processes) => processes.cast<SystemProcess>(),
    );
  }

  @override
  Stream<Map<String, double>> getCpuUsage() {
    return dataSource.getCpuUsage();
  }

  @override
  Stream<Map<String, int>> getMemoryUsage() {
    return dataSource.getMemoryUsage();
  }

  @override
  Stream<Map<String, int>> getNetworkUsage() {
    return dataSource.getNetworkUsage();
  }

  // Logs and Diagnostics
  @override
  ResultFuture<List<String>> getSystemLogs() async {
    try {
      final result = await dataSource.getSystemLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<String>> getKernelLogs() async {
    try {
      final result = await dataSource.getKernelLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<List<String>> getApplicationLogs() async {
    try {
      final result = await dataSource.getApplicationLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid clearLogs() async {
    try {
      await dataSource.clearLogs();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<Map<String, dynamic>> runSystemDiagnostics() async {
    try {
      final result = await dataSource.runSystemDiagnostics();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Recovery and Repair
  @override
  ResultVoid fixPermissions() async {
    try {
      await dataSource.fixPermissions();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid rebuildDalvikCache() async {
    try {
      await dataSource.rebuildDalvikCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid clearDalvikCache() async {
    try {
      await dataSource.clearDalvikCache();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid fixBootloop() async {
    try {
      await dataSource.fixBootloop();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid repairFilesystem(String partition) async {
    try {
      await dataSource.repairFilesystem(partition);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // Custom Scripts
  @override
  ResultFuture<List<String>> getAvailableScripts() async {
    try {
      final result = await dataSource.getAvailableScripts();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<RootCommandResult> executeScript(String scriptPath) async {
    try {
      final result = await dataSource.executeScript(scriptPath);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid installScript(String scriptPath, String name) async {
    try {
      await dataSource.installScript(scriptPath, name);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultVoid uninstallScript(String scriptName) async {
    try {
      await dataSource.uninstallScript(scriptName);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  // System Information Export
  @override
  ResultFuture<String> exportSystemInfo() async {
    try {
      final result = await dataSource.exportSystemInfo();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> exportInstalledApps() async {
    try {
      final result = await dataSource.exportInstalledApps();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> exportSystemLogs() async {
    try {
      final result = await dataSource.exportSystemLogs();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  ResultFuture<String> generateSystemReport() async {
    try {
      final result = await dataSource.generateSystemReport();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
