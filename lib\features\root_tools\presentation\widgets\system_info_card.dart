import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/root_tools_bloc.dart';
import '../bloc/root_tools_state.dart';
import '../bloc/root_tools_event.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/animated_card.dart';
import '../../../../core/utils/formatters.dart';

class SystemInfoCard extends StatefulWidget {
  const SystemInfoCard({super.key});

  @override
  State<SystemInfoCard> createState() => _SystemInfoCardState();
}

class _SystemInfoCardState extends State<SystemInfoCard> {
  @override
  void initState() {
    super.initState();
    // Load system info when widget is created
    context.read<RootToolsBloc>().add(const LoadSystemInfoEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RootToolsBloc, RootToolsState>(
      builder: (context, state) {
        return AnimatedCard(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.outline.withOpacity(0.2),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.info,
                        color: AppColors.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'معلومات النظام',
                            style: AppTextStyles.headlineSmall.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'تفاصيل الجهاز والنظام',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        context.read<RootToolsBloc>().add(const LoadSystemInfoEvent());
                      },
                      icon: Icon(
                        Icons.refresh,
                        color: AppColors.primary,
                      ),
                      tooltip: 'تحديث',
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                if (state is SystemInfoLoaded)
                  _buildSystemInfo(state.systemInfo)
                else if (state is SystemOperationLoading)
                  _buildLoadingState()
                else if (state is RootToolsError)
                  _buildErrorState(state.message)
                else
                  _buildEmptyState(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemInfo(Map<String, dynamic> systemInfo) {
    return Column(
      children: [
        // Device Information
        _buildInfoSection(
          title: 'معلومات الجهاز',
          icon: Icons.phone_android,
          items: [
            _buildInfoItem(
              'اسم الجهاز',
              systemInfo['device_name']?.toString() ?? 'غير محدد',
              Icons.devices,
            ),
            _buildInfoItem(
              'إصدار أندرويد',
              systemInfo['android_version']?.toString() ?? 'غير محدد',
              Icons.android,
            ),
            _buildInfoItem(
              'مستوى API',
              systemInfo['api_level']?.toString() ?? 'غير محدد',
              Icons.api,
            ),
            _buildInfoItem(
              'إصدار النواة',
              systemInfo['kernel_version']?.toString() ?? 'غير محدد',
              Icons.memory,
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // Hardware Information
        _buildInfoSection(
          title: 'معلومات الأجهزة',
          icon: Icons.hardware,
          items: [
            _buildInfoItem(
              'معمارية المعالج',
              systemInfo['cpu_architecture']?.toString() ?? 'غير محدد',
              Icons.developer_board,
            ),
            _buildInfoItem(
              'إجمالي الذاكرة',
              _formatBytes(systemInfo['total_ram']),
              Icons.memory,
            ),
            _buildInfoItem(
              'الذاكرة المتاحة',
              _formatBytes(systemInfo['available_ram']),
              Icons.memory,
            ),
            _buildInfoItem(
              'درجة الحرارة',
              '${systemInfo['temperature']?.toString() ?? 'غير محدد'}°C',
              Icons.thermostat,
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // Storage Information
        _buildInfoSection(
          title: 'معلومات التخزين',
          icon: Icons.storage,
          items: [
            _buildInfoItem(
              'إجمالي التخزين',
              _formatBytes(systemInfo['total_storage']),
              Icons.storage,
            ),
            _buildInfoItem(
              'التخزين المتاح',
              _formatBytes(systemInfo['available_storage']),
              Icons.storage,
            ),
            _buildStorageBar(
              systemInfo['total_storage'],
              systemInfo['available_storage'],
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // System Status
        _buildInfoSection(
          title: 'حالة النظام',
          icon: Icons.system_security_update,
          items: [
            _buildInfoItem(
              'مستوى البطارية',
              '${systemInfo['battery_level']?.toString() ?? 'غير محدد'}%',
              Icons.battery_full,
            ),
            _buildInfoItem(
              'وقت التشغيل',
              _formatUptime(systemInfo['uptime']),
              Icons.access_time,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> items,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.outline.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items,
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.textSecondary,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageBar(dynamic totalStorage, dynamic availableStorage) {
    if (totalStorage == null || availableStorage == null) {
      return const SizedBox.shrink();
    }

    final total = totalStorage as int;
    final available = availableStorage as int;
    final used = total - available;
    final usagePercentage = (used / total) * 100;

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المستخدم: ${_formatBytes(used)}',
                style: AppTextStyles.bodySmall,
              ),
              Text(
                '${usagePercentage.toStringAsFixed(1)}%',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: usagePercentage / 100,
            backgroundColor: AppColors.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              usagePercentage > 80 
                  ? Colors.red 
                  : usagePercentage > 60 
                      ? Colors.orange 
                      : AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(40),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل معلومات النظام',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                context.read<RootToolsBloc>().add(const LoadSystemInfoEvent());
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.textSecondary,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'اضغط على تحديث لعرض معلومات النظام',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatBytes(dynamic bytes) {
    if (bytes == null) return 'غير محدد';
    return Formatters.formatBytes(bytes as int);
  }

  String _formatUptime(dynamic uptimeSeconds) {
    if (uptimeSeconds == null) return 'غير محدد';
    
    final uptime = uptimeSeconds as int;
    final days = uptime ~/ 86400;
    final hours = (uptime % 86400) ~/ 3600;
    final minutes = (uptime % 3600) ~/ 60;

    if (days > 0) {
      return '$days يوم، $hours ساعة';
    } else if (hours > 0) {
      return '$hours ساعة، $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }
}
