import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../repositories/file_operations_repository.dart';

@lazySingleton
class CopyFiles {
  final FileOperationsRepository repository;

  const CopyFiles(this.repository);

  ResultFuture<String> call({
    required List<String> sourcePaths,
    required String destinationPath,
  }) async {
    return await repository.copyFiles(sourcePaths, destinationPath);
  }
}
