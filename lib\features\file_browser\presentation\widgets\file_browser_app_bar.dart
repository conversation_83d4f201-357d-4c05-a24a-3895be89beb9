import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/file_browser_bloc.dart';
import '../bloc/file_browser_event.dart';
import '../../../../core/utils/file_utils.dart';

class FileBrowserAppBar extends StatelessWidget {
  final String currentPath;
  final bool canNavigateBack;
  final bool showHiddenFiles;
  final SortOrder sortOrder;
  final ViewMode viewMode;

  const FileBrowserAppBar({
    super.key,
    required this.currentPath,
    required this.canNavigateBack,
    this.showHiddenFiles = false,
    this.sortOrder = SortOrder.nameAsc,
    this.viewMode = ViewMode.list,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: canNavigateBack
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                context.read<FileBrowserBloc>().add(const NavigateBackEvent());
              },
            )
          : null,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('File Browser'),
          if (currentPath.isNotEmpty)
            Text(
              _getDisplayPath(currentPath),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white70,
              ),
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      actions: [
        // View Mode Toggle
        PopupMenuButton<ViewMode>(
          icon: _getViewModeIcon(viewMode),
          onSelected: (ViewMode mode) {
            context.read<FileBrowserBloc>().add(ChangeViewModeEvent(mode));
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: ViewMode.list,
              child: Row(
                children: [
                  Icon(Icons.list),
                  SizedBox(width: 8),
                  Text('List View'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: ViewMode.grid,
              child: Row(
                children: [
                  Icon(Icons.grid_view),
                  SizedBox(width: 8),
                  Text('Grid View'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: ViewMode.details,
              child: Row(
                children: [
                  Icon(Icons.table_rows),
                  SizedBox(width: 8),
                  Text('Details View'),
                ],
              ),
            ),
          ],
        ),
        
        // Sort Menu
        PopupMenuButton<SortOrder>(
          icon: const Icon(Icons.sort),
          onSelected: (SortOrder order) {
            context.read<FileBrowserBloc>().add(ChangeSortOrderEvent(order));
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: SortOrder.nameAsc,
              child: Row(
                children: [
                  Icon(Icons.sort_by_alpha),
                  SizedBox(width: 8),
                  Text('Name (A-Z)'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: SortOrder.nameDesc,
              child: Row(
                children: [
                  Icon(Icons.sort_by_alpha),
                  SizedBox(width: 8),
                  Text('Name (Z-A)'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: SortOrder.sizeAsc,
              child: Row(
                children: [
                  Icon(Icons.data_usage),
                  SizedBox(width: 8),
                  Text('Size (Small to Large)'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: SortOrder.sizeDesc,
              child: Row(
                children: [
                  Icon(Icons.data_usage),
                  SizedBox(width: 8),
                  Text('Size (Large to Small)'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: SortOrder.dateAsc,
              child: Row(
                children: [
                  Icon(Icons.access_time),
                  SizedBox(width: 8),
                  Text('Date (Oldest First)'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: SortOrder.dateDesc,
              child: Row(
                children: [
                  Icon(Icons.access_time),
                  SizedBox(width: 8),
                  Text('Date (Newest First)'),
                ],
              ),
            ),
          ],
        ),
        
        // More Options
        PopupMenuButton<String>(
          onSelected: (String value) {
            switch (value) {
              case 'toggle_hidden':
                context.read<FileBrowserBloc>().add(const ToggleHiddenFilesEvent());
                break;
              case 'refresh':
                context.read<FileBrowserBloc>().add(const RefreshDirectoryEvent());
                break;
              case 'select_all':
                // TODO: Implement select all
                break;
              case 'properties':
                // TODO: Show folder properties
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle_hidden',
              child: Row(
                children: [
                  Icon(showHiddenFiles ? Icons.visibility_off : Icons.visibility),
                  const SizedBox(width: 8),
                  Text(showHiddenFiles ? 'Hide Hidden Files' : 'Show Hidden Files'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'refresh',
              child: Row(
                children: [
                  Icon(Icons.refresh),
                  SizedBox(width: 8),
                  Text('Refresh'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'select_all',
              child: Row(
                children: [
                  Icon(Icons.select_all),
                  SizedBox(width: 8),
                  Text('Select All'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'properties',
              child: Row(
                children: [
                  Icon(Icons.info),
                  SizedBox(width: 8),
                  Text('Properties'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getDisplayPath(String path) {
    if (path == '/storage/emulated/0') return 'Internal Storage';
    if (path.startsWith('/storage/emulated/0/')) {
      return path.replaceFirst('/storage/emulated/0/', '');
    }
    return FileUtils.getFileName(path);
  }

  Icon _getViewModeIcon(ViewMode mode) {
    switch (mode) {
      case ViewMode.list:
        return const Icon(Icons.list);
      case ViewMode.grid:
        return const Icon(Icons.grid_view);
      case ViewMode.details:
        return const Icon(Icons.table_rows);
    }
  }
}
