import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/app_manager_bloc.dart';
import '../bloc/app_manager_event.dart';
import '../bloc/app_manager_state.dart';
import '../../domain/entities/app_info.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class BatchOperationsWidget extends StatelessWidget {
  const BatchOperationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppManagerBloc, AppManagerState>(
      builder: (context, state) {
        if (state is AppsLoaded && state.selectedApps.isNotEmpty) {
          return _buildBatchOperationsDialog(context, state);
        }

        return AlertDialog(
          title: const Text('Batch Operations'),
          content: const Text(
            'Please select apps first to perform batch operations.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBatchOperationsDialog(BuildContext context, AppsLoaded state) {
    return AlertDialog(
      title: Text('Batch Operations (${state.selectedApps.length} apps)'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected apps preview
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  AppConstants.defaultBorderRadius,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Apps:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  ...state.selectedApps.take(5).map((packageName) {
                    final app = state.filteredApps.firstWhere(
                      (app) => app.packageName == packageName,
                      orElse:
                          () => state.apps.firstWhere(
                            (app) => app.packageName == packageName,
                          ),
                    );
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        '• ${app.appName}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    );
                  }),
                  if (state.selectedApps.length > 5)
                    Text(
                      '... and ${state.selectedApps.length - 5} more',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Batch operations
            _buildBatchOperationTile(
              context,
              'Uninstall Apps',
              'Remove selected apps from the device',
              Icons.delete,
              AppTheme.errorColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.uninstall,
                state.selectedApps.toList(),
              ),
            ),

            _buildBatchOperationTile(
              context,
              'Disable Apps',
              'Disable selected apps',
              Icons.block,
              AppTheme.warningColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.disable,
                state.selectedApps.toList(),
              ),
            ),

            _buildBatchOperationTile(
              context,
              'Enable Apps',
              'Enable selected apps',
              Icons.check_circle,
              AppTheme.successColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.enable,
                state.selectedApps.toList(),
              ),
            ),

            _buildBatchOperationTile(
              context,
              'Clear App Data',
              'Clear data for selected apps',
              Icons.clear_all,
              AppTheme.warningColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.clearData,
                state.selectedApps.toList(),
              ),
            ),

            _buildBatchOperationTile(
              context,
              'Clear Cache',
              'Clear cache for selected apps',
              Icons.cleaning_services,
              AppTheme.primaryColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.clearCache,
                state.selectedApps.toList(),
              ),
            ),

            _buildBatchOperationTile(
              context,
              'Backup Apps',
              'Create backup of selected apps',
              Icons.backup,
              AppTheme.successColor,
              () => _confirmBatchOperation(
                context,
                AppBatchOperationType.backup,
                state.selectedApps.toList(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  Widget _buildBatchOperationTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _confirmBatchOperation(
    BuildContext context,
    AppBatchOperationType type,
    List<String> packageNames,
  ) {
    final actionName = _getBatchActionName(type);
    final isDestructive = _isDestructiveAction(type);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Confirm $actionName'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Are you sure you want to $actionName ${packageNames.length} apps?',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                if (isDestructive) ...[
                  const SizedBox(height: AppConstants.defaultPadding),
                  Container(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppConstants.defaultBorderRadius,
                      ),
                      border: Border.all(
                        color: AppTheme.errorColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: AppTheme.errorColor,
                          size: 20,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            'This action cannot be undone!',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color: AppTheme.errorColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close confirmation dialog
                  Navigator.of(context).pop(); // Close batch operations dialog

                  context.read<AppManagerBloc>().add(
                    StartBatchOperationEvent(
                      type: type,
                      packageNames: packageNames,
                    ),
                  );

                  // Show progress dialog
                  _showBatchProgressDialog(context, type, packageNames.length);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDestructive
                          ? AppTheme.errorColor
                          : AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(actionName),
              ),
            ],
          ),
    );
  }

  void _showBatchProgressDialog(
    BuildContext context,
    AppBatchOperationType type,
    int totalApps,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => BlocConsumer<AppManagerBloc, AppManagerState>(
            listener: (context, state) {
              if (state is BatchOperationCompleted) {
                Navigator.of(context).pop(); // Close progress dialog

                // Show completion message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Batch operation completed: ${state.operation.successfulApps} successful, ${state.operation.failedApps} failed',
                    ),
                    backgroundColor:
                        state.operation.failedApps == 0
                            ? AppTheme.successColor
                            : AppTheme.warningColor,
                    duration: const Duration(seconds: 5),
                  ),
                );
              } else if (state is BatchOperationError) {
                Navigator.of(context).pop(); // Close progress dialog

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Batch operation failed: ${state.message}'),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              }
            },
            builder: (context, state) {
              if (state is BatchOperationProgress ||
                  state is BatchOperationStarted) {
                final operation =
                    state is BatchOperationProgress
                        ? state.operation
                        : (state as BatchOperationStarted).operation;

                return AlertDialog(
                  title: Text('${_getBatchActionName(type)} in Progress'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LinearProgressIndicator(
                        value: operation.progress,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        '${operation.processedApps} of ${operation.totalApps} apps processed',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'Successful: ${operation.successfulApps} | Failed: ${operation.failedApps}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        context.read<AppManagerBloc>().add(
                          CancelBatchOperationEvent(operation.id),
                        );
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                  ],
                );
              }

              return AlertDialog(
                title: Text('Starting ${_getBatchActionName(type)}'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: AppConstants.defaultPadding),
                    Text('Preparing batch operation...'),
                  ],
                ),
              );
            },
          ),
    );
  }

  String _getBatchActionName(AppBatchOperationType type) {
    switch (type) {
      case AppBatchOperationType.uninstall:
        return 'Uninstall';
      case AppBatchOperationType.disable:
        return 'Disable';
      case AppBatchOperationType.enable:
        return 'Enable';
      case AppBatchOperationType.clearData:
        return 'Clear Data';
      case AppBatchOperationType.clearCache:
        return 'Clear Cache';
      case AppBatchOperationType.backup:
        return 'Backup';
    }
  }

  bool _isDestructiveAction(AppBatchOperationType type) {
    return type == AppBatchOperationType.uninstall ||
        type == AppBatchOperationType.clearData;
  }
}
