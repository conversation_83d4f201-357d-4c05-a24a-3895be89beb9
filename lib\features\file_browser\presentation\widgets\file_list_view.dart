import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/file_item.dart';
import '../bloc/file_browser_bloc.dart';
import '../bloc/file_browser_event.dart';
import 'file_item_tile.dart';
import '../../../../core/constants/app_constants.dart';

class FileListView extends StatelessWidget {
  final List<FileItem> files;

  const FileListView({
    super.key,
    required this.files,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      itemCount: files.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final file = files[index];
        return FileItemTile(
          file: file,
          onTap: () => _handleFileTap(context, file),
          onLongPress: () => _showFileOptions(context, file),
        );
      },
    );
  }

  void _handleFileTap(BuildContext context, FileItem file) {
    if (file.isDirectory) {
      context.read<FileBrowserBloc>().add(NavigateToDirectoryEvent(file.path));
    } else {
      // Open file with appropriate app
      _openFile(context, file);
    }
  }

  void _openFile(BuildContext context, FileItem file) {
    // TODO: Implement file opening logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${file.name}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showFileOptions(BuildContext context, FileItem file) {
    showModalBottomSheet(
      context: context,
      builder: (context) => FileOptionsBottomSheet(file: file),
    );
  }
}

class FileOptionsBottomSheet extends StatelessWidget {
  final FileItem file;

  const FileOptionsBottomSheet({
    super.key,
    required this.file,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File info header
          Row(
            children: [
              Icon(
                file.isDirectory ? Icons.folder : Icons.insert_drive_file,
                size: 32,
                color: file.isDirectory ? Colors.blue : Colors.grey[600],
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      file.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      file.path,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          const Divider(),
          
          // Options
          ListTile(
            leading: const Icon(Icons.open_in_new),
            title: Text(file.isDirectory ? 'Open' : 'Open with'),
            onTap: () {
              Navigator.pop(context);
              if (file.isDirectory) {
                context.read<FileBrowserBloc>().add(NavigateToDirectoryEvent(file.path));
              } else {
                // Open file
              }
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Rename'),
            onTap: () {
              Navigator.pop(context);
              _showRenameDialog(context, file);
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.copy),
            title: const Text('Copy'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement copy
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.cut),
            title: const Text('Cut'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement cut
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement share
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('Properties'),
            onTap: () {
              Navigator.pop(context);
              _showPropertiesDialog(context, file);
            },
          ),
          
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _showDeleteConfirmation(context, file);
            },
          ),
        ],
      ),
    );
  }

  void _showRenameDialog(BuildContext context, FileItem file) {
    final controller = TextEditingController(text: file.name);
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Rename'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'New name',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty && 
                  controller.text.trim() != file.name) {
                context.read<FileBrowserBloc>().add(
                  RenameFileEvent(file.path, controller.text.trim()),
                );
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _showPropertiesDialog(BuildContext context, FileItem file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Properties'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPropertyRow('Name', file.name),
            _buildPropertyRow('Path', file.path),
            _buildPropertyRow('Size', file.isDirectory ? 'Directory' : '${file.size} bytes'),
            _buildPropertyRow('Modified', file.modifiedDate.toString()),
            _buildPropertyRow('Permissions', file.permissions.permissionString),
            if (file.mimeType != null)
              _buildPropertyRow('Type', file.mimeType!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, FileItem file) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete'),
        content: Text(
          'Are you sure you want to delete "${file.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () {
              context.read<FileBrowserBloc>().add(DeleteFileEvent(file.path));
              Navigator.of(dialogContext).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
