import 'package:injectable/injectable.dart';
import '../../../../core/utils/typedef.dart';
import '../entities/app_info.dart';
import '../repositories/app_manager_repository.dart';

@lazySingleton
class BatchOperations {
  final AppManagerRepository repository;

  const BatchOperations(this.repository);

  ResultFuture<AppBatchOperation> startBatchOperation(
    AppBatchOperationType type,
    List<String> packageNames,
  ) async {
    return await repository.startBatchOperation(type, packageNames);
  }

  Stream<AppBatchOperation> getBatchOperationStream(String operationId) {
    return repository.getBatchOperationStream(operationId);
  }

  ResultVoid cancelBatchOperation(String operationId) async {
    return await repository.cancelBatchOperation(operationId);
  }
}
