import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/localization/models/language.dart';
import 'core/localization/bloc/language_bloc.dart';
import 'core/localization/bloc/language_event.dart';
import 'core/localization/bloc/language_state.dart';
import 'core/localization/extensions/translation_extensions.dart';
import 'features/dashboard/presentation/pages/dashboard_page.dart';
import 'features/file_browser/presentation/pages/file_browser_page.dart';
import 'features/file_operations/presentation/pages/file_operations_page.dart';
import 'features/search/presentation/pages/search_page.dart';
import 'features/cleaner/presentation/pages/cleaner_page.dart';

class HDFileExplorerApp extends StatelessWidget {
  const HDFileExplorerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LanguageBloc>(
          create:
              (context) =>
                  GetIt.instance<LanguageBloc>()
                    ..add(const LoadSavedLanguageEvent()),
        ),
      ],
      child: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, state) {
          Language currentLanguage = SupportedLanguages.defaultLanguage;

          if (state is LanguageLoaded) {
            currentLanguage = state.language;
          } else if (state is LanguageChanged) {
            currentLanguage = state.currentLanguage;
          }

          return MaterialApp(
            title: AppConstants.appName,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            debugShowCheckedModeBanner: false,

            // Localization support
            locale: currentLanguage.locale,
            supportedLocales: SupportedLanguages.supportedLocales,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // RTL support
            builder: (context, child) {
              return Directionality(
                textDirection:
                    currentLanguage.isRTL
                        ? TextDirection.rtl
                        : TextDirection.ltr,
                child: child!,
              );
            },

            home: const MainNavigationPage(),
          );
        },
      ),
    );
  }
}

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const DashboardPage(),
    const FileBrowserPage(),
    const SearchPage(),
    const FileOperationsPage(),
    const CleanerPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _pages),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard),
            label: context.tr('navigation.dashboard'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.folder),
            label: context.tr('navigation.file_browser'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.search),
            label: context.tr('common.search'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: context.tr('common.settings'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.cleaning_services),
            label: context.tr('navigation.cleaner'),
          ),
        ],
      ),
    );
  }
}

class PlaceholderPage extends StatelessWidget {
  final String title;

  const PlaceholderPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.construction, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              context.tr('common.feature_title', params: {'title': title}),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              context.tr('common.coming_soon'),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }
}
